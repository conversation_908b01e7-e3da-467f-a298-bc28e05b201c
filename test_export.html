<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>导出功能测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        button { padding: 10px 15px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .result { margin-top: 10px; padding: 10px; background: #f8f9fa; border-radius: 3px; }
    </style>
</head>
<body>
    <h1>导出功能测试</h1>
    
    <div class="test-section">
        <h2>测试1: 检查完成度功能</h2>
        <button onclick="testCompleteness()">测试完成度检查</button>
        <div id="completenessResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>测试2: 模拟导出警告</h2>
        <button onclick="testExportWarning()">测试导出警告</button>
        <div id="exportResult" class="result"></div>
    </div>

    <script>
        // 模拟评估系统的核心功能
        class TestEvaluationSystem {
            constructor() {
                this.toolMappings = {
                    'Method_A': { displayName: '方法 A (EvoSuite)', actualTool: 'EvoSuite' },
                    'Method_B': { displayName: '方法 B (ChatUniTest)', actualTool: 'ChatUniTest' },
                    'Method_C': { displayName: '方法 C (HITS)', actualTool: 'HITS' },
                    'Method_D': { displayName: '方法 D (TestAgent)', actualTool: 'TestAgent' }
                };
                
                // 模拟测试方法数据
                this.testMethods = [
                    { id: 'method1', fullName: 'TestMethod1', methodName: 'testMethod1' },
                    { id: 'method2', fullName: 'TestMethod2', methodName: 'testMethod2' },
                    { id: 'method3', fullName: 'TestMethod3', methodName: 'testMethod3' }
                ];
                
                // 模拟部分完成的评估数据
                this.evaluationData = {
                    'method1': {
                        'Method_A': { criterion1: 4, criterion2: 3, criterion3: 5, criterion4: 4 }, // 完整
                        'Method_B': { criterion1: 3, criterion2: 4 }, // 不完整
                        'Method_C': { criterion1: 5, criterion2: 4, criterion3: 3, criterion4: 5 }, // 完整
                        // Method_D 缺失
                    },
                    'method2': {
                        'Method_A': { criterion1: 2, criterion2: 3, criterion3: 4, criterion4: 3 }, // 完整
                        // 其他方法都缺失
                    }
                    // method3 完全缺失
                };
            }
            
            checkEvaluationCompleteness() {
                const incomplete = [];
                const tools = ['Method_A', 'Method_B', 'Method_C', 'Method_D'];
                
                this.testMethods.forEach((method, index) => {
                    const methodData = this.evaluationData[method.id];
                    const methodIncomplete = [];
                    
                    if (!methodData) {
                        methodIncomplete.push('所有工具方法');
                    } else {
                        tools.forEach(tool => {
                            const toolData = methodData[tool];
                            const toolName = this.toolMappings[tool] ? this.toolMappings[tool].actualTool : tool;
                            
                            if (!toolData || Object.keys(toolData).length < 4) {
                                methodIncomplete.push(`${toolName}`);
                            }
                        });
                    }
                    
                    if (methodIncomplete.length > 0) {
                        incomplete.push({
                            methodIndex: index + 1,
                            methodName: method.fullName || method.methodName || method.id,
                            missingTools: methodIncomplete
                        });
                    }
                });
                
                return incomplete;
            }
            
            generateExportWarning(incompleteItems) {
                if (incompleteItems.length === 0) return null;
                
                let warningMessage = '⚠️ 检测到以下内容尚未完成评估：\n\n';
                
                incompleteItems.forEach(item => {
                    warningMessage += `📋 测试方法 ${item.methodIndex}: ${item.methodName}\n`;
                    warningMessage += `   未完成的工具: ${item.missingTools.join(', ')}\n\n`;
                });
                
                warningMessage += `总计：${incompleteItems.length} 个测试方法存在未完成的评估\n\n`;
                warningMessage += '建议：\n';
                warningMessage += '• 点击"取消"返回继续完成评估\n';
                warningMessage += '• 点击"确定"导出当前不完整的结果\n\n';
                warningMessage += '是否确定要导出不完整的评估结果？';
                
                return warningMessage;
            }
        }
        
        const testSystem = new TestEvaluationSystem();
        
        function testCompleteness() {
            const incomplete = testSystem.checkEvaluationCompleteness();
            const result = document.getElementById('completenessResult');
            
            result.innerHTML = `
                <h3>完成度检查结果：</h3>
                <p><strong>未完成项目数量：</strong> ${incomplete.length}</p>
                <pre>${JSON.stringify(incomplete, null, 2)}</pre>
            `;
        }
        
        function testExportWarning() {
            const incomplete = testSystem.checkEvaluationCompleteness();
            const warning = testSystem.generateExportWarning(incomplete);
            const result = document.getElementById('exportResult');
            
            if (warning) {
                result.innerHTML = `
                    <h3>导出警告消息：</h3>
                    <pre style="white-space: pre-wrap; background: #fff3cd; padding: 10px; border: 1px solid #ffeaa7; border-radius: 3px;">${warning}</pre>
                `;
            } else {
                result.innerHTML = '<p style="color: green;">✅ 所有评估都已完成，可以安全导出！</p>';
            }
        }
    </script>
</body>
</html>
