<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据加载测试</title>
</head>
<body>
    <h1>数据加载测试</h1>
    <div id="output"></div>

    <script>
        // 检查实际的数据结构
        console.log('TEST_METHODS_DATA 存在:', !!window.TEST_METHODS_DATA);
        console.log('TEST_CASES_DATA 存在:', !!window.TEST_CASES_DATA);

        if (window.TEST_CASES_DATA) {
            console.log('TEST_CASES_DATA 的工具:', Object.keys(window.TEST_CASES_DATA));

            // 检查第一个工具的方法
            const firstTool = Object.keys(window.TEST_CASES_DATA)[0];
            if (firstTool) {
                console.log(`${firstTool} 的方法:`, Object.keys(window.TEST_CASES_DATA[firstTool]).slice(0, 5));
            }
        }

        // 测试数据加载逻辑
        function testDataLoading() {
            const output = document.getElementById('output');
            let html = '<h2>实际数据结构分析:</h2>';

            // 测试方法数据
            html += '<h3>测试方法数据:</h3>';
            if (window.TEST_METHODS_DATA && Array.isArray(window.TEST_METHODS_DATA)) {
                html += `<p>✅ 找到 ${window.TEST_METHODS_DATA.length} 个测试方法</p>`;
                html += '<ul>';
                window.TEST_METHODS_DATA.slice(0, 5).forEach(method => {
                    html += `<li>${method.id} (${method.project}.${method.methodName})</li>`;
                });
                if (window.TEST_METHODS_DATA.length > 5) {
                    html += `<li>... 还有 ${window.TEST_METHODS_DATA.length - 5} 个方法</li>`;
                }
                html += '</ul>';
            } else {
                html += '<p>❌ 未找到测试方法数据</p>';
            }

            // 测试用例数据
            html += '<h3>测试用例数据:</h3>';
            if (window.TEST_CASES_DATA && typeof window.TEST_CASES_DATA === 'object') {
                const tools = Object.keys(window.TEST_CASES_DATA);
                html += `<p>✅ 找到 ${tools.length} 个工具: ${tools.join(', ')}</p>`;

                // 检查每个工具的方法数量
                tools.forEach(toolName => {
                    const toolData = window.TEST_CASES_DATA[toolName];
                    const methodCount = Object.keys(toolData).length;
                    html += `<p>${toolName}: ${methodCount} 个方法</p>`;
                });

                // 模拟数据加载逻辑
                const testCaseFiles = {};
                for (const toolName of tools) {
                    const toolData = window.TEST_CASES_DATA[toolName];
                    for (const methodId of Object.keys(toolData)) {
                        if (!testCaseFiles[methodId]) {
                            testCaseFiles[methodId] = {};
                        }
                        testCaseFiles[methodId][toolName] = toolData[methodId];
                    }
                }

                html += `<p>✅ 处理后的数据包含 ${Object.keys(testCaseFiles).length} 个方法</p>`;

                // 显示前几个方法的工具分布
                html += '<h4>方法的工具分布（前5个）:</h4>';
                html += '<ul>';
                const methodIds = Object.keys(testCaseFiles).slice(0, 5);
                for (const methodId of methodIds) {
                    const availableTools = Object.keys(testCaseFiles[methodId]);
                    html += `<li>${methodId}: ${availableTools.join(', ')}</li>`;
                }
                html += '</ul>';

                // 测试具体的数据访问
                html += '<h4>数据访问测试:</h4>';
                const testMethod = Object.keys(testCaseFiles)[0]; // 使用第一个可用方法
                const testTool = tools[0]; // 使用第一个可用工具
                if (testCaseFiles[testMethod] && testCaseFiles[testMethod][testTool]) {
                    html += `<p>✅ 成功访问 ${testMethod} 的 ${testTool} 数据</p>`;
                    const content = testCaseFiles[testMethod][testTool];
                    html += `<p>内容长度: ${content.length} 字符</p>`;
                    html += `<pre style="max-height: 200px; overflow-y: auto; background: #f5f5f5; padding: 10px;">${content.substring(0, 500)}...</pre>`;
                } else {
                    html += `<p>❌ 无法访问 ${testMethod} 的 ${testTool} 数据</p>`;
                }

            } else {
                html += '<p>❌ 未找到测试用例数据</p>';
            }

            output.innerHTML = html;
        }
        
        // 页面加载完成后执行测试
        window.addEventListener('load', testDataLoading);
    </script>
</body>
</html>
