#!/usr/bin/env python3
"""
生成测试用例评估系统的数据
将新的HITS方法添加到现有的99个方法中，使总数达到100个
"""

import os
import json
import re

def read_java_file(filepath):
    """读取Java文件内容"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            return f.read()
    except Exception as e:
        print(f"Error reading {filepath}: {e}")
        return None

def extract_method_info_from_filename(filename):
    """从文件名提取方法信息"""
    # 移除.java扩展名
    name = filename.replace('.java', '')
    
    # 尝试分割项目名和方法名
    if '_' in name:
        parts = name.split('_', 1)
        project = parts[0]
        method_name = parts[1]
        return {
            'id': name,
            'project': project,
            'methodName': method_name,
            'fullName': name
        }
    else:
        return {
            'id': name,
            'project': 'unknown',
            'methodName': name,
            'fullName': name
        }

def scan_test_methods():
    """扫描所有测试方法"""
    test_methods = []
    test_cases = {
        'Method_A': {},  # EvoSuite
        'Method_B': {},  # ChatUniTest
        'Method_C': {},  # HITS
        'Method_D': {}   # TestAgent
    }
    
    # 工具映射
    tool_mapping = {
        'EvoSuite_Test': 'Method_A',
        'ChatUniTest_Test': 'Method_B', 
        'HITS_Test': 'Method_C',
        'TestAgent_Test': 'Method_D'
    }
    
    # 扫描每个工具目录
    for tool_dir, method_key in tool_mapping.items():
        tool_path = f'test_resource/{tool_dir}'
        if os.path.exists(tool_path):
            for filename in os.listdir(tool_path):
                if filename.endswith('.java'):
                    filepath = os.path.join(tool_path, filename)
                    content = read_java_file(filepath)
                    if content:
                        method_info = extract_method_info_from_filename(filename)
                        
                        # 只添加到方法列表一次（使用第一个遇到的工具）
                        if not any(m['id'] == method_info['id'] for m in test_methods):
                            test_methods.append(method_info)
                        
                        # 添加到测试用例数据
                        test_cases[method_key][method_info['id']] = content
    
    return test_methods, test_cases

def main():
    print("正在扫描测试方法...")
    all_test_methods, all_test_cases = scan_test_methods()

    print(f"找到 {len(all_test_methods)} 个测试方法")

    # 统计每个工具的方法数量
    for tool, cases in all_test_cases.items():
        print(f"{tool}: {len(cases)} 个方法")

    # 检查是否包含新添加的方法
    new_method = 'gson_setLongSerializationPolicy'
    new_method_found = any(m['id'] == new_method for m in all_test_methods)
    if new_method_found:
        print(f"✅ 新方法 {new_method} 已包含在数据中")
    else:
        print(f"❌ 新方法 {new_method} 未找到")

    # 限制为100个方法
    # 确保新方法包含在前100个中
    if new_method_found:
        # 将新方法移到列表前面
        new_method_obj = next(m for m in all_test_methods if m['id'] == new_method)
        other_methods = [m for m in all_test_methods if m['id'] != new_method]
        test_methods = [new_method_obj] + other_methods[:99]
    else:
        test_methods = all_test_methods[:100]

    print(f"选择了前 {len(test_methods)} 个方法用于评估系统")

    # 过滤测试用例数据，只保留选中的方法
    selected_method_ids = {m['id'] for m in test_methods}
    test_cases = {}
    for tool, cases in all_test_cases.items():
        test_cases[tool] = {mid: content for mid, content in cases.items() if mid in selected_method_ids}

    # 验证过滤后的数据
    print(f"过滤后的数据:")
    for tool, cases in test_cases.items():
        print(f"{tool}: {len(cases)} 个方法")

    # 生成JavaScript数据
    methods_js = json.dumps(test_methods, ensure_ascii=False, separators=(',', ':'))
    cases_js = json.dumps(test_cases, ensure_ascii=False, separators=(',', ':'))
    
    print(f"\n生成的数据:")
    print(f"TEST_METHODS_DATA 长度: {len(test_methods)}")
    print(f"TEST_CASES_DATA 工具数量: {len(test_cases)}")
    
    # 保存到文件
    with open('generated_data.js', 'w', encoding='utf-8') as f:
        f.write(f"window.TEST_METHODS_DATA = {methods_js};\n")
        f.write(f"window.TEST_CASES_DATA = {cases_js};\n")
    
    print("数据已保存到 generated_data.js")
    
    return test_methods, test_cases

if __name__ == '__main__':
    main()
