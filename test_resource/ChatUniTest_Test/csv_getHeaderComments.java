package org.apache.commons.csv;

import org.junit.Test;
import static org.junit.Assert.*;

public class CSVFormatTest {
  
    @Test
    public void testGetHeaderCommentsNull() {
        CSVFormat format = CSVFormat.DEFAULT.builder().setHeaderComments((Object[]) null).build();
        assertNull("Header comments should be null when disabled", format.getHeaderComments());
    }
  
    @Test
    public void testGetHeaderCommentsEmpty() {
        CSVFormat format = CSVFormat.DEFAULT.builder().setHeaderComments().build();
        String[] headerComments = format.getHeaderComments();
        assertNotNull("Header comments should not be null when initialized empty", headerComments);
        assertEquals("Header comments should be an empty array when initialized empty", 0, headerComments.length);
    }
    
    @Test
    public void testGetHeaderCommentsRegular() {
        CSVFormat format = CSVFormat.DEFAULT.builder().setHeaderComments("Generated by Apache Commons CSV.", "Date: 2023-10-21").build();
        String[] headerComments = format.getHeaderComments();
        assertNotNull("Header comments should not be null when defined", headerComments);
        assertEquals("Header comments should have the correct length", 2, headerComments.length);
        assertEquals("First header comment should be 'Generated by Apache Commons CSV.'", "Generated by Apache Commons CSV.", headerComments[0]);
        assertEquals("Second header comment should be 'Date: 2023-10-21'", "Date: 2023-10-21", headerComments[1]);
    }
    
    @Test
    public void testGetHeaderCommentsClone() {
        String[] commentsArray = {"Generated by Apache Commons CSV.", "Date: 2023-10-21"};
        CSVFormat format = CSVFormat.DEFAULT.builder().setHeaderComments((Object[]) commentsArray).build();
        String[] headerComments = format.getHeaderComments();
        assertNotSame("Clone of header comments should not be the same instance as the original", commentsArray, headerComments);
        assertArrayEquals("Clone of header comments should be equal to the original array", commentsArray, headerComments);
    }
}