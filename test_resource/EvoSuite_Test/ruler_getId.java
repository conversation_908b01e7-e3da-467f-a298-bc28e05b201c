/*
 * This file was automatically generated by EvoSuite
 * Wed Jun 18 09:01:46 GMT 2025
 */

package software.amazon.event.ruler;

import org.junit.Test;
import static org.junit.Assert.*;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.junit.runner.RunWith;
import software.amazon.event.ruler.SubRuleContext;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true) 
public class SubRuleContext_ESTest extends SubRuleContext_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test0()  throws Throwable  {
      SubRuleContext.Generator subRuleContext_Generator0 = new SubRuleContext.Generator();
      Object object0 = new Object();
      SubRuleContext subRuleContext0 = subRuleContext_Generator0.generate(object0);
      SubRuleContext subRuleContext1 = subRuleContext_Generator0.generate(subRuleContext0);
      SubRuleContext subRuleContext2 = subRuleContext_Generator0.generate(subRuleContext1);
      assertEquals((-1.7976931348623153E308), subRuleContext2.getId(), 0.01);
      assertFalse(subRuleContext1.equals((Object)subRuleContext0));
  }

  @Test(timeout = 4000)
  public void test1()  throws Throwable  {
      SubRuleContext.Generator subRuleContext_Generator0 = new SubRuleContext.Generator();
      Object object0 = new Object();
      SubRuleContext subRuleContext0 = subRuleContext_Generator0.generate(object0);
      subRuleContext_Generator0.generate(subRuleContext0);
      SubRuleContext subRuleContext1 = subRuleContext_Generator0.generate(subRuleContext0);
      assertEquals((-1.7976931348623153E308), subRuleContext1.getId(), 0.01);
  }

  @Test(timeout = 4000)
  public void test2()  throws Throwable  {
      SubRuleContext.Generator subRuleContext_Generator0 = new SubRuleContext.Generator();
      Object object0 = new Object();
      SubRuleContext subRuleContext0 = subRuleContext_Generator0.generate(object0);
      SubRuleContext subRuleContext1 = subRuleContext_Generator0.generate(subRuleContext0);
      boolean boolean0 = subRuleContext1.equals(subRuleContext0);
      assertFalse(subRuleContext0.equals((Object)subRuleContext1));
      assertEquals((-1.7976931348623155E308), subRuleContext1.getId(), 0.01);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test3()  throws Throwable  {
      SubRuleContext.Generator subRuleContext_Generator0 = new SubRuleContext.Generator();
      Object object0 = new Object();
      SubRuleContext subRuleContext0 = subRuleContext_Generator0.generate(object0);
      boolean boolean0 = subRuleContext0.equals(object0);
      assertFalse(boolean0);
      assertEquals((-1.7976931348623157E308), subRuleContext0.getId(), 0.01);
  }

  @Test(timeout = 4000)
  public void test4()  throws Throwable  {
      SubRuleContext.Generator subRuleContext_Generator0 = new SubRuleContext.Generator();
      Object object0 = new Object();
      SubRuleContext subRuleContext0 = subRuleContext_Generator0.generate(object0);
      boolean boolean0 = subRuleContext0.equals(subRuleContext0);
      assertTrue(boolean0);
      assertEquals((-1.7976931348623157E308), subRuleContext0.getId(), 0.01);
  }

  @Test(timeout = 4000)
  public void test5()  throws Throwable  {
      SubRuleContext.Generator subRuleContext_Generator0 = new SubRuleContext.Generator();
      Object object0 = new Object();
      SubRuleContext subRuleContext0 = subRuleContext_Generator0.generate(object0);
      boolean boolean0 = subRuleContext0.equals((Object) null);
      assertFalse(boolean0);
      assertEquals((-1.7976931348623157E308), subRuleContext0.getId(), 0.01);
  }

  @Test(timeout = 4000)
  public void test6()  throws Throwable  {
      SubRuleContext.Generator subRuleContext_Generator0 = new SubRuleContext.Generator();
      Double double0 = new Double((-1.7976931348623153E308));
      Object object0 = subRuleContext_Generator0.getNameForGeneratedId(double0);
      assertNull(object0);
  }

  @Test(timeout = 4000)
  public void test7()  throws Throwable  {
      SubRuleContext.Generator subRuleContext_Generator0 = new SubRuleContext.Generator();
      Object object0 = new Object();
      SubRuleContext subRuleContext0 = subRuleContext_Generator0.generate(object0);
      subRuleContext_Generator0.getIdsGeneratedForName(subRuleContext0);
      assertEquals((-1.7976931348623157E308), subRuleContext0.getId(), 0.01);
  }

  @Test(timeout = 4000)
  public void test8()  throws Throwable  {
      SubRuleContext.Generator subRuleContext_Generator0 = new SubRuleContext.Generator();
      Object object0 = new Object();
      SubRuleContext subRuleContext0 = subRuleContext_Generator0.generate(object0);
      double double0 = subRuleContext0.getId();
      assertEquals((-1.7976931348623157E308), double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test9()  throws Throwable  {
      SubRuleContext.Generator subRuleContext_Generator0 = new SubRuleContext.Generator();
      Object object0 = new Object();
      SubRuleContext subRuleContext0 = subRuleContext_Generator0.generate(object0);
      subRuleContext0.hashCode();
      assertEquals((-1.7976931348623157E308), subRuleContext0.getId(), 0.01);
  }
}
