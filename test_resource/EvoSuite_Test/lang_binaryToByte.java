/*
 * This file was automatically generated by EvoSuite
 * Wed Jun 18 09:08:46 GMT 2025
 */

package org.apache.commons.lang3;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.util.Arrays;
import java.util.UUID;
import org.apache.commons.lang3.Conversion;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.evosuite.runtime.mock.java.util.MockUUID;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true) 
public class Conversion_ESTest extends Conversion_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test000()  throws Throwable  {
      UUID uUID0 = MockUUID.randomUUID();
      byte[] byteArray0 = new byte[8];
      // Undeclared exception!
      try { 
        Conversion.uuidToByteArray(uUID0, byteArray0, 1, 16);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 8
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test001()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.shortToHex((short) (-594), (short)0, "", (byte)1, (byte)1);
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
         //
         // String index out of range: 1
         //
         verifyException("java.lang.AbstractStringBuilder", e);
      }
  }

  @Test(timeout = 4000)
  public void test002()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.intToHex(2401, 1, "RU4STj00C$@.IDr", 102, 1);
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
         //
         // String index out of range: 102
         //
         verifyException("java.lang.AbstractStringBuilder", e);
      }
  }

  @Test(timeout = 4000)
  public void test003()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.longToHex((byte) (-97), (-2263), "s]`s6U", 81, (byte)81);
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
         //
         // String index out of range: 81
         //
         verifyException("java.lang.AbstractStringBuilder", e);
      }
  }

  @Test(timeout = 4000)
  public void test004()  throws Throwable  {
      byte[] byteArray0 = new byte[9];
      // Undeclared exception!
      try { 
        Conversion.shortToByteArray((short) (-2992), (-17), byteArray0, (byte)94, (byte)1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 94
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test005()  throws Throwable  {
      UUID uUID0 = MockUUID.randomUUID();
      byte[] byteArray0 = new byte[6];
      byte[] byteArray1 = Conversion.uuidToByteArray(uUID0, byteArray0, (byte)1, (byte)1);
      assertArrayEquals(new byte[] {(byte)0, (byte)0, (byte)0, (byte)0, (byte)0, (byte)0}, byteArray1);
  }

  @Test(timeout = 4000)
  public void test006()  throws Throwable  {
      short[] shortArray0 = new short[2];
      // Undeclared exception!
      try { 
        Conversion.intToShortArray((-297), (-297), shortArray0, 3181, (short)10);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 3181
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test007()  throws Throwable  {
      short[] shortArray0 = new short[7];
      // Undeclared exception!
      try { 
        Conversion.longToShortArray((-641), (-641), shortArray0, (-1205), 1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -1205
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test008()  throws Throwable  {
      int[] intArray0 = new int[6];
      // Undeclared exception!
      try { 
        Conversion.longToIntArray(1631L, (-2660), intArray0, (-667), 49);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -667
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test009()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      // Undeclared exception!
      try { 
        Conversion.binaryToShort(booleanArray0, 108, (short)86, (byte)1, (short)86);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+dstPos is greater or equal to than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test010()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      // Undeclared exception!
      try { 
        Conversion.byteArrayToInt(byteArray0, (-342), (-342), (-342), 86);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nBytes-1)*8+dstPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test011()  throws Throwable  {
      short[] shortArray0 = new short[0];
      // Undeclared exception!
      try { 
        Conversion.shortArrayToInt(shortArray0, (-1), (-2877), 9, 90);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nShorts-1)*16+dstPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test012()  throws Throwable  {
      short[] shortArray0 = new short[0];
      // Undeclared exception!
      try { 
        Conversion.shortArrayToLong(shortArray0, (-1283), 1303L, 50, 3955);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nShorts-1)*16+dstPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test013()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToInt("0WHE*a?kD~2YFq,DqJ ", 0, 3873, (-3942), 607);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'W' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test014()  throws Throwable  {
      UUID uUID0 = MockUUID.randomUUID();
      byte[] byteArray0 = new byte[0];
      byte[] byteArray1 = Conversion.uuidToByteArray(uUID0, byteArray0, (byte)0, 0);
      assertSame(byteArray0, byteArray1);
  }

  @Test(timeout = 4000)
  public void test015()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      byte[] byteArray1 = Conversion.shortToByteArray((short)93, (-2491), byteArray0, (short)93, (-478));
      assertEquals(0, byteArray1.length);
  }

  @Test(timeout = 4000)
  public void test016()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      boolean[] booleanArray1 = Conversion.shortToBinary((short) (-211), (byte)0, booleanArray0, (byte)0, (byte)0);
      assertSame(booleanArray0, booleanArray1);
  }

  @Test(timeout = 4000)
  public void test017()  throws Throwable  {
      short[] shortArray0 = new short[5];
      long long0 = Conversion.shortArrayToLong(shortArray0, (short) (-912), 0L, 0, (-1065));
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test018()  throws Throwable  {
      short[] shortArray0 = new short[3];
      long long0 = Conversion.shortArrayToLong(shortArray0, 0, 65535L, (short)0, 0);
      assertEquals(65535L, long0);
  }

  @Test(timeout = 4000)
  public void test019()  throws Throwable  {
      short[] shortArray0 = new short[3];
      int int0 = Conversion.shortArrayToInt(shortArray0, (short)1, 246, (short) (-796), (short) (-1921));
      assertEquals(246, int0);
  }

  @Test(timeout = 4000)
  public void test020()  throws Throwable  {
      short[] shortArray0 = new short[0];
      short[] shortArray1 = Conversion.longToShortArray(65535L, (-2795), shortArray0, (-2127), (-2303));
      assertSame(shortArray0, shortArray1);
  }

  @Test(timeout = 4000)
  public void test021()  throws Throwable  {
      int[] intArray0 = new int[0];
      int[] intArray1 = Conversion.longToIntArray((byte)0, (byte)0, intArray0, 7, (-547));
      assertSame(intArray0, intArray1);
  }

  @Test(timeout = 4000)
  public void test022()  throws Throwable  {
      String string0 = Conversion.longToHex((-180L), (-1), "", (-3192), (-1));
      assertEquals("", string0);
  }

  @Test(timeout = 4000)
  public void test023()  throws Throwable  {
      byte[] byteArray0 = new byte[8];
      byte[] byteArray1 = Conversion.longToByteArray((byte)0, (byte)0, byteArray0, (byte)0, (byte)0);
      assertArrayEquals(new byte[] {(byte)0, (byte)0, (byte)0, (byte)0, (byte)0, (byte)0, (byte)0, (byte)0}, byteArray1);
  }

  @Test(timeout = 4000)
  public void test024()  throws Throwable  {
      char char0 = Conversion.intToHexDigit(12);
      assertEquals('c', char0);
  }

  @Test(timeout = 4000)
  public void test025()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      byte[] byteArray1 = Conversion.intToByteArray((-1431), 1, byteArray0, (-4478), (-1431));
      assertSame(byteArray0, byteArray1);
  }

  @Test(timeout = 4000)
  public void test026()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.intToBinary((byte)93, 1, (boolean[]) null, (-653), (-1297));
      assertNull(booleanArray0);
  }

  @Test(timeout = 4000)
  public void test027()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      boolean[] booleanArray1 = Conversion.intToBinary((short)606, (byte) (-54), booleanArray0, 0, (-1494));
      assertTrue(Arrays.equals(new boolean[] {}, booleanArray1));
  }

  @Test(timeout = 4000)
  public void test028()  throws Throwable  {
      int[] intArray0 = new int[5];
      long long0 = Conversion.intArrayToLong(intArray0, 6, (-1283), (-1298), (-1));
      assertEquals((-1283L), long0);
  }

  @Test(timeout = 4000)
  public void test029()  throws Throwable  {
      short short0 = Conversion.hexToShort("", (-480), (short) (-761), (short) (-761), (short) (-761));
      assertEquals((short) (-761), short0);
  }

  @Test(timeout = 4000)
  public void test030()  throws Throwable  {
      long long0 = Conversion.hexToLong("Hs<i#noeXEWcC?([", (byte)49, 2041L, (byte)0, (byte)0);
      assertEquals(2041L, long0);
  }

  @Test(timeout = 4000)
  public void test031()  throws Throwable  {
      long long0 = Conversion.hexToLong("", (-226), (-395), 57, 0);
      assertEquals((-395L), long0);
  }

  @Test(timeout = 4000)
  public void test032()  throws Throwable  {
      int int0 = Conversion.hexToInt("sr=9( F", (-17), 1, 1, (-17));
      assertEquals(1, int0);
  }

  @Test(timeout = 4000)
  public void test033()  throws Throwable  {
      byte byte0 = Conversion.hexToByte("Cannot interpret '", 1, (byte)14, 49, (byte)0);
      assertEquals((byte)14, byte0);
  }

  @Test(timeout = 4000)
  public void test034()  throws Throwable  {
      int int0 = Conversion.hexDigitToInt('0');
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test035()  throws Throwable  {
      String string0 = Conversion.byteToHex((byte)11, 0, "5A/Y7,dE^ga:,]wh.", (short) (-2728), (short)0);
      assertEquals("5A/Y7,dE^ga:,]wh.", string0);
  }

  @Test(timeout = 4000)
  public void test036()  throws Throwable  {
      byte[] byteArray0 = new byte[9];
      long long0 = Conversion.byteArrayToLong(byteArray0, (byte)0, (-120), (byte)0, (-120));
      assertEquals((-120L), long0);
  }

  @Test(timeout = 4000)
  public void test037()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      short short0 = Conversion.binaryToShort(booleanArray0, (byte)0, (short)54, 1, 1);
      assertEquals((short)52, short0);
  }

  @Test(timeout = 4000)
  public void test038()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      long long0 = Conversion.binaryToLong(booleanArray0, 3469, 3469, (-99), (-99));
      assertEquals(3469L, long0);
  }

  @Test(timeout = 4000)
  public void test039()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[7];
      int int0 = Conversion.binaryToInt(booleanArray0, 0, 0, (-2722), 0);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test040()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[2];
      byte byte0 = Conversion.binaryToByte(booleanArray0, 12, (byte) (-36), (byte) (-36), (byte) (-36));
      assertEquals((byte) (-36), byte0);
  }

  @Test(timeout = 4000)
  public void test041()  throws Throwable  {
      byte[] byteArray0 = new byte[9];
      // Undeclared exception!
      try { 
        Conversion.uuidToByteArray((UUID) null, byteArray0, (byte)0, 13);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test042()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.shortToHex((short) (-502), 8, (String) null, (short) (-502), (short) (-502));
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
      }
  }

  @Test(timeout = 4000)
  public void test043()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.shortToBinary((byte)81, (-99), (boolean[]) null, (-179), 1);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test044()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.shortArrayToLong((short[]) null, (byte)0, (byte)0, (-901), 65);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test045()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.shortArrayToInt((short[]) null, 0, 57, 0, 100);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test046()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.longToHex((-82L), 3, "k?75k>e,|^D", (-2), 3);
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
         //
         // String index out of range: -2
         //
         verifyException("java.lang.AbstractStringBuilder", e);
      }
  }

  @Test(timeout = 4000)
  public void test047()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.intToHex((-17), (-3487), (String) null, 85, (-2796));
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
      }
  }

  @Test(timeout = 4000)
  public void test048()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.intArrayToLong((int[]) null, 1953, (-2692L), 1953, (-828));
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test049()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToByte((String) null, 1, (byte) (-60), (-2189), 92);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test050()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.byteToHex((byte)50, (-1), (String) null, (-1), (-1));
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
      }
  }

  @Test(timeout = 4000)
  public void test051()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.byteToBinary((byte)127, (-1529), (boolean[]) null, 95, 95);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test052()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.byteArrayToUuid((byte[]) null, 50);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test053()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.byteArrayToShort((byte[]) null, (byte)110, (short)97, (-6224), 3553);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test054()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.byteArrayToLong((byte[]) null, 472, 472, 8, 1);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test055()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.byteArrayToInt((byte[]) null, 0, 1112, 54, 3695);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test056()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.binaryToShort((boolean[]) null, 0, (byte)0, 1804, (byte)0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test057()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.binaryToLong((boolean[]) null, (-2782), 306L, 14, 154);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test058()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.binaryToInt((boolean[]) null, 0, (byte)0, (-2458), 1642);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test059()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[7];
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, (-19788));
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -19785
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test060()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigitMsb0_4bits((boolean[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test061()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigit((boolean[]) null, 181);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test062()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigit((boolean[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test063()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.binaryToByte((boolean[]) null, (-1), (byte)105, 2074, (-1167));
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test064()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.binaryBeMsb0ToHexDigit((boolean[]) null, (-1));
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test065()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[2];
      // Undeclared exception!
      try { 
        Conversion.binaryBeMsb0ToHexDigit(booleanArray0, (int) (byte)84);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
      }
  }

  @Test(timeout = 4000)
  public void test066()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.binaryBeMsb0ToHexDigit((boolean[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test067()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      // Undeclared exception!
      try { 
        Conversion.binaryBeMsb0ToHexDigit(booleanArray0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot convert an empty array.
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test068()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      byte[] byteArray1 = Conversion.longToByteArray((-1L), (short) (-1371), byteArray0, 0, (short) (-1371));
      assertSame(byteArray0, byteArray1);
  }

  @Test(timeout = 4000)
  public void test069()  throws Throwable  {
      byte[] byteArray0 = new byte[5];
      // Undeclared exception!
      try { 
        Conversion.byteArrayToLong(byteArray0, (byte) (-1), (byte) (-50), (-1726), 1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -1
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test070()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      // Undeclared exception!
      try { 
        Conversion.byteArrayToLong(byteArray0, 1, 1L, 69, 764);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nBytes-1)*8+dstPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test071()  throws Throwable  {
      char char0 = Conversion.intToHexDigit(3);
      assertEquals('3', char0);
  }

  @Test(timeout = 4000)
  public void test072()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[3];
      booleanArray0[1] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0, 0);
      assertEquals('2', char0);
  }

  @Test(timeout = 4000)
  public void test073()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0, (int) (byte)0);
      assertEquals('0', char0);
  }

  @Test(timeout = 4000)
  public void test074()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[4];
      booleanArray0[0] = true;
      booleanArray0[1] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0, 1);
      assertEquals('6', char0);
  }

  @Test(timeout = 4000)
  public void test075()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[9];
      booleanArray0[4] = true;
      booleanArray0[5] = true;
      booleanArray0[7] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0, 1);
      assertEquals('d', char0);
  }

  @Test(timeout = 4000)
  public void test076()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      booleanArray0[3] = true;
      booleanArray0[4] = true;
      booleanArray0[5] = true;
      booleanArray0[6] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0, 1);
      assertEquals('f', char0);
  }

  @Test(timeout = 4000)
  public void test077()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      booleanArray0[3] = true;
      booleanArray0[4] = true;
      booleanArray0[5] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0, 1);
      assertEquals('e', char0);
  }

  @Test(timeout = 4000)
  public void test078()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[4];
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, 0);
      assertEquals('0', char0);
  }

  @Test(timeout = 4000)
  public void test079()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      booleanArray0[2] = true;
      booleanArray0[1] = true;
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, (int) (short)0);
      assertEquals('6', char0);
  }

  @Test(timeout = 4000)
  public void test080()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      booleanArray0[2] = true;
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, (int) (short)0);
      assertEquals('2', char0);
  }

  @Test(timeout = 4000)
  public void test081()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      booleanArray0[0] = true;
      booleanArray0[2] = true;
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, (int) (short)0);
      assertEquals('b', char0);
  }

  @Test(timeout = 4000)
  public void test082()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[7];
      booleanArray0[1] = true;
      booleanArray0[2] = true;
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, 0);
      assertEquals('7', char0);
  }

  @Test(timeout = 4000)
  public void test083()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, 86);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // src.length-srcPos<4: src.length=5, srcPos=86
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test084()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[3];
      booleanArray0[1] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0, 1);
      assertEquals('1', char0);
  }

  @Test(timeout = 4000)
  public void test085()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      booleanArray0[0] = true;
      booleanArray0[1] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0, (int) (byte)0);
      assertEquals('3', char0);
  }

  @Test(timeout = 4000)
  public void test086()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[2];
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigit(booleanArray0, 757);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 757
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test087()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[4];
      booleanArray0[2] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0, 0);
      assertEquals('4', char0);
  }

  @Test(timeout = 4000)
  public void test088()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[4];
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0, 0);
      assertEquals('8', char0);
  }

  @Test(timeout = 4000)
  public void test089()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[4];
      booleanArray0[1] = true;
      booleanArray0[2] = true;
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0, 0);
      assertEquals('e', char0);
  }

  @Test(timeout = 4000)
  public void test090()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[4];
      booleanArray0[2] = true;
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0, 0);
      assertEquals('c', char0);
  }

  @Test(timeout = 4000)
  public void test091()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[3];
      char char0 = Conversion.binaryToHexDigit(booleanArray0, 1);
      assertEquals('0', char0);
  }

  @Test(timeout = 4000)
  public void test092()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigit(booleanArray0, 0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot convert an empty array.
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test093()  throws Throwable  {
      byte[] byteArray0 = new byte[8];
      // Undeclared exception!
      try { 
        Conversion.byteArrayToUuid(byteArray0, 680);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Need at least 16 bytes for UUID
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test094()  throws Throwable  {
      byte[] byteArray0 = new byte[1];
      UUID uUID0 = MockUUID.fromString((String) null);
      // Undeclared exception!
      try { 
        Conversion.uuidToByteArray(uUID0, byteArray0, 78, 746);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBytes is greater than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test095()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      // Undeclared exception!
      try { 
        Conversion.byteToBinary((byte)0, (short)0, booleanArray0, 2044, 3);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 2044
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test096()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[1];
      boolean[] booleanArray1 = Conversion.byteToBinary((byte)14, (byte)14, booleanArray0, (byte)14, (-670));
      assertEquals(1, booleanArray1.length);
  }

  @Test(timeout = 4000)
  public void test097()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      boolean[] booleanArray1 = Conversion.byteToBinary((byte)79, (-2534), booleanArray0, 48, 0);
      assertSame(booleanArray1, booleanArray0);
  }

  @Test(timeout = 4000)
  public void test098()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[6];
      // Undeclared exception!
      try { 
        Conversion.byteToBinary((byte) (-86), (byte) (-86), booleanArray0, (byte) (-86), 62);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -86
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test099()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[6];
      // Undeclared exception!
      try { 
        Conversion.shortToBinary((short) (-1848), (-2772), booleanArray0, 6, 1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 6
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test100()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[6];
      boolean[] booleanArray1 = Conversion.shortToBinary((short) (-1848), (-2779), booleanArray0, 1, 1);
      assertTrue(Arrays.equals(new boolean[] {false, false, false, false, false, false}, booleanArray1));
  }

  @Test(timeout = 4000)
  public void test101()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[1];
      boolean[] booleanArray1 = Conversion.shortToBinary((short) (-966), 53, booleanArray0, (-1), (-3160));
      assertTrue(Arrays.equals(new boolean[] {false}, booleanArray1));
  }

  @Test(timeout = 4000)
  public void test102()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      // Undeclared exception!
      try { 
        Conversion.shortToBinary((short)100, 601, booleanArray0, (short)100, 89);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+srcPos is greater or equal to than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test103()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[7];
      // Undeclared exception!
      try { 
        Conversion.intToBinary(3474, (-2492), booleanArray0, (byte) (-83), 101);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -83
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test104()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      // Undeclared exception!
      try { 
        Conversion.intToBinary((byte) (-9), 3, booleanArray0, 53, 9);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 53
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test105()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      boolean[] booleanArray1 = Conversion.intToBinary(1, (-1573), booleanArray0, 1, (-335));
      assertSame(booleanArray0, booleanArray1);
  }

  @Test(timeout = 4000)
  public void test106()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[4];
      boolean[] booleanArray1 = Conversion.intToBinary(0, 1, booleanArray0, 2288, 0);
      assertSame(booleanArray0, booleanArray1);
  }

  @Test(timeout = 4000)
  public void test107()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[4];
      // Undeclared exception!
      try { 
        Conversion.intToBinary((byte)0, 1441, booleanArray0, 2617, 2653);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+srcPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test108()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[9];
      // Undeclared exception!
      try { 
        Conversion.longToBinary((short) (-3929), (-2402), booleanArray0, (-2402), 53);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -2402
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test109()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      boolean[] booleanArray1 = Conversion.longToBinary(0L, 0, booleanArray0, 0, (-2036));
      assertTrue(Arrays.equals(new boolean[] {false, false, false, false, false, false, false, false}, booleanArray1));
  }

  @Test(timeout = 4000)
  public void test110()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      // Undeclared exception!
      try { 
        Conversion.longToBinary(0, 0, booleanArray0, (-1), 1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -1
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test111()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.longToBinary((-460L), 155, (boolean[]) null, 13, 0);
      assertNull(booleanArray0);
  }

  @Test(timeout = 4000)
  public void test112()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[4];
      // Undeclared exception!
      try { 
        Conversion.longToBinary((-1501L), 3170, booleanArray0, (short)1, 75);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+srcPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test113()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.byteToHex((byte)101, (-2320), "", (-2320), 58);
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
         //
         // String index out of range: -2320
         //
         verifyException("java.lang.AbstractStringBuilder", e);
      }
  }

  @Test(timeout = 4000)
  public void test114()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.byteToHex((byte) (-49), (byte)38, "", (byte) (-49), 9);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+srcPos is greater or equal to than 8
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test115()  throws Throwable  {
      String string0 = Conversion.byteToHex((byte)69, (byte)69, (String) null, (-1), (short)0);
      assertNull(string0);
  }

  @Test(timeout = 4000)
  public void test116()  throws Throwable  {
      String string0 = Conversion.byteToHex((byte) (-89), (byte) (-89), "", (byte) (-89), (-2841));
      assertEquals("", string0);
  }

  @Test(timeout = 4000)
  public void test117()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.shortToHex((byte) (-54), (-1863), "", (-1863), 98);
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
         //
         // String index out of range: -1863
         //
         verifyException("java.lang.AbstractStringBuilder", e);
      }
  }

  @Test(timeout = 4000)
  public void test118()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.shortToHex((short)15, (byte)76, "", 15, 2179);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+srcPos is greater or equal to than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test119()  throws Throwable  {
      String string0 = Conversion.shortToHex((short)1347, (-6453), "nBytes is greater than 16", (-738), 0);
      assertEquals("nBytes is greater than 16", string0);
  }

  @Test(timeout = 4000)
  public void test120()  throws Throwable  {
      String string0 = Conversion.shortToHex((short)54, (-403), "", (short)54, (-2070));
      assertEquals("", string0);
  }

  @Test(timeout = 4000)
  public void test121()  throws Throwable  {
      String string0 = Conversion.intToHex((-1126), (-1126), "(nShorts-1)*16+srcPos is greater or equal to than 32", 49, 49);
      assertEquals("(nShorts-1)*16+srcPos is greater or equal to thanff6eefffff6eefffff6eefffff6eefffff6eefffff6eeffff", string0);
  }

  @Test(timeout = 4000)
  public void test122()  throws Throwable  {
      String string0 = Conversion.intToHex((-640), 0, "p4&psy(t1\"]dY", 2032, (-1699));
      assertEquals("p4&psy(t1\"]dY", string0);
  }

  @Test(timeout = 4000)
  public void test123()  throws Throwable  {
      String string0 = Conversion.intToHex((byte)0, (-2515), "", (-2431), 0);
      assertEquals("", string0);
  }

  @Test(timeout = 4000)
  public void test124()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.intToHex(62, 65, "", 65, 3);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+srcPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test125()  throws Throwable  {
      String string0 = Conversion.longToHex((-17), (-3643), "src.length>8: src.length=", 0, 73);
      assertEquals("fffffffffffffff7fffffffffffffff7fffffffffffffff7fffffffffffffff7fffffffff", string0);
  }

  @Test(timeout = 4000)
  public void test126()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.longToHex(3, 90, "s{*G|5.qc2IR", 3, 1);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+srcPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test127()  throws Throwable  {
      String string0 = Conversion.longToHex(1, 10, "A", 83, 0);
      assertEquals("A", string0);
  }

  @Test(timeout = 4000)
  public void test128()  throws Throwable  {
      byte[] byteArray0 = new byte[1];
      byte[] byteArray1 = Conversion.shortToByteArray((byte)0, (byte)0, byteArray0, (byte)0, 1);
      assertArrayEquals(new byte[] {(byte)0}, byteArray1);
  }

  @Test(timeout = 4000)
  public void test129()  throws Throwable  {
      byte[] byteArray0 = Conversion.shortToByteArray((short) (-524), (short) (-524), (byte[]) null, (-1), (-237));
      assertNull(byteArray0);
  }

  @Test(timeout = 4000)
  public void test130()  throws Throwable  {
      byte[] byteArray0 = new byte[2];
      byte[] byteArray1 = Conversion.shortToByteArray((short) (-3255), 1, byteArray0, (short) (-3255), 0);
      assertSame(byteArray0, byteArray1);
  }

  @Test(timeout = 4000)
  public void test131()  throws Throwable  {
      byte[] byteArray0 = new byte[1];
      // Undeclared exception!
      try { 
        Conversion.shortToByteArray((short)1, (short)1, byteArray0, (short)1, (byte)36);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nBytes-1)*8+srcPos is greater or equal to than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test132()  throws Throwable  {
      byte[] byteArray0 = new byte[2];
      byte[] byteArray1 = Conversion.intToByteArray((-478), (-478), byteArray0, (byte)90, (-20));
      assertEquals(2, byteArray1.length);
  }

  @Test(timeout = 4000)
  public void test133()  throws Throwable  {
      byte[] byteArray0 = new byte[1];
      // Undeclared exception!
      try { 
        Conversion.intToByteArray(16, 3, byteArray0, (byte)90, 3);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 90
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test134()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      // Undeclared exception!
      try { 
        Conversion.intToByteArray((-3189), (-3189), byteArray0, (-3189), 870);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nBytes-1)*8+srcPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test135()  throws Throwable  {
      byte[] byteArray0 = new byte[5];
      // Undeclared exception!
      try { 
        Conversion.longToByteArray((short) (-761), (short) (-761), byteArray0, (byte)55, 32);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 55
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test136()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      // Undeclared exception!
      try { 
        Conversion.longToByteArray((byte) (-126), (byte)110, byteArray0, 54, 1963);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nBytes-1)*8+srcPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test137()  throws Throwable  {
      byte[] byteArray0 = Conversion.longToByteArray(0L, 974, (byte[]) null, 76, 0);
      assertNull(byteArray0);
  }

  @Test(timeout = 4000)
  public void test138()  throws Throwable  {
      byte[] byteArray0 = new byte[7];
      UUID uUID0 = MockUUID.randomUUID();
      byte[] byteArray1 = Conversion.uuidToByteArray(uUID0, byteArray0, (byte)0, (-1));
      assertArrayEquals(new byte[] {(byte)0, (byte)0, (byte)0, (byte)0, (byte)0, (byte)0, (byte)0}, byteArray1);
  }

  @Test(timeout = 4000)
  public void test139()  throws Throwable  {
      short[] shortArray0 = new short[1];
      short[] shortArray1 = Conversion.intToShortArray((-847), (-1497), shortArray0, (-3577), (-3577));
      assertEquals(1, shortArray1.length);
  }

  @Test(timeout = 4000)
  public void test140()  throws Throwable  {
      short[] shortArray0 = new short[0];
      short[] shortArray1 = Conversion.intToShortArray(0, 0, shortArray0, 0, 0);
      assertSame(shortArray0, shortArray1);
  }

  @Test(timeout = 4000)
  public void test141()  throws Throwable  {
      short[] shortArray0 = new short[0];
      // Undeclared exception!
      try { 
        Conversion.intToShortArray(0, 0, shortArray0, 124, 124);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nShorts-1)*16+srcPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test142()  throws Throwable  {
      short[] shortArray0 = new short[3];
      short[] shortArray1 = Conversion.longToShortArray(1L, 88, shortArray0, (-2888), (-1778));
      assertArrayEquals(new short[] {(short)0, (short)0, (short)0}, shortArray1);
  }

  @Test(timeout = 4000)
  public void test143()  throws Throwable  {
      short[] shortArray0 = Conversion.longToShortArray(817L, (-997), (short[]) null, (-2847), 0);
      assertNull(shortArray0);
  }

  @Test(timeout = 4000)
  public void test144()  throws Throwable  {
      short[] shortArray0 = new short[0];
      // Undeclared exception!
      try { 
        Conversion.longToShortArray(1L, (-1), shortArray0, 64, 35);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nShorts-1)*16+srcPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test145()  throws Throwable  {
      int[] intArray0 = new int[5];
      // Undeclared exception!
      try { 
        Conversion.longToIntArray((-2371L), (-17), intArray0, (-17), 1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -17
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test146()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.longToIntArray((-426), (-426), (int[]) null, (-426), 99);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nInts-1)*32+srcPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test147()  throws Throwable  {
      int[] intArray0 = new int[2];
      int[] intArray1 = Conversion.longToIntArray(1554L, (-2523), intArray0, (short)0, 0);
      assertSame(intArray1, intArray0);
  }

  @Test(timeout = 4000)
  public void test148()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[28];
      booleanArray0[1] = true;
      byte byte0 = Conversion.binaryToByte(booleanArray0, 1, (byte)0, 1, 1);
      assertEquals((byte)2, byte0);
  }

  @Test(timeout = 4000)
  public void test149()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      // Undeclared exception!
      try { 
        Conversion.binaryToByte(booleanArray0, (short)54, (byte)72, (short) (-393), 57);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 54
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test150()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      byte byte0 = Conversion.binaryToByte(booleanArray0, 1, (byte)0, (short) (-1424), (byte)0);
      assertEquals((byte)0, byte0);
  }

  @Test(timeout = 4000)
  public void test151()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      // Undeclared exception!
      try { 
        Conversion.binaryToByte(booleanArray0, (-3596), (byte)0, 633, 787);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+dstPos is greater or equal to than 8
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test152()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      // Undeclared exception!
      try { 
        Conversion.binaryToShort(booleanArray0, (short)0, (short) (-1), (-2790), 32);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 8
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test153()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      short short0 = Conversion.binaryToShort(booleanArray0, (-17), (short)0, (-17), (short)0);
      assertEquals((short)0, short0);
  }

  @Test(timeout = 4000)
  public void test154()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[9];
      short short0 = Conversion.binaryToShort(booleanArray0, (-17), (short) (-25), (-17), (short) (-25));
      assertEquals((short) (-25), short0);
  }

  @Test(timeout = 4000)
  public void test155()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      booleanArray0[2] = true;
      // Undeclared exception!
      try { 
        Conversion.binaryToInt(booleanArray0, (byte)1, (-3471), (-2458), 6);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 5
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test156()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      int int0 = Conversion.binaryToInt(booleanArray0, (-2233), (-2233), (-2303), (-2303));
      assertEquals((-2233), int0);
  }

  @Test(timeout = 4000)
  public void test157()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      // Undeclared exception!
      try { 
        Conversion.binaryToInt(booleanArray0, 94, 94, 54, 67);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+dstPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test158()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[1];
      int int0 = Conversion.binaryToInt(booleanArray0, 54, 1112, 0, 0);
      assertEquals(1112, int0);
  }

  @Test(timeout = 4000)
  public void test159()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[16];
      // Undeclared exception!
      try { 
        Conversion.binaryToLong(booleanArray0, (-2921), 1L, (-2921), 56);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -2921
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test160()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[3];
      long long0 = Conversion.binaryToLong(booleanArray0, 0, 0, 0, 0);
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test161()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      // Undeclared exception!
      try { 
        Conversion.binaryToLong(booleanArray0, 54, (-144L), 96, 12);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+dstPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test162()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      long long0 = Conversion.binaryToLong(booleanArray0, 2634, (-62), 70, (-62));
      assertEquals((-62L), long0);
  }

  @Test(timeout = 4000)
  public void test163()  throws Throwable  {
      byte byte0 = Conversion.hexToByte("", (byte)55, (byte) (-117), (byte)55, (-120));
      assertEquals((byte) (-117), byte0);
  }

  @Test(timeout = 4000)
  public void test164()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToByte("9Vx9mt", (short)13, (byte) (-79), 49, (short)13);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+dstPos is greater or equal to than 8
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test165()  throws Throwable  {
      byte byte0 = Conversion.hexToByte("V@&,", 0, (byte)0, 0, 0);
      assertEquals((byte)0, byte0);
  }

  @Test(timeout = 4000)
  public void test166()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToByte("||_i&tfsO~nwDRXeH/w", 53, (byte) (-7), (-2947), 49);
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
      }
  }

  @Test(timeout = 4000)
  public void test167()  throws Throwable  {
      short short0 = Conversion.hexToShort("", (-2796), (short)0, (short)0, (-17));
      assertEquals((short)0, short0);
  }

  @Test(timeout = 4000)
  public void test168()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToShort("nBytes is greater than 16", 90, (byte)0, (short) (-2990), 86);
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
      }
  }

  @Test(timeout = 4000)
  public void test169()  throws Throwable  {
      short short0 = Conversion.hexToShort("{C{P", (-965), (short)66, 54, (short)0);
      assertEquals((short)66, short0);
  }

  @Test(timeout = 4000)
  public void test170()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToShort("<sST!", 1, (short)1494, (short)1494, (short)1494);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+dstPos is greater or equal to than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test171()  throws Throwable  {
      int int0 = Conversion.hexToInt("", (-4845), (-3899), (-4845), (-3899));
      assertEquals((-3899), int0);
  }

  @Test(timeout = 4000)
  public void test172()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToInt("", 84, 3, 3, 3);
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
      }
  }

  @Test(timeout = 4000)
  public void test173()  throws Throwable  {
      int int0 = Conversion.hexToInt("", (-2359), (-2359), (byte)0, (byte)0);
      assertEquals((-2359), int0);
  }

  @Test(timeout = 4000)
  public void test174()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToInt("", 18, 84, 84, 18);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+dstPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test175()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToLong("!Y\"AUX1#Y/2I5'HK]", 'D', (-226), (-2506), 'b');
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
      }
  }

  @Test(timeout = 4000)
  public void test176()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToLong((String) null, (-158), 9, 67, 66);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+dstPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test177()  throws Throwable  {
      long long0 = Conversion.hexToLong("QDx^o2", 71, 0, (-395), 0);
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test178()  throws Throwable  {
      long long0 = Conversion.hexToLong("QDx^o2", 71, 0, (-395), (-1));
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test179()  throws Throwable  {
      byte[] byteArray0 = new byte[12];
      // Undeclared exception!
      try { 
        Conversion.byteArrayToShort(byteArray0, (-5), (short) (-1), (short) (-1), 1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -5
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test180()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      short short0 = Conversion.byteArrayToShort(byteArray0, 10, (byte) (-84), 1963, (-2277));
      assertEquals((short) (-84), short0);
  }

  @Test(timeout = 4000)
  public void test181()  throws Throwable  {
      byte[] byteArray0 = new byte[9];
      short short0 = Conversion.byteArrayToShort(byteArray0, (-3399), (byte)46, (-2142), (byte)0);
      assertEquals((short)46, short0);
  }

  @Test(timeout = 4000)
  public void test182()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      // Undeclared exception!
      try { 
        Conversion.byteArrayToShort(byteArray0, 55, (short)93, 55, 7);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nBytes-1)*8+dstPos is greater or equal to than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test183()  throws Throwable  {
      byte[] byteArray0 = new byte[9];
      // Undeclared exception!
      try { 
        Conversion.byteArrayToInt(byteArray0, 0, 82, (-939), 12);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 9
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test184()  throws Throwable  {
      byte[] byteArray0 = new byte[7];
      int int0 = Conversion.byteArrayToInt(byteArray0, (byte)0, (-569), (-519), (-569));
      assertEquals((-569), int0);
  }

  @Test(timeout = 4000)
  public void test185()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      // Undeclared exception!
      try { 
        Conversion.byteArrayToInt(byteArray0, 10, 1, 10, 10);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nBytes-1)*8+dstPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test186()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      int int0 = Conversion.byteArrayToInt(byteArray0, 0, 1, 0, 0);
      assertEquals(1, int0);
  }

  @Test(timeout = 4000)
  public void test187()  throws Throwable  {
      byte[] byteArray0 = new byte[7];
      int int0 = Conversion.byteArrayToInt(byteArray0, (byte)0, (-2578), 676, (byte)0);
      assertEquals((-2578), int0);
  }

  @Test(timeout = 4000)
  public void test188()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      long long0 = Conversion.byteArrayToLong(byteArray0, (byte)0, 0L, (byte)0, (byte)0);
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test189()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      long long0 = Conversion.byteArrayToLong(byteArray0, 0, 1L, 0, (-585));
      assertEquals(1L, long0);
  }

  @Test(timeout = 4000)
  public void test190()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      // Undeclared exception!
      try { 
        Conversion.byteArrayToUuid(byteArray0, (short) (-2728));
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -2728
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test191()  throws Throwable  {
      short[] shortArray0 = new short[6];
      int int0 = Conversion.shortArrayToInt(shortArray0, (short)0, (-1283), 50, (-282));
      assertEquals((-1283), int0);
  }

  @Test(timeout = 4000)
  public void test192()  throws Throwable  {
      short[] shortArray0 = new short[1];
      // Undeclared exception!
      try { 
        Conversion.shortArrayToInt(shortArray0, (byte) (-60), 54, (-1), 1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -60
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test193()  throws Throwable  {
      short[] shortArray0 = new short[0];
      // Undeclared exception!
      try { 
        Conversion.shortArrayToInt(shortArray0, 541, (byte)6, 541, 9);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nShorts-1)*16+dstPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test194()  throws Throwable  {
      short[] shortArray0 = new short[0];
      int int0 = Conversion.shortArrayToInt(shortArray0, 0, 0, 369, (-3356));
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test195()  throws Throwable  {
      short[] shortArray0 = new short[2];
      int int0 = Conversion.shortArrayToInt(shortArray0, (short)16, (-388), 255, (byte)0);
      assertEquals((-388), int0);
  }

  @Test(timeout = 4000)
  public void test196()  throws Throwable  {
      short[] shortArray0 = new short[0];
      // Undeclared exception!
      try { 
        Conversion.shortArrayToLong(shortArray0, 91, 91, (-5554), 91);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 91
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test197()  throws Throwable  {
      short[] shortArray0 = new short[0];
      long long0 = Conversion.shortArrayToLong(shortArray0, 64, (-1), (short)0, (short)0);
      assertEquals((-1L), long0);
  }

  @Test(timeout = 4000)
  public void test198()  throws Throwable  {
      short[] shortArray0 = new short[0];
      long long0 = Conversion.shortArrayToLong(shortArray0, (short)0, (-1), (short)0, (short)0);
      assertEquals((-1L), long0);
  }

  @Test(timeout = 4000)
  public void test199()  throws Throwable  {
      int[] intArray0 = new int[6];
      long long0 = Conversion.intArrayToLong(intArray0, 98, 0L, 49, (-313));
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test200()  throws Throwable  {
      int[] intArray0 = new int[1];
      long long0 = Conversion.intArrayToLong(intArray0, (byte) (-36), (byte)107, (-868), (byte)0);
      assertEquals(107L, long0);
  }

  @Test(timeout = 4000)
  public void test201()  throws Throwable  {
      int[] intArray0 = new int[2];
      // Undeclared exception!
      try { 
        Conversion.intArrayToLong(intArray0, 86, 0L, 84, 98);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nInts-1)*32+dstPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test202()  throws Throwable  {
      char char0 = Conversion.intToHexDigitMsb0(5);
      assertEquals('a', char0);
  }

  @Test(timeout = 4000)
  public void test203()  throws Throwable  {
      char char0 = Conversion.intToHexDigitMsb0(3);
      assertEquals('c', char0);
  }

  @Test(timeout = 4000)
  public void test204()  throws Throwable  {
      char char0 = Conversion.intToHexDigitMsb0(1);
      assertEquals('8', char0);
  }

  @Test(timeout = 4000)
  public void test205()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.intToHexDigitMsb0((-220));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nibble value not between 0 and 15: -220
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test206()  throws Throwable  {
      char char0 = Conversion.intToHexDigitMsb0((byte)0);
      assertEquals('0', char0);
  }

  @Test(timeout = 4000)
  public void test207()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.intToHexDigit((short)35);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nibble value not between 0 and 15: 35
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test208()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[1];
      booleanArray0[0] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0);
      assertEquals('1', char0);
  }

  @Test(timeout = 4000)
  public void test209()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[2];
      booleanArray0[0] = true;
      booleanArray0[1] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0, (int) (byte)0);
      assertEquals('3', char0);
  }

  @Test(timeout = 4000)
  public void test210()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[3];
      booleanArray0[0] = true;
      booleanArray0[2] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0);
      assertEquals('5', char0);
  }

  @Test(timeout = 4000)
  public void test211()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[6];
      booleanArray0[3] = true;
      booleanArray0[4] = true;
      booleanArray0[5] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0);
      assertEquals('7', char0);
  }

  @Test(timeout = 4000)
  public void test212()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      booleanArray0[1] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0, 1);
      assertEquals('4', char0);
  }

  @Test(timeout = 4000)
  public void test213()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[4];
      booleanArray0[0] = true;
      booleanArray0[2] = true;
      booleanArray0[3] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0);
      assertEquals('b', char0);
  }

  @Test(timeout = 4000)
  public void test214()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[4];
      booleanArray0[0] = true;
      booleanArray0[2] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0);
      assertEquals('a', char0);
  }

  @Test(timeout = 4000)
  public void test215()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      booleanArray0[0] = true;
      booleanArray0[1] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0, 1);
      assertEquals('c', char0);
  }

  @Test(timeout = 4000)
  public void test216()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      booleanArray0[1] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0, (int) (byte)0);
      assertEquals('8', char0);
  }

  @Test(timeout = 4000)
  public void test217()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      // Undeclared exception!
      try { 
        Conversion.binaryBeMsb0ToHexDigit(booleanArray0, (-730));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot convert an empty array.
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test218()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[7];
      booleanArray0[0] = true;
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0);
      assertEquals('8', char0);
  }

  @Test(timeout = 4000)
  public void test219()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      booleanArray0[0] = true;
      booleanArray0[1] = true;
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, (int) (byte)0);
      assertEquals('c', char0);
  }

  @Test(timeout = 4000)
  public void test220()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      booleanArray0[1] = true;
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, (int) (byte)0);
      assertEquals('4', char0);
  }

  @Test(timeout = 4000)
  public void test221()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[7];
      booleanArray0[0] = true;
      booleanArray0[2] = true;
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0);
      assertEquals('a', char0);
  }

  @Test(timeout = 4000)
  public void test222()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[7];
      booleanArray0[1] = true;
      booleanArray0[2] = true;
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, 1);
      assertEquals('e', char0);
  }

  @Test(timeout = 4000)
  public void test223()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      booleanArray0[1] = true;
      booleanArray0[2] = true;
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0);
      assertEquals('6', char0);
  }

  @Test(timeout = 4000)
  public void test224()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[7];
      booleanArray0[1] = true;
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, 0);
      assertEquals('5', char0);
  }

  @Test(timeout = 4000)
  public void test225()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[4];
      booleanArray0[2] = true;
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, 0);
      assertEquals('3', char0);
  }

  @Test(timeout = 4000)
  public void test226()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[7];
      booleanArray0[0] = true;
      booleanArray0[1] = true;
      booleanArray0[2] = true;
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, 0);
      assertEquals('f', char0);
  }

  @Test(timeout = 4000)
  public void test227()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[7];
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, 0);
      assertEquals('1', char0);
  }

  @Test(timeout = 4000)
  public void test228()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[9];
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, (-1173));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // src.length>8: src.length=9
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test229()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[6];
      booleanArray0[0] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0);
      assertEquals('1', char0);
  }

  @Test(timeout = 4000)
  public void test230()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      booleanArray0[1] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0, (int) (byte)0);
      assertEquals('2', char0);
  }

  @Test(timeout = 4000)
  public void test231()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[3];
      booleanArray0[0] = true;
      booleanArray0[2] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0);
      assertEquals('5', char0);
  }

  @Test(timeout = 4000)
  public void test232()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[3];
      booleanArray0[0] = true;
      booleanArray0[1] = true;
      booleanArray0[2] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0);
      assertEquals('7', char0);
  }

  @Test(timeout = 4000)
  public void test233()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[3];
      booleanArray0[1] = true;
      booleanArray0[2] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0);
      assertEquals('6', char0);
  }

  @Test(timeout = 4000)
  public void test234()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[4];
      booleanArray0[0] = true;
      booleanArray0[1] = true;
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0, 0);
      assertEquals('b', char0);
  }

  @Test(timeout = 4000)
  public void test235()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[4];
      booleanArray0[1] = true;
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0, 0);
      assertEquals('a', char0);
  }

  @Test(timeout = 4000)
  public void test236()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[7];
      booleanArray0[0] = true;
      booleanArray0[2] = true;
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0);
      assertEquals('d', char0);
  }

  @Test(timeout = 4000)
  public void test237()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      booleanArray0[0] = true;
      booleanArray0[1] = true;
      booleanArray0[2] = true;
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0);
      assertEquals('f', char0);
  }

  @Test(timeout = 4000)
  public void test238()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigit(booleanArray0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot convert an empty array.
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test239()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToBinary('k');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'k' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test240()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitMsb0ToBinary('f');
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0);
      assertEquals('f', char0);
  }

  @Test(timeout = 4000)
  public void test241()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitMsb0ToBinary('e');
      assertTrue(Arrays.equals(new boolean[] {true, true, true, false}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test242()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitMsb0ToBinary('d');
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0);
      assertEquals('d', char0);
  }

  @Test(timeout = 4000)
  public void test243()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitMsb0ToBinary('c');
      assertTrue(Arrays.equals(new boolean[] {true, true, false, false}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test244()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitMsb0ToBinary('b');
      assertTrue(Arrays.equals(new boolean[] {true, false, true, true}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test245()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitMsb0ToBinary('a');
      assertTrue(Arrays.equals(new boolean[] {true, false, true, false}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test246()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToBinary('^');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret '^' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test247()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToBinary('[');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret '[' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test248()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToBinary('Y');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'Y' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test249()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToBinary('W');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'W' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test250()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToBinary('V');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'V' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test251()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToBinary('S');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'S' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test252()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToBinary('R');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'R' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test253()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToBinary('Q');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'Q' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test254()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToBinary('P');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'P' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test255()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToBinary('K');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'K' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test256()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitMsb0ToBinary('F');
      // Undeclared exception!
      try { 
        Conversion.binaryToShort(booleanArray0, 1, (short)16, (-26), 32);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 4
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test257()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitMsb0ToBinary('E');
      // Undeclared exception!
      try { 
        Conversion.byteToBinary((byte)53, (byte)53, booleanArray0, (byte)53, (byte)53);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+srcPos is greater or equal to than 8
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test258()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitMsb0ToBinary('D');
      assertTrue(Arrays.equals(new boolean[] {true, true, false, true}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test259()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitMsb0ToBinary('A');
      assertTrue(Arrays.equals(new boolean[] {true, false, true, false}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test260()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToBinary('@');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret '@' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test261()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToBinary('>');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret '>' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test262()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToBinary('=');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret '=' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test263()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitMsb0ToBinary('7');
      assertTrue(Arrays.equals(new boolean[] {false, true, true, true}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test264()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitMsb0ToBinary('6');
      assertTrue(Arrays.equals(new boolean[] {false, true, true, false}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test265()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitMsb0ToBinary('5');
      assertTrue(Arrays.equals(new boolean[] {false, true, false, true}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test266()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitMsb0ToBinary('3');
      assertTrue(Arrays.equals(new boolean[] {false, false, true, true}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test267()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitMsb0ToBinary('2');
      assertTrue(Arrays.equals(new boolean[] {false, false, true, false}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test268()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitMsb0ToBinary('1');
      assertTrue(Arrays.equals(new boolean[] {false, false, false, true}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test269()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToBinary('`');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret '`' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test270()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitMsb0ToBinary('0');
      assertTrue(Arrays.equals(new boolean[] {false, false, false, false}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test271()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitToBinary('f');
      assertEquals(4, booleanArray0.length);
  }

  @Test(timeout = 4000)
  public void test272()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitToBinary('e');
      assertEquals(4, booleanArray0.length);
  }

  @Test(timeout = 4000)
  public void test273()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitToBinary('d');
      assertTrue(Arrays.equals(new boolean[] {true, false, true, true}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test274()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitToBinary('c');
      assertTrue(Arrays.equals(new boolean[] {false, false, true, true}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test275()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitToBinary('b');
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0);
      assertEquals('d', char0);
  }

  @Test(timeout = 4000)
  public void test276()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitToBinary('a');
      assertTrue(Arrays.equals(new boolean[] {false, true, false, true}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test277()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitToBinary('`');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret '`' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test278()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitToBinary('_');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret '_' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test279()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitToBinary('[');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret '[' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test280()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitToBinary('Z');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'Z' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test281()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitToBinary('Y');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'Y' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test282()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitToBinary('W');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'W' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test283()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitToBinary('U');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'U' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test284()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitToBinary('T');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'T' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test285()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitToBinary('Q');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'Q' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test286()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitToBinary('P');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'P' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test287()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitToBinary('N');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'N' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test288()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitToBinary('L');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'L' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test289()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitToBinary('I');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'I' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test290()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitToBinary('H');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'H' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test291()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitToBinary('G');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'G' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test292()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitToBinary('E');
      assertTrue(Arrays.equals(new boolean[] {false, true, true, true}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test293()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitToBinary('D');
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0);
      assertEquals('b', char0);
  }

  @Test(timeout = 4000)
  public void test294()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitToBinary('A');
      assertTrue(Arrays.equals(new boolean[] {false, true, false, true}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test295()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitToBinary('@');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret '@' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test296()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitToBinary('=');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret '=' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test297()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitToBinary(':');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret ':' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test298()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitToBinary('9');
      assertTrue(Arrays.equals(new boolean[] {true, false, false, true}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test299()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitToBinary('8');
      assertTrue(Arrays.equals(new boolean[] {false, false, false, true}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test300()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitToBinary('7');
      assertTrue(Arrays.equals(new boolean[] {true, true, true, false}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test301()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitToBinary('6');
      assertTrue(Arrays.equals(new boolean[] {false, true, true, false}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test302()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitToBinary('5');
      assertTrue(Arrays.equals(new boolean[] {true, false, true, false}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test303()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitToBinary('4');
      assertTrue(Arrays.equals(new boolean[] {false, false, true, false}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test304()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitToBinary('3');
      assertTrue(Arrays.equals(new boolean[] {true, true, false, false}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test305()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitToBinary('2');
      assertTrue(Arrays.equals(new boolean[] {false, true, false, false}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test306()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitToBinary('1');
      assertTrue(Arrays.equals(new boolean[] {true, false, false, false}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test307()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitToBinary('*');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret '*' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test308()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitToBinary('0');
      assertTrue(Arrays.equals(new boolean[] {false, false, false, false}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test309()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToInt('w');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'w' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test310()  throws Throwable  {
      int int0 = Conversion.hexDigitMsb0ToInt('c');
      assertEquals(3, int0);
  }

  @Test(timeout = 4000)
  public void test311()  throws Throwable  {
      int int0 = Conversion.hexDigitMsb0ToInt('b');
      assertEquals(13, int0);
  }

  @Test(timeout = 4000)
  public void test312()  throws Throwable  {
      int int0 = Conversion.hexDigitMsb0ToInt('a');
      assertEquals(5, int0);
  }

  @Test(timeout = 4000)
  public void test313()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToInt('^');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret '^' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test314()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToInt('[');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret '[' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test315()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToInt('Z');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'Z' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test316()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToInt('V');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'V' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test317()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToInt('Q');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'Q' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test318()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToInt('O');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'O' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test319()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToInt('N');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'N' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test320()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToInt('M');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'M' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test321()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToInt('L');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'L' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test322()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToInt('J');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'J' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test323()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToInt('H');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'H' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test324()  throws Throwable  {
      int int0 = Conversion.hexDigitMsb0ToInt('D');
      assertEquals(11, int0);
  }

  @Test(timeout = 4000)
  public void test325()  throws Throwable  {
      int int0 = Conversion.hexDigitMsb0ToInt('A');
      assertEquals(5, int0);
  }

  @Test(timeout = 4000)
  public void test326()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToInt('@');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret '@' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test327()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToInt('=');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret '=' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test328()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToInt('<');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret '<' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test329()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToInt(';');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret ';' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test330()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToInt(':');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret ':' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test331()  throws Throwable  {
      int int0 = Conversion.hexDigitMsb0ToInt('9');
      assertEquals(9, int0);
  }

  @Test(timeout = 4000)
  public void test332()  throws Throwable  {
      int int0 = Conversion.hexDigitMsb0ToInt('8');
      assertEquals(1, int0);
  }

  @Test(timeout = 4000)
  public void test333()  throws Throwable  {
      int int0 = Conversion.hexDigitMsb0ToInt('6');
      assertEquals(6, int0);
  }

  @Test(timeout = 4000)
  public void test334()  throws Throwable  {
      int int0 = Conversion.hexDigitMsb0ToInt('4');
      assertEquals(2, int0);
  }

  @Test(timeout = 4000)
  public void test335()  throws Throwable  {
      int int0 = Conversion.hexDigitMsb0ToInt('3');
      assertEquals(12, int0);
  }

  @Test(timeout = 4000)
  public void test336()  throws Throwable  {
      int int0 = Conversion.hexDigitMsb0ToInt('2');
      assertEquals(4, int0);
  }

  @Test(timeout = 4000)
  public void test337()  throws Throwable  {
      int int0 = Conversion.hexDigitMsb0ToInt('1');
      assertEquals(8, int0);
  }

  @Test(timeout = 4000)
  public void test338()  throws Throwable  {
      int int0 = Conversion.hexDigitMsb0ToInt('0');
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test339()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitToInt('\'');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret ''' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test340()  throws Throwable  {
      int int0 = Conversion.hexDigitToInt('7');
      assertEquals(7, int0);
  }

  @Test(timeout = 4000)
  public void test341()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigitMsb0_4bits(booleanArray0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // src.length-srcPos<4: src.length=0, srcPos=0
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test342()  throws Throwable  {
      Conversion conversion0 = new Conversion();
  }
}
