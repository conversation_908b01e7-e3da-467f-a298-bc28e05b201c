/*
 * This file was automatically generated by EvoSuite
 * Wed Jun 18 07:58:01 GMT 2025
 */

package org.apache.commons.cli;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.io.ByteArrayOutputStream;
import java.io.OutputStream;
import java.io.PipedOutputStream;
import java.io.PrintStream;
import java.io.PrintWriter;
import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.util.Comparator;
import java.util.Locale;
import org.apache.commons.cli.HelpFormatter;
import org.apache.commons.cli.Option;
import org.apache.commons.cli.OptionGroup;
import org.apache.commons.cli.Options;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.evosuite.runtime.mock.java.io.MockFile;
import org.evosuite.runtime.mock.java.io.MockFileOutputStream;
import org.evosuite.runtime.mock.java.io.MockPrintStream;
import org.evosuite.runtime.mock.java.io.MockPrintWriter;
import org.evosuite.runtime.mock.java.net.MockURI;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true) 
public class HelpFormatter_ESTest extends HelpFormatter_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      MockFileOutputStream mockFileOutputStream0 = new MockFileOutputStream("-");
      Options options0 = new Options();
      helpFormatter0.printHelp("-", options0, true);
      helpFormatter0.setDescPadding(1973);
      helpFormatter0.getLongOptPrefix();
      helpFormatter0.getLeftPadding();
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      String string0 = "~LU";
      String string1 = "";
      Options options0 = null;
      // Undeclared exception!
      try { 
        helpFormatter0.printHelp("", (Options) null);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // cmdLineSyntax not provided
         //
         verifyException("org.apache.commons.cli.HelpFormatter", e);
      }
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      StringBuffer stringBuffer0 = new StringBuffer();
      int int0 = 553;
      String string0 = null;
      // Undeclared exception!
      try { 
        helpFormatter0.renderWrappedText(stringBuffer0, 553, 553, (String) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.cli.HelpFormatter", e);
      }
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      String string0 = "   ";
      helpFormatter0.rtrim("   ");
      Options options0 = new Options();
      OptionGroup optionGroup0 = new OptionGroup();
      options0.addOptionGroup(optionGroup0);
      String string1 = "Cannot add value, list full.";
      Option option0 = null;
      try {
        option0 = new Option("Cannot add value, list full.", "");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The option 'Cannot add value, list full.' contains an illegal character : ' '
         //
         verifyException("org.apache.commons.cli.OptionValidator", e);
      }
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      helpFormatter0.rtrim("i9?_B");
      String string0 = "D";
      helpFormatter0.setLongOptSeparator("D");
      helpFormatter0.setSyntaxPrefix("--");
      String string1 = "_y|&56:qjwe15|@";
      MockFile mockFile0 = new MockFile("mX[^QQqo5~", "_y|&56:qjwe15|@");
      MockPrintWriter mockPrintWriter0 = null;
      try {
        mockPrintWriter0 = new MockPrintWriter(mockFile0, "");
        fail("Expecting exception: UnsupportedEncodingException");
      
      } catch(Throwable e) {
         //
         // 
         //
         verifyException("org.evosuite.runtime.mock.java.io.MockPrintWriter", e);
      }
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      helpFormatter0.defaultLeftPad = 0;
      String string0 = ",Q0}YJ!W@!HDXY";
      helpFormatter0.printWrapped((PrintWriter) null, 0, ">");
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      StringBuffer stringBuffer0 = new StringBuffer("--");
      int int0 = 0;
      StringBuffer stringBuffer1 = helpFormatter0.renderWrappedText(stringBuffer0, 0, 0, "");
      PipedOutputStream pipedOutputStream0 = new PipedOutputStream();
      MockPrintWriter mockPrintWriter0 = new MockPrintWriter(pipedOutputStream0, false);
      Object[] objectArray0 = new Object[3];
      objectArray0[0] = (Object) "";
      objectArray0[1] = (Object) stringBuffer1;
      objectArray0[2] = (Object) stringBuffer1;
      mockPrintWriter0.printf((Locale) null, "", objectArray0);
      String string0 = "[";
      Options options0 = new Options();
      Options options1 = options0.addOption("", "[", false, "usage: ");
      String string1 = "T|mEZ";
      // Undeclared exception!
      try { 
        options1.addOption("T|mEZ", true, "arg");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The option 'T|mEZ' contains an illegal character : '|'
         //
         verifyException("org.apache.commons.cli.OptionValidator", e);
      }
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      URI uRI0 = MockURI.aFileURI;
      MockFile mockFile0 = new MockFile(uRI0);
      mockFile0.setWritable(true, true);
      MockPrintWriter mockPrintWriter0 = new MockPrintWriter(mockFile0);
      Object[] objectArray0 = new Object[6];
      objectArray0[0] = (Object) "";
      objectArray0[1] = (Object) helpFormatter0;
      objectArray0[2] = (Object) mockPrintWriter0;
      objectArray0[3] = (Object) mockPrintWriter0;
      objectArray0[4] = (Object) mockPrintWriter0;
      objectArray0[5] = (Object) uRI0;
      PrintWriter printWriter0 = mockPrintWriter0.format("", objectArray0);
      char[] charArray0 = new char[2];
      charArray0[0] = 'X';
      charArray0[1] = 'w';
      mockPrintWriter0.println(charArray0);
      int int0 = (-460);
      // Undeclared exception!
      try { 
        helpFormatter0.printUsage(printWriter0, (-460), "((*");
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
      }
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      URI uRI0 = MockURI.aFileURI;
      MockFile mockFile0 = new MockFile(uRI0);
      mockFile0.setWritable(true, true);
      MockPrintWriter mockPrintWriter0 = new MockPrintWriter(mockFile0);
      Object[] objectArray0 = new Object[6];
      objectArray0[0] = (Object) "";
      objectArray0[1] = (Object) helpFormatter0;
      objectArray0[2] = (Object) mockPrintWriter0;
      objectArray0[3] = (Object) mockPrintWriter0;
      objectArray0[4] = (Object) mockPrintWriter0;
      objectArray0[5] = (Object) uRI0;
      PrintWriter printWriter0 = mockPrintWriter0.format("", objectArray0);
      char[] charArray0 = new char[2];
      charArray0[0] = 'X';
      charArray0[1] = 'w';
      mockPrintWriter0.println(charArray0);
      // Undeclared exception!
      try { 
        helpFormatter0.printUsage(printWriter0, (-473), "((*");
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
      }
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      helpFormatter0.setOptPrefix("2<FOvv 8ts}Rd<#2}");
      MockFileOutputStream mockFileOutputStream0 = new MockFileOutputStream("@-");
      MockPrintWriter mockPrintWriter0 = new MockPrintWriter(mockFileOutputStream0, false);
      PrintWriter printWriter0 = mockPrintWriter0.printf("--", (Object[]) null);
      MockPrintWriter mockPrintWriter1 = new MockPrintWriter(printWriter0);
      Locale locale0 = new Locale("J\"qKVJ<SM&)=]f/-Lq", "'");
      Object[] objectArray0 = new Object[2];
      Object object0 = new Object();
      objectArray0[0] = object0;
      objectArray0[1] = (Object) "J\"qKVJ<SM&)=]f/-Lq";
      PrintWriter printWriter1 = mockPrintWriter1.printf(locale0, "Jf\"k(QUgVR$x$", objectArray0);
      helpFormatter0.printWrapped(printWriter1, 973, 0, "Jf\"k(QUgVR$x$");
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      String string0 = ".c=<.=2pd?_x>+Su";
      helpFormatter0.setArgName(".c=<.=2pd?_x>+Su");
      String string1 = "";
      helpFormatter0.setNewLine("");
      helpFormatter0.setWidth((-550));
      Options options0 = new Options();
      // Undeclared exception!
      try { 
        options0.addOption("usage: ", "");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The option 'usage: ' contains an illegal character : ':'
         //
         verifyException("org.apache.commons.cli.OptionValidator", e);
      }
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      helpFormatter0.defaultWidth = 1267;
      helpFormatter0.setWidth(0);
      helpFormatter0.getDescPadding();
      int int0 = (-903);
      String string0 = "Xm{aIyvF%iX";
      String string1 = "Cannot add value, list full.";
      Options options0 = new Options();
      OptionGroup optionGroup0 = new OptionGroup();
      Options options1 = options0.addOptionGroup(optionGroup0);
      boolean boolean0 = true;
      // Undeclared exception!
      try { 
        options1.addRequiredOption("Z\"~9_8d:Onr@<EwGaVd", (String) null, true, " ");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The option 'Z\"~9_8d:Onr@<EwGaVd' contains an illegal character : '\"'
         //
         verifyException("org.apache.commons.cli.OptionValidator", e);
      }
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      // Undeclared exception!
      try { 
        helpFormatter0.printHelp("-", (Options) null, false);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.cli.HelpFormatter", e);
      }
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      // Undeclared exception!
      try { 
        helpFormatter0.printHelp("", (Options) null, false);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // cmdLineSyntax not provided
         //
         verifyException("org.apache.commons.cli.HelpFormatter", e);
      }
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      MockFileOutputStream mockFileOutputStream0 = new MockFileOutputStream("usage: ");
      MockPrintStream mockPrintStream0 = new MockPrintStream(mockFileOutputStream0);
      Object object0 = new Object();
      Object object1 = new Object();
      MockPrintStream mockPrintStream1 = new MockPrintStream(mockFileOutputStream0, true);
      Object[] objectArray0 = new Object[7];
      objectArray0[0] = (Object) mockPrintStream0;
      objectArray0[1] = (Object) helpFormatter0;
      objectArray0[2] = (Object) helpFormatter0;
      objectArray0[3] = (Object) mockPrintStream1;
      objectArray0[4] = (Object) mockPrintStream0;
      objectArray0[5] = (Object) "[ option: ";
      objectArray0[6] = (Object) mockPrintStream0;
      PrintStream printStream0 = mockPrintStream1.format("[ option: ", objectArray0);
      MockPrintWriter mockPrintWriter0 = new MockPrintWriter(printStream0);
      int int0 = 1;
      Options options0 = new Options();
      // Undeclared exception!
      try { 
        helpFormatter0.printHelp((PrintWriter) mockPrintWriter0, 1, "", "", options0, 1, 1, "");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // cmdLineSyntax not provided
         //
         verifyException("org.apache.commons.cli.HelpFormatter", e);
      }
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      String string0 = "-yMhG!7a0)yB";
      Options options0 = new Options();
      Comparator<Option> comparator0 = helpFormatter0.optionComparator;
      helpFormatter0.setOptionComparator(comparator0);
      String string1 = "q?/k~YwU{[8Rq->";
      PrintWriter printWriter0 = null;
      // Undeclared exception!
      try { 
        helpFormatter0.printHelp((PrintWriter) null, (-2), "", "-yMhG!7a0)yB", options0, (-1300), (-2), "-yMhG!7a0)yB");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // cmdLineSyntax not provided
         //
         verifyException("org.apache.commons.cli.HelpFormatter", e);
      }
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      String string0 = "%6y|H8imn,d-0~";
      helpFormatter0.defaultSyntaxPrefix = "%6y|H8imn,d-0~";
      helpFormatter0.getLeftPadding();
      helpFormatter0.getLongOptPrefix();
      Options options0 = new Options();
      String string1 = "[ option: ";
      // Undeclared exception!
      try { 
        options0.addRequiredOption("usage: ", "--", false, "[ option: ");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The option 'usage: ' contains an illegal character : ':'
         //
         verifyException("org.apache.commons.cli.OptionValidator", e);
      }
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      String string0 = "";
      // Undeclared exception!
      try { 
        helpFormatter0.renderWrappedText((StringBuffer) null, (-411), (-411), "");
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
      }
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      int int0 = (-411);
      String string0 = "";
      // Undeclared exception!
      try { 
        helpFormatter0.renderWrappedText((StringBuffer) null, (-411), (-411), "");
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
      }
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      helpFormatter0.setDescPadding(0);
      String string0 = "pz%k'8GP5YhN]$";
      helpFormatter0.defaultSyntaxPrefix = "pz%k'8GP5YhN]$";
      helpFormatter0.setLongOptPrefix((String) null);
      helpFormatter0.getSyntaxPrefix();
      MockPrintWriter mockPrintWriter0 = new MockPrintWriter("arg");
      // Undeclared exception!
      try { 
        mockPrintWriter0.append((CharSequence) "arg", 1, 0);
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
      }
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      MockFileOutputStream mockFileOutputStream0 = new MockFileOutputStream("-");
      MockPrintStream mockPrintStream0 = new MockPrintStream(mockFileOutputStream0);
      Object[] objectArray0 = new Object[5];
      objectArray0[0] = (Object) helpFormatter0;
      Object object0 = new Object();
      objectArray0[1] = object0;
      Object object1 = new Object();
      objectArray0[2] = object1;
      objectArray0[3] = (Object) "[ option: ";
      objectArray0[4] = (Object) helpFormatter0;
      PrintStream printStream0 = mockPrintStream0.format("[ option: ", objectArray0);
      MockPrintWriter mockPrintWriter0 = new MockPrintWriter(printStream0);
      Options options0 = new Options();
      // Undeclared exception!
      helpFormatter0.printHelp((PrintWriter) mockPrintWriter0, 1, "[ option: ", "", options0, 1, 1, "");
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      helpFormatter0.setOptPrefix((String) null);
      Comparator<Option> comparator0 = helpFormatter0.optionComparator;
      helpFormatter0.optionComparator = comparator0;
      helpFormatter0.defaultWidth = (-31);
      helpFormatter0.findWrapPos(" ", (-1), (-1));
      helpFormatter0.setNewLine(" ");
      helpFormatter0.getWidth();
      helpFormatter0.getOptPrefix();
      helpFormatter0.getOptPrefix();
      helpFormatter0.createPadding(2655);
      helpFormatter0.getDescPadding();
      helpFormatter0.getOptionComparator();
  }

  @Test(timeout = 4000)
  public void test22()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      ByteArrayOutputStream byteArrayOutputStream0 = new ByteArrayOutputStream();
      MockPrintStream mockPrintStream0 = new MockPrintStream(byteArrayOutputStream0);
      Object object0 = new Object();
      Object object1 = new Object();
      Object[] objectArray0 = new Object[7];
      objectArray0[0] = (Object) helpFormatter0;
      objectArray0[1] = (Object) byteArrayOutputStream0;
      objectArray0[2] = (Object) "[ option: ";
      objectArray0[4] = (Object) byteArrayOutputStream0;
      Object object2 = new Object();
      objectArray0[5] = object2;
      mockPrintStream0.format("\n", objectArray0);
      MockPrintWriter mockPrintWriter0 = new MockPrintWriter(byteArrayOutputStream0);
      Options options0 = new Options();
      helpFormatter0.printHelp((PrintWriter) mockPrintWriter0, 3, "org.apache.commons.cli.Option$1", "[ option: ", options0, 137, 1, "[ option: ");
      PrintWriter printWriter0 = mockPrintWriter0.format("-", objectArray0);
      MockPrintWriter mockPrintWriter1 = new MockPrintWriter(printWriter0);
      // Undeclared exception!
      helpFormatter0.printUsage((PrintWriter) mockPrintWriter1, 1, " | ", options0);
  }

  @Test(timeout = 4000)
  public void test23()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      String string0 = "";
      String string1 = "vYH ";
      helpFormatter0.setNewLine("vYH ");
      helpFormatter0.defaultWidth = (-3528);
      helpFormatter0.setSyntaxPrefix("\"z+'d!(|o5fRi,P~1y%");
      int int0 = 3;
      helpFormatter0.findWrapPos("", 3, 3);
      Options options0 = new Options();
      options0.helpOptions();
      String string2 = "";
      options0.addOption("", "usage: ");
      helpFormatter0.setSyntaxPrefix("");
      options0.helpOptions();
      // Undeclared exception!
      try { 
        helpFormatter0.printHelp((-2191), "", "", options0, "", true);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // cmdLineSyntax not provided
         //
         verifyException("org.apache.commons.cli.HelpFormatter", e);
      }
  }

  @Test(timeout = 4000)
  public void test24()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      helpFormatter0.defaultLongOptPrefix = "";
      helpFormatter0.setArgName("");
      String string0 = "egGtz";
      int int0 = (-694);
      helpFormatter0.findWrapPos("egGtz", (-694), (-1));
      MockPrintWriter mockPrintWriter0 = new MockPrintWriter("arg");
      String string1 = null;
      // Undeclared exception!
      try { 
        helpFormatter0.printWrapped((PrintWriter) mockPrintWriter0, (-2518), (String) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.cli.HelpFormatter", e);
      }
  }

  @Test(timeout = 4000)
  public void test25()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      String string0 = "1\"kS<>z6{tB=\"AV<";
      helpFormatter0.setArgName("1\"kS<>z6{tB=\"AV<");
      helpFormatter0.getOptPrefix();
      helpFormatter0.setArgName("");
      String string1 = ";1bieCPT";
      Comparator<Option> comparator0 = helpFormatter0.getOptionComparator();
      helpFormatter0.setOptionComparator(comparator0);
      helpFormatter0.setArgName(";1bieCPT");
      helpFormatter0.getArgName();
      helpFormatter0.setLongOptPrefix(";1bieCPT");
      helpFormatter0.getDescPadding();
      Options options0 = new Options();
      // Undeclared exception!
      try { 
        options0.addRequiredOption(";1bieCPT", ":GQhK!ugd\"i 0B", false, "wSshZ$");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The option ';1bieCPT' contains an illegal character : ';'
         //
         verifyException("org.apache.commons.cli.OptionValidator", e);
      }
  }

  @Test(timeout = 4000)
  public void test26()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      helpFormatter0.defaultOptPrefix = "ONT";
      helpFormatter0.getOptionComparator();
      helpFormatter0.getWidth();
      String string0 = "";
      helpFormatter0.setOptPrefix("");
      MockPrintWriter mockPrintWriter0 = null;
      try {
        mockPrintWriter0 = new MockPrintWriter((OutputStream) null, false);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("java.io.Writer", e);
      }
  }

  @Test(timeout = 4000)
  public void test27()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      Comparator<Option> comparator0 = helpFormatter0.getOptionComparator();
      helpFormatter0.setArgName("org.apache.commons.cli.HelpFormatter$OptionComparator");
      helpFormatter0.setOptionComparator(comparator0);
      helpFormatter0.setNewLine("2");
      helpFormatter0.defaultOptPrefix = "";
      helpFormatter0.setLeftPadding(44);
      helpFormatter0.getDescPadding();
      MockFile mockFile0 = new MockFile("2");
      MockPrintWriter mockPrintWriter0 = null;
      try {
        mockPrintWriter0 = new MockPrintWriter(mockFile0, "usage: ");
        fail("Expecting exception: UnsupportedEncodingException");
      
      } catch(Throwable e) {
         //
         // usage: 
         //
         verifyException("org.evosuite.runtime.mock.java.io.MockPrintWriter", e);
      }
  }

  @Test(timeout = 4000)
  public void test28()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      helpFormatter0.createPadding(0);
      helpFormatter0.setLongOptSeparator((String) null);
      helpFormatter0.setNewLine("4hmU]uRHQiJ\"{;");
      Options options0 = new Options();
      options0.toString();
      // Undeclared exception!
      try { 
        helpFormatter0.printHelp("", "4hmU]uRHQiJ\"{;", options0, "xR", false);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // cmdLineSyntax not provided
         //
         verifyException("org.apache.commons.cli.HelpFormatter", e);
      }
  }

  @Test(timeout = 4000)
  public void test29()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      MockFile mockFile0 = new MockFile("The addValue method is not intended for client use. Subclasses should use the addValueForProcessing method instead. ");
      helpFormatter0.setNewLine("The addValue method is not intended for client use. Subclasses should use the addValueForProcessing method instead. ");
      MockPrintWriter mockPrintWriter0 = null;
      try {
        mockPrintWriter0 = new MockPrintWriter(mockFile0, "`d%Yt7sqam");
        fail("Expecting exception: UnsupportedEncodingException");
      
      } catch(Throwable e) {
         //
         // `d%Yt7sqam
         //
         verifyException("org.evosuite.runtime.mock.java.io.MockPrintWriter", e);
      }
  }

  @Test(timeout = 4000)
  public void test30()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      helpFormatter0.setArgName("~LU");
      // Undeclared exception!
      try { 
        helpFormatter0.printHelp("", (Options) null);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // cmdLineSyntax not provided
         //
         verifyException("org.apache.commons.cli.HelpFormatter", e);
      }
  }

  @Test(timeout = 4000)
  public void test31()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      String string0 = "tkz}fc0a@=pXiUQ+mo";
      helpFormatter0.setArgName("tkz}fc0a@=pXiUQ+mo");
      MockPrintWriter mockPrintWriter0 = new MockPrintWriter("tkz}fc0a@=pXiUQ+mo");
      int int0 = 71;
      String string1 = "d(FI}Jb{Z,qOm";
      Options options0 = new Options();
      boolean boolean0 = false;
      // Undeclared exception!
      try { 
        options0.addRequiredOption("--", "usage: ", false, "az-;(c8TgpH)DYJ#;");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The option '--' contains an illegal character : '-'
         //
         verifyException("org.apache.commons.cli.OptionValidator", e);
      }
  }

  @Test(timeout = 4000)
  public void test32()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      helpFormatter0.getArgName();
      String string0 = "['JMP:E4gco";
      Options options0 = new Options();
      boolean boolean0 = true;
      Option option0 = null;
      try {
        option0 = new Option("usage: ", "--", true, "cmdLineSyntax not provided");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The option 'usage: ' contains an illegal character : ':'
         //
         verifyException("org.apache.commons.cli.OptionValidator", e);
      }
  }

  @Test(timeout = 4000)
  public void test33()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      // Undeclared exception!
      try { 
        helpFormatter0.printHelp("--", (Options) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.cli.HelpFormatter", e);
      }
  }

  @Test(timeout = 4000)
  public void test34()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      int int0 = 0;
      String string0 = "7MK@";
      String string1 = "";
      Options options0 = new Options();
      String string2 = "+Y~BLQKhY{vTz& af9_";
      // Undeclared exception!
      try { 
        options0.addOption("+Y~BLQKhY{vTz& af9_", false, "W1m~'k2");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The option '+Y~BLQKhY{vTz& af9_' contains an illegal character : '+'
         //
         verifyException("org.apache.commons.cli.OptionValidator", e);
      }
  }
}
