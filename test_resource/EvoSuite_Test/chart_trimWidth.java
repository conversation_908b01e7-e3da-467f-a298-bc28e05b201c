/*
 * This file was automatically generated by EvoSuite
 * Wed Jun 18 07:21:56 GMT 2025
 */

package org.jfree.chart.ui;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.awt.Dimension;
import java.awt.Point;
import java.awt.Polygon;
import java.awt.Rectangle;
import java.awt.geom.Line2D;
import java.awt.geom.Rectangle2D;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.jfree.chart.ui.LengthAdjustmentType;
import org.jfree.chart.ui.RectangleInsets;
import org.jfree.chart.util.UnitType;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true) 
public class RectangleInsets_ESTest extends RectangleInsets_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets(1.0, 1905.4296283896597, 1905.4296283896597, 1905.4296283896597);
      Rectangle2D.Float rectangle2D_Float0 = new Rectangle2D.Float();
      Rectangle2D.Double rectangle2D_Double0 = (Rectangle2D.Double)rectangleInsets0.createInsetRectangle((Rectangle2D) rectangle2D_Float0, false, false);
      rectangleInsets0.trim(rectangle2D_Double0);
      assertEquals(1.0, rectangle2D_Double0.y, 0.01);
      assertEquals(1905.4296283896597, rectangle2D_Double0.getMinX(), 0.01);
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets((-1.0), (-1.0), (-1945.0), (-1.0));
      Point point0 = new Point((-1385), (-1385));
      Rectangle rectangle0 = new Rectangle(point0);
      rectangleInsets0.trim(rectangle0);
      assertEquals((-1386), rectangle0.x);
      assertEquals(2.0, rectangle0.getWidth(), 0.01);
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets(1.0, 2622.2158113001665, 2622.2158113001665, 1.0);
      Rectangle2D.Double rectangle2D_Double0 = new Rectangle2D.Double(1.0, 2622.2158113001665, 1.0, 2622.2158113001665);
      rectangleInsets0.trim(rectangle2D_Double0);
      assertEquals(1.0, rectangle2D_Double0.getMaxX(), 0.01);
      assertEquals(2622.7158113001665, rectangle2D_Double0.getCenterY(), 0.01);
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      UnitType unitType0 = UnitType.ABSOLUTE;
      RectangleInsets rectangleInsets0 = new RectangleInsets(unitType0, 0.0, (-588.0), 0.0, 0.0);
      double double0 = rectangleInsets0.extendWidth(1034.32115944);
      assertEquals(0.0, rectangleInsets0.getTop(), 0.01);
      assertEquals(0.0, rectangleInsets0.getRight(), 0.01);
      assertEquals(0.0, rectangleInsets0.getBottom(), 0.01);
      assertEquals(446.32115944, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      RectangleInsets rectangleInsets0 = RectangleInsets.ZERO_INSETS;
      Rectangle2D.Double rectangle2D_Double0 = new Rectangle2D.Double(0.0, (-3153.6249230988706), 0.0, 0.0);
      Rectangle2D rectangle2D0 = rectangleInsets0.ZERO_INSETS.createInsetRectangle((Rectangle2D) rectangle2D_Double0, false, true);
      assertTrue(rectangle2D0.equals((Object)rectangle2D_Double0));
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets(0.0, (-6062.976114048), 0.0, 0.0);
      Line2D.Double line2D_Double0 = new Line2D.Double();
      Rectangle2D rectangle2D0 = line2D_Double0.getBounds2D();
      Rectangle2D.Double rectangle2D_Double0 = (Rectangle2D.Double)rectangleInsets0.createOutsetRectangle(rectangle2D0);
      LengthAdjustmentType lengthAdjustmentType0 = LengthAdjustmentType.CONTRACT;
      Rectangle2D.Double rectangle2D_Double1 = (Rectangle2D.Double)rectangleInsets0.createAdjustedRectangle(rectangle2D_Double0, lengthAdjustmentType0, lengthAdjustmentType0);
      assertEquals(0.0, rectangleInsets0.getTop(), 0.01);
      assertEquals((-6062.976114048), rectangle2D_Double0.width, 0.01);
      assertEquals(0.0, rectangleInsets0.getBottom(), 0.01);
      assertEquals(0.0, rectangle2D_Double1.width, 0.01);
      assertTrue(rectangle2D_Double1.equals((Object)rectangle2D0));
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets(0.0, 0.0, 837.19316816, 0.0);
      Point point0 = new Point();
      Dimension dimension0 = new Dimension();
      Rectangle rectangle0 = new Rectangle(point0, dimension0);
      LengthAdjustmentType lengthAdjustmentType0 = LengthAdjustmentType.CONTRACT;
      Rectangle2D.Double rectangle2D_Double0 = (Rectangle2D.Double)rectangleInsets0.createAdjustedRectangle(rectangle0, (LengthAdjustmentType) null, lengthAdjustmentType0);
      assertEquals((-837.19316816), rectangle2D_Double0.height, 0.01);
      assertEquals(0.0, rectangleInsets0.getLeft(), 0.01);
      assertEquals(0.0, rectangleInsets0.getRight(), 0.01);
      assertEquals(0.0, rectangleInsets0.getTop(), 0.01);
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets((-1.0), (-1.0), (-663.9), (-657.87));
      Rectangle rectangle0 = new Rectangle();
      rectangle0.add((-561.157), 1.0);
      LengthAdjustmentType lengthAdjustmentType0 = LengthAdjustmentType.EXPAND;
      Rectangle2D rectangle2D0 = rectangleInsets0.createAdjustedRectangle(rectangle0, lengthAdjustmentType0, lengthAdjustmentType0);
      assertEquals((-609.435), rectangle2D0.getCenterX(), 0.01);
      assertEquals((-330.95), rectangle2D0.getCenterY(), 0.01);
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      UnitType unitType0 = UnitType.RELATIVE;
      RectangleInsets rectangleInsets0 = new RectangleInsets(unitType0, 1718.19374, 1718.19374, 1718.19374, 1718.19374);
      Rectangle rectangle0 = new Rectangle((-1204), 29, 29, 635);
      LengthAdjustmentType lengthAdjustmentType0 = LengthAdjustmentType.CONTRACT;
      LengthAdjustmentType lengthAdjustmentType1 = LengthAdjustmentType.EXPAND;
      Rectangle2D.Double rectangle2D_Double0 = (Rectangle2D.Double)rectangleInsets0.createAdjustedRectangle(rectangle0, lengthAdjustmentType1, lengthAdjustmentType0);
      assertEquals((-1189.5), rectangle2D_Double0.getCenterX(), 0.01);
      assertEquals((-2181471.0497999997), rectangle2D_Double0.height, 0.01);
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets((-1.0), (-1.0), (-663.9), (-657.87));
      rectangleInsets0.hashCode();
      assertEquals((-1.0), rectangleInsets0.getLeft(), 0.01);
      assertEquals((-657.87), rectangleInsets0.getRight(), 0.01);
      assertEquals((-1.0), rectangleInsets0.getTop(), 0.01);
      assertEquals((-663.9), rectangleInsets0.getBottom(), 0.01);
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      UnitType unitType0 = UnitType.RELATIVE;
      RectangleInsets rectangleInsets0 = new RectangleInsets(unitType0, 1718.19374, 1718.19374, 1718.19374, 1718.19374);
      rectangleInsets0.hashCode();
      assertEquals(1718.19374, rectangleInsets0.getTop(), 0.01);
      assertEquals(1718.19374, rectangleInsets0.getLeft(), 0.01);
      assertEquals(1718.19374, rectangleInsets0.getBottom(), 0.01);
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets();
      RectangleInsets rectangleInsets1 = new RectangleInsets((-1.0), 1.0, (-1.0), 1.0);
      boolean boolean0 = rectangleInsets1.equals(rectangleInsets0);
      assertEquals((-1.0), rectangleInsets1.getBottom(), 0.01);
      assertEquals(1.0, rectangleInsets1.getRight(), 0.01);
      assertEquals((-1.0), rectangleInsets1.getTop(), 0.01);
      assertFalse(boolean0);
      assertEquals(1.0, rectangleInsets1.getLeft(), 0.01);
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      RectangleInsets rectangleInsets0 = RectangleInsets.ZERO_INSETS;
      RectangleInsets rectangleInsets1 = new RectangleInsets(0.0, 0.0, 0.0, (-1212.0));
      boolean boolean0 = rectangleInsets1.equals(rectangleInsets0);
      assertEquals((-1212.0), rectangleInsets1.getRight(), 0.01);
      assertEquals(0.0, rectangleInsets1.getLeft(), 0.01);
      assertFalse(boolean0);
      assertFalse(rectangleInsets0.equals((Object)rectangleInsets1));
      assertEquals(0.0, rectangleInsets1.getBottom(), 0.01);
      assertEquals(0.0, rectangleInsets1.getTop(), 0.01);
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets(0.0, 0.0, (-3476.32088), 1.0);
      RectangleInsets rectangleInsets1 = new RectangleInsets();
      boolean boolean0 = rectangleInsets1.equals(rectangleInsets0);
      assertEquals((-3476.32088), rectangleInsets0.getBottom(), 0.01);
      assertEquals(0.0, rectangleInsets0.getTop(), 0.01);
      assertEquals(1.0, rectangleInsets0.getRight(), 0.01);
      assertFalse(boolean0);
      assertEquals(0.0, rectangleInsets0.getLeft(), 0.01);
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets();
      double double0 = rectangleInsets0.trimWidth(1513.014);
      assertEquals(1511.014, double0, 0.01);
      assertEquals(1.0, rectangleInsets0.getTop(), 0.01);
      assertEquals(1.0, rectangleInsets0.getBottom(), 0.01);
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets((-1360.62239), 1718.19374, 1718.19374, 861.1736);
      double double0 = rectangleInsets0.trimWidth(0.0);
      assertEquals((-2579.36734), double0, 0.01);
      assertEquals(1718.19374, rectangleInsets0.getBottom(), 0.01);
      assertEquals((-1360.62239), rectangleInsets0.getTop(), 0.01);
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets(0.0, 0.0, (-3476.32088), 1.0);
      double double0 = rectangleInsets0.trimHeight((-3476.32088));
      assertEquals(0.0, double0, 0.01);
      assertEquals(0.0, rectangleInsets0.getLeft(), 0.01);
      assertEquals(0.0, rectangleInsets0.getTop(), 0.01);
      assertEquals(1.0, rectangleInsets0.getRight(), 0.01);
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets((-1360.62239), 1718.19374, 1718.19374, 861.1736);
      double double0 = rectangleInsets0.trimHeight(1718.19374);
      assertEquals(1718.19374, rectangleInsets0.getLeft(), 0.01);
      assertEquals(861.1736, rectangleInsets0.getRight(), 0.01);
      assertEquals(1360.6223900000002, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      UnitType unitType0 = UnitType.RELATIVE;
      RectangleInsets rectangleInsets0 = new RectangleInsets(unitType0, 783.3530276, 783.3530276, 0.0, 783.3530276);
      double double0 = rectangleInsets0.getTop();
      assertEquals(783.3530276, double0, 0.01);
      assertEquals(783.3530276, rectangleInsets0.getLeft(), 0.01);
      assertEquals(783.3530276, rectangleInsets0.getRight(), 0.01);
      assertEquals(0.0, rectangleInsets0.getBottom(), 0.01);
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets((-1.0), (-1.0), (-663.9), (-657.87));
      double double0 = rectangleInsets0.getTop();
      assertEquals((-1.0), double0, 0.01);
      assertEquals((-663.9), rectangleInsets0.getBottom(), 0.01);
      assertEquals((-1.0), rectangleInsets0.getLeft(), 0.01);
      assertEquals((-657.87), rectangleInsets0.getRight(), 0.01);
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      UnitType unitType0 = UnitType.ABSOLUTE;
      RectangleInsets rectangleInsets0 = new RectangleInsets(unitType0, (-2474.456), 0.0, 0.0, 3004.65434334);
      double double0 = rectangleInsets0.getRight();
      assertEquals((-2474.456), rectangleInsets0.getTop(), 0.01);
      assertEquals(0.0, rectangleInsets0.getLeft(), 0.01);
      assertEquals(3004.65434334, double0, 0.01);
      assertEquals(0.0, rectangleInsets0.getBottom(), 0.01);
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets(0.0, 0.0, 0.0, (-1086.65216781));
      double double0 = rectangleInsets0.getRight();
      assertEquals(0.0, rectangleInsets0.getBottom(), 0.01);
      assertEquals(0.0, rectangleInsets0.getLeft(), 0.01);
      assertEquals((-1086.65216781), double0, 0.01);
      assertEquals(0.0, rectangleInsets0.getTop(), 0.01);
  }

  @Test(timeout = 4000)
  public void test22()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets(1.0, 1905.4296283896597, 1905.4296283896597, 1905.4296283896597);
      double double0 = rectangleInsets0.getLeft();
      assertEquals(1905.4296283896597, rectangleInsets0.getBottom(), 0.01);
      assertEquals(1905.4296283896597, rectangleInsets0.getRight(), 0.01);
      assertEquals(1905.4296283896597, double0, 0.01);
      assertEquals(1.0, rectangleInsets0.getTop(), 0.01);
  }

  @Test(timeout = 4000)
  public void test23()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets((-1.0), (-1.0), (-1945.0), (-1.0));
      double double0 = rectangleInsets0.getLeft();
      assertEquals((-1945.0), rectangleInsets0.getBottom(), 0.01);
      assertEquals((-1.0), double0, 0.01);
      assertEquals((-1.0), rectangleInsets0.getTop(), 0.01);
      assertEquals((-1.0), rectangleInsets0.getRight(), 0.01);
  }

  @Test(timeout = 4000)
  public void test24()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets(0.0, (-6062.976114048), 0.0, 0.0);
      double double0 = rectangleInsets0.getBottom();
      assertEquals((-6062.976114048), rectangleInsets0.getLeft(), 0.01);
      assertEquals(0.0, rectangleInsets0.getTop(), 0.01);
      assertEquals(0.0, rectangleInsets0.getRight(), 0.01);
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test25()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets(1.0, 2622.2158113001665, 2622.2158113001665, 1.0);
      double double0 = rectangleInsets0.getBottom();
      assertEquals(2622.2158113001665, rectangleInsets0.getLeft(), 0.01);
      assertEquals(2622.2158113001665, double0, 0.01);
      assertEquals(1.0, rectangleInsets0.getRight(), 0.01);
      assertEquals(1.0, rectangleInsets0.getTop(), 0.01);
  }

  @Test(timeout = 4000)
  public void test26()  throws Throwable  {
      RectangleInsets rectangleInsets0 = RectangleInsets.ZERO_INSETS;
      double double0 = rectangleInsets0.extendWidth(0.0);
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test27()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets(0.0, 0.0, (-3476.32088), 1.0);
      double double0 = rectangleInsets0.extendWidth(1.0);
      assertEquals(0.0, rectangleInsets0.getTop(), 0.01);
      assertEquals(0.0, rectangleInsets0.getLeft(), 0.01);
      assertEquals((-3476.32088), rectangleInsets0.getBottom(), 0.01);
      assertEquals(2.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test28()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets(0.0, 0.0, 0.0, (-1086.65216781));
      double double0 = rectangleInsets0.extendHeight(0.0);
      assertEquals(0.0, double0, 0.01);
      assertEquals(0.0, rectangleInsets0.getBottom(), 0.01);
      assertEquals(0.0, rectangleInsets0.getTop(), 0.01);
      assertEquals(0.0, rectangleInsets0.getLeft(), 0.01);
      assertEquals((-1086.65216781), rectangleInsets0.getRight(), 0.01);
  }

  @Test(timeout = 4000)
  public void test29()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets();
      double double0 = rectangleInsets0.extendHeight((-133.0));
      assertEquals((-131.0), double0, 0.01);
      assertEquals(1.0, rectangleInsets0.getLeft(), 0.01);
      assertEquals(1.0, rectangleInsets0.getRight(), 0.01);
  }

  @Test(timeout = 4000)
  public void test30()  throws Throwable  {
      RectangleInsets rectangleInsets0 = RectangleInsets.ZERO_INSETS;
      Line2D.Double line2D_Double0 = new Line2D.Double((-625.0), 0.0, 0.0, (-625.0));
      Rectangle2D rectangle2D0 = line2D_Double0.getBounds2D();
      Rectangle2D rectangle2D1 = rectangleInsets0.createOutsetRectangle(rectangle2D0, true, false);
      assertTrue(rectangle2D1.equals((Object)rectangle2D0));
  }

  @Test(timeout = 4000)
  public void test31()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets();
      Rectangle2D.Float rectangle2D_Float0 = new Rectangle2D.Float();
      Rectangle2D.Double rectangle2D_Double0 = (Rectangle2D.Double)rectangleInsets0.createOutsetRectangle((Rectangle2D) rectangle2D_Float0);
      assertEquals((-1.0), rectangle2D_Double0.x, 0.01);
      assertEquals(0.0, rectangle2D_Double0.getCenterX(), 0.01);
      assertEquals((-1.0), rectangle2D_Double0.y, 0.01);
      assertEquals(0.0, rectangle2D_Double0.getCenterY(), 0.01);
  }

  @Test(timeout = 4000)
  public void test32()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets();
      Rectangle2D.Float rectangle2D_Float0 = new Rectangle2D.Float();
      Rectangle2D.Double rectangle2D_Double0 = (Rectangle2D.Double)rectangleInsets0.createInsetRectangle((Rectangle2D) rectangle2D_Float0, true, true);
      assertEquals(1.0, rectangle2D_Double0.x, 0.01);
      assertEquals((-2.0), rectangle2D_Double0.height, 0.01);
      assertEquals(0.0, rectangle2D_Double0.getCenterX(), 0.01);
      assertEquals(0.0, rectangle2D_Double0.getCenterY(), 0.01);
  }

  @Test(timeout = 4000)
  public void test33()  throws Throwable  {
      RectangleInsets rectangleInsets0 = RectangleInsets.ZERO_INSETS;
      Rectangle2D.Float rectangle2D_Float0 = new Rectangle2D.Float();
      rectangle2D_Float0.y = 0.0F;
      rectangle2D_Float0.y = 1.0F;
      Rectangle2D rectangle2D0 = rectangleInsets0.createInsetRectangle((Rectangle2D) rectangle2D_Float0, true, true);
      assertEquals(1.0, rectangle2D0.getMaxY(), 0.01);
      assertEquals(0.0, rectangle2D0.getMaxX(), 0.01);
  }

  @Test(timeout = 4000)
  public void test34()  throws Throwable  {
      RectangleInsets rectangleInsets0 = RectangleInsets.ZERO_INSETS;
      Point point0 = new Point();
      Dimension dimension0 = new Dimension(1671, 0);
      Rectangle rectangle0 = new Rectangle(point0, dimension0);
      Rectangle2D.Float rectangle2D_Float0 = new Rectangle2D.Float();
      Rectangle2D rectangle2D0 = rectangle0.createUnion(rectangle2D_Float0);
      Rectangle2D rectangle2D1 = rectangleInsets0.createInsetRectangle(rectangle2D0, false, false);
      assertTrue(rectangle2D1.equals((Object)rectangle2D0));
  }

  @Test(timeout = 4000)
  public void test35()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets(0.0, (-6062.976114048), 0.0, 0.0);
      Line2D.Double line2D_Double0 = new Line2D.Double();
      Rectangle2D rectangle2D0 = line2D_Double0.getBounds2D();
      Rectangle2D rectangle2D1 = rectangleInsets0.createInsetRectangle(rectangle2D0, true, true);
      assertEquals((-3031.488057024), rectangle2D1.getCenterX(), 0.01);
      assertEquals(0.0, rectangleInsets0.getBottom(), 0.01);
      assertEquals(0.0, rectangleInsets0.getTop(), 0.01);
      assertEquals(0.0, rectangleInsets0.getRight(), 0.01);
      assertEquals(0.0, rectangle2D1.getCenterY(), 0.01);
  }

  @Test(timeout = 4000)
  public void test36()  throws Throwable  {
      RectangleInsets rectangleInsets0 = RectangleInsets.ZERO_INSETS;
      Rectangle2D.Float rectangle2D_Float0 = new Rectangle2D.Float();
      Rectangle2D rectangle2D0 = rectangleInsets0.ZERO_INSETS.createOutsetRectangle((Rectangle2D) rectangle2D_Float0);
      Rectangle2D rectangle2D1 = rectangleInsets0.createInsetRectangle(rectangle2D0);
      assertTrue(rectangle2D1.equals((Object)rectangle2D0));
  }

  @Test(timeout = 4000)
  public void test37()  throws Throwable  {
      UnitType unitType0 = UnitType.RELATIVE;
      RectangleInsets rectangleInsets0 = new RectangleInsets(unitType0, 0.0, 497.255317203861, 0.0, 546.885581);
      Line2D.Float line2D_Float0 = new Line2D.Float(0.0F, (-1036.9F), 0.0F, 679.593F);
      Rectangle2D rectangle2D0 = line2D_Float0.getBounds2D();
      Rectangle2D rectangle2D1 = rectangleInsets0.createInsetRectangle(rectangle2D0, false, false);
      Rectangle2D rectangle2D2 = rectangleInsets0.createInsetRectangle(rectangle2D1);
      assertEquals(497.255317203861, rectangleInsets0.getLeft(), 0.01);
      assertEquals(546.885581, rectangleInsets0.getRight(), 0.01);
      assertEquals(0.0, rectangleInsets0.getBottom(), 0.01);
      assertTrue(rectangle2D2.equals((Object)rectangle2D1));
      assertEquals((-178.65350341796875), rectangle2D2.getCenterY(), 0.01);
  }

  @Test(timeout = 4000)
  public void test38()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets((-1.0), (-1.0), (-663.9), (-657.87));
      Rectangle rectangle0 = new Rectangle();
      Rectangle2D rectangle2D0 = rectangleInsets0.ZERO_INSETS.createInsetRectangle((Rectangle2D) rectangle0);
      Rectangle2D rectangle2D1 = rectangleInsets0.createOutsetRectangle(rectangle2D0, true, true);
      assertEquals((-331.45), rectangle2D1.getCenterY(), 0.01);
      assertEquals((-328.435), rectangle2D1.getCenterX(), 0.01);
  }

  @Test(timeout = 4000)
  public void test39()  throws Throwable  {
      RectangleInsets rectangleInsets0 = RectangleInsets.ZERO_INSETS;
      Dimension dimension0 = new Dimension((-848), (-1839));
      Rectangle rectangle0 = new Rectangle(dimension0);
      Rectangle2D.Double rectangle2D_Double0 = (Rectangle2D.Double)rectangleInsets0.createOutsetRectangle((Rectangle2D) rectangle0);
      Rectangle2D.Double rectangle2D_Double1 = (Rectangle2D.Double)rectangleInsets0.createInsetRectangle((Rectangle2D) rectangle2D_Double0);
      assertEquals((-848.0), rectangle2D_Double0.width, 0.01);
      assertTrue(rectangle2D_Double1.equals((Object)rectangle2D_Double0));
      assertEquals((-1839.0), rectangle2D_Double0.height, 0.01);
  }

  @Test(timeout = 4000)
  public void test40()  throws Throwable  {
      RectangleInsets rectangleInsets0 = RectangleInsets.ZERO_INSETS;
      Rectangle2D.Double rectangle2D_Double0 = new Rectangle2D.Double();
      LengthAdjustmentType lengthAdjustmentType0 = LengthAdjustmentType.CONTRACT;
      Rectangle2D.Double rectangle2D_Double1 = (Rectangle2D.Double)rectangleInsets0.createAdjustedRectangle(rectangle2D_Double0, lengthAdjustmentType0, lengthAdjustmentType0);
      assertEquals(0.0, rectangle2D_Double1.getMaxX(), 0.01);
      assertEquals(0.0, rectangle2D_Double1.width, 0.01);
  }

  @Test(timeout = 4000)
  public void test41()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets(0.0, 0.0, (-3476.32088), 1.0);
      Polygon polygon0 = new Polygon();
      Rectangle rectangle0 = polygon0.getBounds();
      LengthAdjustmentType lengthAdjustmentType0 = LengthAdjustmentType.EXPAND;
      Rectangle2D.Double rectangle2D_Double0 = (Rectangle2D.Double)rectangleInsets0.createAdjustedRectangle(rectangle0, lengthAdjustmentType0, lengthAdjustmentType0);
      assertEquals(0.5, rectangle2D_Double0.getCenterX(), 0.01);
      assertEquals(0.0, rectangle2D_Double0.y, 0.01);
      assertEquals(0.0, rectangle2D_Double0.x, 0.01);
      assertEquals((-1738.16044), rectangle2D_Double0.getCenterY(), 0.01);
      assertEquals(0.0, rectangleInsets0.getTop(), 0.01);
      assertEquals(0.0, rectangleInsets0.getLeft(), 0.01);
  }

  @Test(timeout = 4000)
  public void test42()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets((-1.0), (-1.0), (-1945.0), (-1.0));
      double double0 = rectangleInsets0.calculateTopOutset(0.0);
      assertEquals((-1.0), double0, 0.01);
      assertEquals((-1.0), rectangleInsets0.getLeft(), 0.01);
      assertEquals((-1.0), rectangleInsets0.getRight(), 0.01);
      assertEquals((-1945.0), rectangleInsets0.getBottom(), 0.01);
  }

  @Test(timeout = 4000)
  public void test43()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets(0.0, 0.0, (-3476.32088), 1.0);
      double double0 = rectangleInsets0.calculateTopInset(2624.4687);
      assertEquals(1.0, rectangleInsets0.getRight(), 0.01);
      assertEquals(0.0, rectangleInsets0.getLeft(), 0.01);
      assertEquals((-3476.32088), rectangleInsets0.getBottom(), 0.01);
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test44()  throws Throwable  {
      RectangleInsets rectangleInsets0 = RectangleInsets.ZERO_INSETS;
      double double0 = rectangleInsets0.calculateRightOutset((-1.0));
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test45()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets(0.0, 0.0, 0.0, (-1086.65216781));
      double double0 = rectangleInsets0.calculateRightOutset((-1535.6961965532244));
      assertEquals(0.0, rectangleInsets0.getLeft(), 0.01);
      assertEquals(0.0, rectangleInsets0.getTop(), 0.01);
      assertEquals(0.0, rectangleInsets0.getBottom(), 0.01);
      assertEquals((-1086.65216781), double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test46()  throws Throwable  {
      UnitType unitType0 = UnitType.ABSOLUTE;
      RectangleInsets rectangleInsets0 = new RectangleInsets(unitType0, 0.0, 0.0, 0.0, (-657.87));
      double double0 = rectangleInsets0.calculateRightInset(304.402275109);
      assertEquals((-657.87), double0, 0.01);
      assertEquals(0.0, rectangleInsets0.getBottom(), 0.01);
      assertEquals(0.0, rectangleInsets0.getTop(), 0.01);
      assertEquals(0.0, rectangleInsets0.getLeft(), 0.01);
  }

  @Test(timeout = 4000)
  public void test47()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets();
      double double0 = rectangleInsets0.calculateLeftOutset(0.0);
      assertEquals(1.0, rectangleInsets0.getRight(), 0.01);
      assertEquals(1.0, rectangleInsets0.getBottom(), 0.01);
      assertEquals(1.0, double0, 0.01);
      assertEquals(1.0, rectangleInsets0.getTop(), 0.01);
  }

  @Test(timeout = 4000)
  public void test48()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets((-1.0), (-1.0), (-663.9), (-657.87));
      double double0 = rectangleInsets0.calculateLeftOutset((-243.34));
      assertEquals((-1.0), rectangleInsets0.getTop(), 0.01);
      assertEquals((-663.9), rectangleInsets0.getBottom(), 0.01);
      assertEquals((-1.0), double0, 0.01);
      assertEquals((-657.87), rectangleInsets0.getRight(), 0.01);
  }

  @Test(timeout = 4000)
  public void test49()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets((-1360.62239), 1718.19374, 1718.19374, 861.1736);
      double double0 = rectangleInsets0.calculateLeftInset(0.0);
      assertEquals(861.1736, rectangleInsets0.getRight(), 0.01);
      assertEquals((-1360.62239), rectangleInsets0.getTop(), 0.01);
      assertEquals(1718.19374, rectangleInsets0.getBottom(), 0.01);
      assertEquals(1718.19374, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test50()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets((-1287.8), (-1287.8), (-1287.8), 0.0);
      double double0 = rectangleInsets0.calculateLeftInset((-911.0));
      assertEquals((-1287.8), double0, 0.01);
      assertEquals((-1287.8), rectangleInsets0.getBottom(), 0.01);
      assertEquals(0.0, rectangleInsets0.getRight(), 0.01);
      assertEquals((-1287.8), rectangleInsets0.getTop(), 0.01);
  }

  @Test(timeout = 4000)
  public void test51()  throws Throwable  {
      UnitType unitType0 = UnitType.ABSOLUTE;
      RectangleInsets rectangleInsets0 = new RectangleInsets(unitType0, (-2474.456), 0.0, 0.0, 3004.65434334);
      double double0 = rectangleInsets0.calculateBottomOutset(645.8);
      assertEquals(0.0, double0, 0.01);
      assertEquals(0.0, rectangleInsets0.getLeft(), 0.01);
      assertEquals(3004.65434334, rectangleInsets0.getRight(), 0.01);
      assertEquals((-2474.456), rectangleInsets0.getTop(), 0.01);
  }

  @Test(timeout = 4000)
  public void test52()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets((-1.0), (-1.0), (-1945.0), (-1.0));
      double double0 = rectangleInsets0.calculateBottomOutset(3062.8);
      assertEquals((-1945.0), double0, 0.01);
      assertEquals((-1.0), rectangleInsets0.getTop(), 0.01);
      assertEquals((-1.0), rectangleInsets0.getRight(), 0.01);
      assertEquals((-1.0), rectangleInsets0.getLeft(), 0.01);
  }

  @Test(timeout = 4000)
  public void test53()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets((-1.0), (-1.0), (-1945.0), (-1.0));
      double double0 = rectangleInsets0.calculateBottomInset((-1945.0));
      assertEquals((-1.0), rectangleInsets0.getLeft(), 0.01);
      assertEquals((-1945.0), double0, 0.01);
      assertEquals((-1.0), rectangleInsets0.getRight(), 0.01);
      assertEquals((-1.0), rectangleInsets0.getTop(), 0.01);
  }

  @Test(timeout = 4000)
  public void test54()  throws Throwable  {
      RectangleInsets rectangleInsets0 = RectangleInsets.ZERO_INSETS;
      // Undeclared exception!
      try { 
        rectangleInsets0.ZERO_INSETS.trim((Rectangle2D) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.jfree.chart.ui.RectangleInsets", e);
      }
  }

  @Test(timeout = 4000)
  public void test55()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets(0.0, 0.0, (-3476.32088), 1.0);
      double double0 = rectangleInsets0.calculateRightOutset((-3476.32088));
      assertEquals(0.0, rectangleInsets0.getLeft(), 0.01);
      assertEquals(0.0, rectangleInsets0.getTop(), 0.01);
      assertEquals(1.0, double0, 0.01);
      assertEquals((-3476.32088), rectangleInsets0.getBottom(), 0.01);
  }

  @Test(timeout = 4000)
  public void test56()  throws Throwable  {
      UnitType unitType0 = UnitType.RELATIVE;
      RectangleInsets rectangleInsets0 = new RectangleInsets(unitType0, 0.0, 497.255317203861, 0.0, 546.885581);
      double double0 = rectangleInsets0.calculateRightOutset((-1639.9568319501036));
      assertEquals(859.7771848464872, double0, 0.01);
      assertEquals(0.0, rectangleInsets0.getTop(), 0.01);
      assertEquals(0.0, rectangleInsets0.getBottom(), 0.01);
  }

  @Test(timeout = 4000)
  public void test57()  throws Throwable  {
      UnitType unitType0 = UnitType.RELATIVE;
      RectangleInsets rectangleInsets0 = new RectangleInsets(unitType0, 2218.0134, 0.0, 2218.0134, 0.0);
      double double0 = rectangleInsets0.calculateRightInset(0.0);
      assertEquals(0.0, rectangleInsets0.getLeft(), 0.01);
      assertEquals(0.0, rectangleInsets0.getRight(), 0.01);
      assertEquals(0.0, double0, 0.01);
      assertEquals(2218.0134, rectangleInsets0.getBottom(), 0.01);
      assertEquals(2218.0134, rectangleInsets0.getTop(), 0.01);
  }

  @Test(timeout = 4000)
  public void test58()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets();
      double double0 = rectangleInsets0.calculateRightInset((-1.0));
      assertEquals(1.0, double0, 0.01);
      assertEquals(1.0, rectangleInsets0.getLeft(), 0.01);
      assertEquals(1.0, rectangleInsets0.getBottom(), 0.01);
      assertEquals(1.0, rectangleInsets0.getTop(), 0.01);
  }

  @Test(timeout = 4000)
  public void test59()  throws Throwable  {
      UnitType unitType0 = UnitType.RELATIVE;
      RectangleInsets rectangleInsets0 = new RectangleInsets(unitType0, 2950.2359874, 0.0, 386.3919835, 0.0);
      double double0 = rectangleInsets0.calculateLeftOutset(0);
      assertEquals(386.3919835, rectangleInsets0.getBottom(), 0.01);
      assertEquals(0.0, double0, 0.01);
      assertEquals(0.0, rectangleInsets0.getRight(), 0.01);
      assertEquals(2950.2359874, rectangleInsets0.getTop(), 0.01);
  }

  @Test(timeout = 4000)
  public void test60()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets(0.0, 0.0, 837.19316816, 0.0);
      double double0 = rectangleInsets0.calculateLeftInset((-1311.919216543419));
      assertEquals(837.19316816, rectangleInsets0.getBottom(), 0.01);
      assertEquals(0.0, double0, 0.01);
      assertEquals(0.0, rectangleInsets0.getRight(), 0.01);
      assertEquals(0.0, rectangleInsets0.getTop(), 0.01);
  }

  @Test(timeout = 4000)
  public void test61()  throws Throwable  {
      UnitType unitType0 = UnitType.RELATIVE;
      RectangleInsets rectangleInsets0 = new RectangleInsets(unitType0, 2950.2359874, 0.0, 386.3919835, 0.0);
      double double0 = rectangleInsets0.calculateLeftInset(0.0);
      assertEquals(0.0, rectangleInsets0.getRight(), 0.01);
      assertEquals(0.0, rectangleInsets0.getLeft(), 0.01);
      assertEquals(0.0, double0, 0.01);
      assertEquals(386.3919835, rectangleInsets0.getBottom(), 0.01);
      assertEquals(2950.2359874, rectangleInsets0.getTop(), 0.01);
  }

  @Test(timeout = 4000)
  public void test62()  throws Throwable  {
      UnitType unitType0 = UnitType.RELATIVE;
      RectangleInsets rectangleInsets0 = new RectangleInsets(unitType0, (-3475.32088), 1.0, (-3476.32088), (-2517.1912667901183));
      double double0 = rectangleInsets0.calculateBottomOutset((-3475.32088));
      assertEquals(1737.66044, double0, 0.01);
      assertEquals(1.0, rectangleInsets0.getLeft(), 0.01);
      assertEquals((-2517.1912667901183), rectangleInsets0.getRight(), 0.01);
  }

  @Test(timeout = 4000)
  public void test63()  throws Throwable  {
      UnitType unitType0 = UnitType.RELATIVE;
      RectangleInsets rectangleInsets0 = new RectangleInsets(unitType0, 1718.19374, 1718.19374, 1718.19374, 1718.19374);
      double double0 = rectangleInsets0.calculateBottomInset(0.0F);
      assertEquals(1718.19374, rectangleInsets0.getLeft(), 0.01);
      assertEquals(0.0, double0, 0.01);
      assertEquals(1718.19374, rectangleInsets0.getRight(), 0.01);
      assertEquals(1718.19374, rectangleInsets0.getTop(), 0.01);
  }

  @Test(timeout = 4000)
  public void test64()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets();
      double double0 = rectangleInsets0.calculateBottomInset((-3242.2596133501));
      assertEquals(1.0, rectangleInsets0.getRight(), 0.01);
      assertEquals(1.0, rectangleInsets0.getTop(), 0.01);
      assertEquals(1.0, double0, 0.01);
      assertEquals(1.0, rectangleInsets0.getLeft(), 0.01);
  }

  @Test(timeout = 4000)
  public void test65()  throws Throwable  {
      UnitType unitType0 = UnitType.RELATIVE;
      RectangleInsets rectangleInsets0 = new RectangleInsets(unitType0, 0.0, 497.255317203861, 0.0, 546.885581);
      double double0 = rectangleInsets0.calculateTopOutset(679.593F);
      assertEquals(546.885581, rectangleInsets0.getRight(), 0.01);
      assertEquals(497.255317203861, rectangleInsets0.getLeft(), 0.01);
      assertEquals(0.0, double0, 0.01);
      assertEquals(0.0, rectangleInsets0.getBottom(), 0.01);
  }

  @Test(timeout = 4000)
  public void test66()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets(1.0, 1905.4296283896597, 1905.4296283896597, 1905.4296283896597);
      double double0 = rectangleInsets0.calculateTopOutset(1905.4296283896597);
      assertEquals(1905.4296283896597, rectangleInsets0.getRight(), 0.01);
      assertEquals(1905.4296283896597, rectangleInsets0.getBottom(), 0.01);
      assertEquals(1.0, double0, 0.01);
      assertEquals(1905.4296283896597, rectangleInsets0.getLeft(), 0.01);
  }

  @Test(timeout = 4000)
  public void test67()  throws Throwable  {
      UnitType unitType0 = UnitType.RELATIVE;
      RectangleInsets rectangleInsets0 = new RectangleInsets(unitType0, 2218.0134, 0.0, 2218.0134, 0.0);
      double double0 = rectangleInsets0.calculateTopInset((-401.964));
      assertEquals(0.0, rectangleInsets0.getLeft(), 0.01);
      assertEquals(0.0, rectangleInsets0.getRight(), 0.01);
      assertEquals((-891561.5383175999), double0, 0.01);
      assertEquals(2218.0134, rectangleInsets0.getBottom(), 0.01);
  }

  @Test(timeout = 4000)
  public void test68()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets(1.0, 2622.2158113001665, 2622.2158113001665, 1.0);
      double double0 = rectangleInsets0.calculateTopInset(0.0);
      assertEquals(1.0, double0, 0.01);
      assertEquals(1.0, rectangleInsets0.getRight(), 0.01);
      assertEquals(2622.2158113001665, rectangleInsets0.getBottom(), 0.01);
      assertEquals(2622.2158113001665, rectangleInsets0.getLeft(), 0.01);
  }

  @Test(timeout = 4000)
  public void test69()  throws Throwable  {
      UnitType unitType0 = UnitType.RELATIVE;
      RectangleInsets rectangleInsets0 = new RectangleInsets(unitType0, (-3059.137), (-3059.137), (-3059.137), (-1027.232078));
      Rectangle2D.Float rectangle2D_Float0 = new Rectangle2D.Float(0.0F, 1186.076F, 0.0F, 0.0F);
      Rectangle2D.Double rectangle2D_Double0 = (Rectangle2D.Double)rectangleInsets0.createOutsetRectangle((Rectangle2D) rectangle2D_Float0, false, true);
      assertEquals((-3059.137), rectangleInsets0.getTop(), 0.01);
      assertEquals(0.0, rectangle2D_Double0.getCenterX(), 0.01);
      assertEquals((-3059.137), rectangleInsets0.getLeft(), 0.01);
      assertEquals((-3059.137), rectangleInsets0.getBottom(), 0.01);
      assertEquals(0.0, rectangle2D_Double0.height, 0.01);
      assertEquals((-1027.232078), rectangleInsets0.getRight(), 0.01);
      assertEquals(1186.0760498046875, rectangle2D_Double0.y, 0.01);
  }

  @Test(timeout = 4000)
  public void test70()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets();
      // Undeclared exception!
      try { 
        rectangleInsets0.createOutsetRectangle((Rectangle2D) null, true, true);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Null 'base' argument.
         //
         verifyException("org.jfree.chart.ui.RectangleInsets", e);
      }
  }

  @Test(timeout = 4000)
  public void test71()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets();
      // Undeclared exception!
      try { 
        rectangleInsets0.createInsetRectangle((Rectangle2D) null, false, false);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Null 'base' argument.
         //
         verifyException("org.jfree.chart.ui.RectangleInsets", e);
      }
  }

  @Test(timeout = 4000)
  public void test72()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets();
      Rectangle rectangle0 = new Rectangle();
      Rectangle2D.Double rectangle2D_Double0 = (Rectangle2D.Double)rectangleInsets0.createOutsetRectangle((Rectangle2D) rectangle0, false, false);
      assertEquals(0.0, rectangle2D_Double0.height, 0.01);
      assertEquals(1.0, rectangleInsets0.getRight(), 0.01);
      assertEquals(1.0, rectangleInsets0.getTop(), 0.01);
      assertEquals(1.0, rectangleInsets0.getBottom(), 0.01);
      assertEquals(0.0, rectangle2D_Double0.y, 0.01);
      assertEquals(0.0, rectangle2D_Double0.width, 0.01);
      assertEquals(0.0, rectangle2D_Double0.x, 0.01);
      assertEquals(1.0, rectangleInsets0.getLeft(), 0.01);
  }

  @Test(timeout = 4000)
  public void test73()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets();
      // Undeclared exception!
      try { 
        rectangleInsets0.createOutsetRectangle((Rectangle2D) null);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Null 'base' argument.
         //
         verifyException("org.jfree.chart.ui.RectangleInsets", e);
      }
  }

  @Test(timeout = 4000)
  public void test74()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets();
      // Undeclared exception!
      try { 
        rectangleInsets0.createInsetRectangle((Rectangle2D) null);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Null 'base' argument.
         //
         verifyException("org.jfree.chart.ui.RectangleInsets", e);
      }
  }

  @Test(timeout = 4000)
  public void test75()  throws Throwable  {
      UnitType unitType0 = UnitType.RELATIVE;
      RectangleInsets rectangleInsets0 = new RectangleInsets(unitType0, 1718.19374, 1718.19374, 1718.19374, 1718.19374);
      Rectangle rectangle0 = new Rectangle((-1204), 29, 29, 635);
      LengthAdjustmentType lengthAdjustmentType0 = LengthAdjustmentType.CONTRACT;
      Rectangle2D.Double rectangle2D_Double0 = (Rectangle2D.Double)rectangleInsets0.createAdjustedRectangle(rectangle0, lengthAdjustmentType0, lengthAdjustmentType0);
      assertEquals((-99626.23692), rectangle2D_Double0.width, 0.01);
      assertEquals((-1189.5), rectangle2D_Double0.getCenterX(), 0.01);
      assertEquals((-2181471.0497999997), rectangle2D_Double0.height, 0.01);
  }

  @Test(timeout = 4000)
  public void test76()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets();
      Rectangle rectangle0 = new Rectangle();
      LengthAdjustmentType lengthAdjustmentType0 = LengthAdjustmentType.EXPAND;
      Rectangle2D.Double rectangle2D_Double0 = (Rectangle2D.Double)rectangleInsets0.createAdjustedRectangle(rectangle0, lengthAdjustmentType0, lengthAdjustmentType0);
      assertEquals(0.0, rectangle2D_Double0.getCenterX(), 0.01);
      assertEquals((-1.0), rectangle2D_Double0.x, 0.01);
      assertEquals(1.0, rectangle2D_Double0.getMaxY(), 0.01);
      assertEquals(0.0, rectangle2D_Double0.getCenterY(), 0.01);
  }

  @Test(timeout = 4000)
  public void test77()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets();
      LengthAdjustmentType lengthAdjustmentType0 = LengthAdjustmentType.CONTRACT;
      // Undeclared exception!
      try { 
        rectangleInsets0.createAdjustedRectangle((Rectangle2D) null, lengthAdjustmentType0, lengthAdjustmentType0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Null 'base' argument.
         //
         verifyException("org.jfree.chart.ui.RectangleInsets", e);
      }
  }

  @Test(timeout = 4000)
  public void test78()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets(0.0, 0.0, (-3476.32088), 1.0);
      Rectangle2D.Float rectangle2D_Float0 = new Rectangle2D.Float(0.0F, 0.0F, 0.0F, 3924.99F);
      Rectangle2D rectangle2D0 = rectangleInsets0.ZERO_INSETS.createAdjustedRectangle(rectangle2D_Float0, (LengthAdjustmentType) null, (LengthAdjustmentType) null);
      Rectangle2D rectangle2D1 = rectangleInsets0.createOutsetRectangle(rectangle2D0, true, false);
      assertEquals((-3476.32088), rectangleInsets0.getBottom(), 0.01);
      assertEquals(0.0, rectangleInsets0.getLeft(), 0.01);
      assertEquals(0.0, rectangleInsets0.getTop(), 0.01);
      assertEquals(0.5, rectangle2D1.getCenterX(), 0.01);
      assertEquals(1962.4949951171875, rectangle2D1.getCenterY(), 0.01);
  }

  @Test(timeout = 4000)
  public void test79()  throws Throwable  {
      RectangleInsets rectangleInsets0 = RectangleInsets.ZERO_INSETS;
      rectangleInsets0.hashCode();
  }

  @Test(timeout = 4000)
  public void test80()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets();
      RectangleInsets rectangleInsets1 = new RectangleInsets((-1.0), 1.0, 1780.754, 1.0);
      boolean boolean0 = rectangleInsets0.equals(rectangleInsets1);
      assertEquals((-1.0), rectangleInsets1.getTop(), 0.01);
      assertEquals(1.0, rectangleInsets1.getLeft(), 0.01);
      assertEquals(1780.754, rectangleInsets1.getBottom(), 0.01);
      assertEquals(1.0, rectangleInsets1.getRight(), 0.01);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test81()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets();
      RectangleInsets rectangleInsets1 = new RectangleInsets(1.0, 1.0, 1.0, (-493.592775385));
      boolean boolean0 = rectangleInsets0.equals(rectangleInsets1);
      assertEquals(1.0, rectangleInsets1.getBottom(), 0.01);
      assertEquals(1.0, rectangleInsets1.getLeft(), 0.01);
      assertFalse(rectangleInsets1.equals((Object)rectangleInsets0));
      assertFalse(boolean0);
      assertEquals(1.0, rectangleInsets1.getTop(), 0.01);
      assertEquals(1.0, rectangleInsets0.getRight(), 0.01);
  }

  @Test(timeout = 4000)
  public void test82()  throws Throwable  {
      RectangleInsets rectangleInsets0 = RectangleInsets.ZERO_INSETS;
      RectangleInsets rectangleInsets1 = new RectangleInsets();
      boolean boolean0 = rectangleInsets0.equals(rectangleInsets1);
      assertEquals(1.0, rectangleInsets1.getLeft(), 0.01);
      assertEquals(1.0, rectangleInsets1.getTop(), 0.01);
      assertEquals(1.0, rectangleInsets1.getRight(), 0.01);
      assertFalse(boolean0);
      assertEquals(1.0, rectangleInsets1.getBottom(), 0.01);
  }

  @Test(timeout = 4000)
  public void test83()  throws Throwable  {
      RectangleInsets rectangleInsets0 = RectangleInsets.ZERO_INSETS;
      UnitType unitType0 = UnitType.RELATIVE;
      RectangleInsets rectangleInsets1 = new RectangleInsets(unitType0, 1.0, 1.0, 1.0, 1.0);
      boolean boolean0 = rectangleInsets0.equals(rectangleInsets1);
      assertEquals(1.0, rectangleInsets1.getTop(), 0.01);
      assertEquals(1.0, rectangleInsets1.getLeft(), 0.01);
      assertEquals(1.0, rectangleInsets1.getBottom(), 0.01);
      assertEquals(1.0, rectangleInsets1.getRight(), 0.01);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test84()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets();
      Object object0 = new Object();
      boolean boolean0 = rectangleInsets0.equals(object0);
      assertEquals(1.0, rectangleInsets0.getRight(), 0.01);
      assertEquals(1.0, rectangleInsets0.getTop(), 0.01);
      assertFalse(boolean0);
      assertEquals(1.0, rectangleInsets0.getBottom(), 0.01);
      assertEquals(1.0, rectangleInsets0.getLeft(), 0.01);
  }

  @Test(timeout = 4000)
  public void test85()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets();
      boolean boolean0 = rectangleInsets0.equals(rectangleInsets0);
      assertTrue(boolean0);
      assertEquals(1.0, rectangleInsets0.getRight(), 0.01);
      assertEquals(1.0, rectangleInsets0.getBottom(), 0.01);
      assertEquals(1.0, rectangleInsets0.getTop(), 0.01);
      assertEquals(1.0, rectangleInsets0.getLeft(), 0.01);
  }

  @Test(timeout = 4000)
  public void test86()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets();
      RectangleInsets rectangleInsets1 = new RectangleInsets();
      boolean boolean0 = rectangleInsets1.equals(rectangleInsets0);
      assertEquals(1.0, rectangleInsets1.getTop(), 0.01);
      assertEquals(1.0, rectangleInsets1.getLeft(), 0.01);
      assertTrue(boolean0);
      assertEquals(1.0, rectangleInsets1.getBottom(), 0.01);
      assertEquals(1.0, rectangleInsets1.getRight(), 0.01);
  }

  @Test(timeout = 4000)
  public void test87()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets();
      rectangleInsets0.getUnitType();
      assertEquals(1.0, rectangleInsets0.getLeft(), 0.01);
      assertEquals(1.0, rectangleInsets0.getBottom(), 0.01);
      assertEquals(1.0, rectangleInsets0.getTop(), 0.01);
      assertEquals(1.0, rectangleInsets0.getRight(), 0.01);
  }

  @Test(timeout = 4000)
  public void test88()  throws Throwable  {
      RectangleInsets rectangleInsets0 = RectangleInsets.ZERO_INSETS;
      double double0 = rectangleInsets0.trimWidth(0);
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test89()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets(1.0, 2622.2158113001665, 2622.2158113001665, 1.0);
      double double0 = rectangleInsets0.extendHeight(0.0);
      assertEquals(2622.2158113001665, rectangleInsets0.getLeft(), 0.01);
      assertEquals(1.0, rectangleInsets0.getRight(), 0.01);
      assertEquals(2623.2158113001665, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test90()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets(0.0, 0.0, (-3476.32088), 1.0);
      double double0 = rectangleInsets0.getBottom();
      assertEquals(0.0, rectangleInsets0.getTop(), 0.01);
      assertEquals((-3476.32088), double0, 0.01);
      assertEquals(0.0, rectangleInsets0.getLeft(), 0.01);
      assertEquals(1.0, rectangleInsets0.getRight(), 0.01);
  }

  @Test(timeout = 4000)
  public void test91()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets();
      double double0 = rectangleInsets0.trimHeight(1.0);
      assertEquals((-1.0), double0, 0.01);
      assertEquals(1.0, rectangleInsets0.getRight(), 0.01);
      assertEquals(1.0, rectangleInsets0.getLeft(), 0.01);
  }

  @Test(timeout = 4000)
  public void test92()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets(0.0, 0.0, (-3476.32088), 1.0);
      double double0 = rectangleInsets0.extendWidth((-3476.32088));
      assertEquals((-3475.32088), double0, 0.01);
      assertEquals(0.0, rectangleInsets0.getLeft(), 0.01);
      assertEquals((-3476.32088), rectangleInsets0.getBottom(), 0.01);
      assertEquals(0.0, rectangleInsets0.getTop(), 0.01);
  }

  @Test(timeout = 4000)
  public void test93()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets();
      Rectangle2D.Float rectangle2D_Float0 = new Rectangle2D.Float();
      Rectangle rectangle0 = rectangle2D_Float0.getBounds();
      Rectangle2D.Double rectangle2D_Double0 = (Rectangle2D.Double)rectangleInsets0.createInsetRectangle((Rectangle2D) rectangle0);
      assertEquals((-2.0), rectangle2D_Double0.height, 0.01);
      assertEquals(0.0, rectangle2D_Double0.getCenterY(), 0.01);
      assertEquals(0.0, rectangle2D_Double0.getCenterX(), 0.01);
      assertEquals((-1.0), rectangle2D_Double0.getMaxX(), 0.01);
  }

  @Test(timeout = 4000)
  public void test94()  throws Throwable  {
      UnitType unitType0 = UnitType.RELATIVE;
      RectangleInsets rectangleInsets0 = new RectangleInsets(unitType0, 2218.0134, 0.0, 2218.0134, 0.0);
      double double0 = rectangleInsets0.getRight();
      assertEquals(2218.0134, rectangleInsets0.getBottom(), 0.01);
      assertEquals(2218.0134, rectangleInsets0.getTop(), 0.01);
      assertEquals(0.0, double0, 0.01);
      assertEquals(0.0, rectangleInsets0.getLeft(), 0.01);
  }

  @Test(timeout = 4000)
  public void test95()  throws Throwable  {
      UnitType unitType0 = UnitType.RELATIVE;
      RectangleInsets rectangleInsets0 = new RectangleInsets(unitType0, 2218.0134, 0.0, 2218.0134, 0.0);
      double double0 = rectangleInsets0.getLeft();
      assertEquals(2218.0134, rectangleInsets0.getBottom(), 0.01);
      assertEquals(0.0, double0, 0.01);
      assertEquals(2218.0134, rectangleInsets0.getTop(), 0.01);
      assertEquals(0.0, rectangleInsets0.getRight(), 0.01);
  }

  @Test(timeout = 4000)
  public void test96()  throws Throwable  {
      UnitType unitType0 = UnitType.RELATIVE;
      RectangleInsets rectangleInsets0 = new RectangleInsets(unitType0, 2670.113662936206, 2670.113662936206, 2670.113662936206, 2670.113662936206);
      Rectangle2D.Double rectangle2D_Double0 = new Rectangle2D.Double(1806.694567, 0.0, 1806.694567, 2670.113662936206);
      Rectangle2D.Double rectangle2D_Double1 = (Rectangle2D.Double)rectangleInsets0.createOutsetRectangle((Rectangle2D) rectangle2D_Double0);
      assertEquals(1335.3068782913576, rectangle2D_Double1.y, 0.01);
      assertEquals(1335.056831468103, rectangle2D_Double1.getCenterY(), 0.01);
      assertEquals(2710.0418505000002, rectangle2D_Double1.getCenterX(), 0.01);
  }

  @Test(timeout = 4000)
  public void test97()  throws Throwable  {
      UnitType unitType0 = UnitType.RELATIVE;
      RectangleInsets rectangleInsets0 = new RectangleInsets(unitType0, 2670.113662936206, 2670.113662936206, 2670.113662936206, 2670.113662936206);
      String string0 = rectangleInsets0.toString();
      assertEquals("RectangleInsets[t=2670.113662936206,l=2670.113662936206,b=2670.113662936206,r=2670.113662936206]", string0);
  }

  @Test(timeout = 4000)
  public void test98()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets(0.0, 0.0, (-3476.32088), 1.0);
      double double0 = rectangleInsets0.getTop();
      assertEquals(1.0, rectangleInsets0.getRight(), 0.01);
      assertEquals((-3476.32088), rectangleInsets0.getBottom(), 0.01);
      assertEquals(0.0, rectangleInsets0.getLeft(), 0.01);
      assertEquals(0.0, double0, 0.01);
  }
}
