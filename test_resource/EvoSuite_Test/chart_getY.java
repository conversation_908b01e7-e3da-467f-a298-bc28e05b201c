/*
 * This file was automatically generated by EvoSuite
 * Wed Jun 18 07:27:01 GMT 2025
 */

package org.jfree.chart.renderer;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.awt.geom.Point2D;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.jfree.chart.renderer.Outlier;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true) 
public class Outlier_ESTest extends Outlier_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      Outlier outlier0 = new Outlier(0.0, 130.00978, (-112.5));
      Outlier outlier1 = new Outlier((-966.48479), (-966.48479), (-112.5));
      Point2D.Float point2D_Float0 = new Point2D.Float(0.0F, (-2464.619F));
      outlier1.setPoint(point2D_Float0);
      outlier1.setRadius(1);
      outlier0.setPoint(point2D_Float0);
      boolean boolean0 = outlier0.equals(outlier1);
      assertEquals(1.0, outlier1.getRadius(), 0.01);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      Outlier outlier0 = new Outlier(0.0, 0.0, 0.0);
      boolean boolean0 = outlier0.overlaps(outlier0);
      assertEquals(0.0, outlier0.getX(), 0.01);
      assertTrue(boolean0);
      assertEquals(0.0, outlier0.getRadius(), 0.01);
      assertEquals(0.0, outlier0.getY(), 0.01);
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      Outlier outlier0 = new Outlier((-1055.056618650211), (-1055.056618650211), (-1055.056618650211));
      Outlier outlier1 = new Outlier((-1055.056618650211), (-1.0), (-1.0));
      int int0 = outlier0.compareTo(outlier1);
      assertEquals(1, int0);
      assertEquals((-1054.056618650211), outlier1.getX(), 0.01);
      assertEquals(0.0, outlier1.getY(), 0.01);
      assertEquals((-1.0), outlier1.getRadius(), 0.01);
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      Outlier outlier0 = new Outlier((-1416.5), 1.0, (-3943.24));
      Outlier outlier1 = new Outlier(2526.74, 2526.74, 0.0);
      int int0 = outlier0.compareTo(outlier1);
      assertEquals(2526.74, outlier1.getY(), 0.01);
      assertEquals(1, int0);
      assertEquals(0.0, outlier1.getRadius(), 0.01);
      assertEquals(2526.74, outlier0.getX(), 0.01);
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      Outlier outlier0 = new Outlier(2280.0, 0.0, 0.0);
      double double0 = outlier0.getY();
      assertEquals(0.0, double0, 0.01);
      assertEquals(2280.0, outlier0.getX(), 0.01);
      assertEquals(0.0, outlier0.getRadius(), 0.01);
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      Outlier outlier0 = new Outlier(1.1, 0.0, 1.1);
      double double0 = outlier0.getY();
      assertEquals(1.1, outlier0.getRadius(), 0.01);
      assertEquals((-1.1), double0, 0.01);
      assertEquals(0.0, outlier0.getX(), 0.01);
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      Outlier outlier0 = new Outlier(2280.0, 0.0, 0.0);
      double double0 = outlier0.getX();
      assertEquals(2280.0, double0, 0.01);
      assertEquals(0.0, outlier0.getY(), 0.01);
      assertEquals(0.0, outlier0.getRadius(), 0.01);
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      Outlier outlier0 = new Outlier((-1307.31), 1431.08, 1431.08);
      double double0 = outlier0.getX();
      assertEquals((-2738.39), double0, 0.01);
      assertEquals(0.0, outlier0.getY(), 0.01);
      assertEquals(1431.08, outlier0.getRadius(), 0.01);
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      Outlier outlier0 = new Outlier(0.0, 0.0, (-2.3100000000000005));
      double double0 = outlier0.getRadius();
      assertEquals(2.3100000000000005, outlier0.getY(), 0.01);
      assertEquals(2.3100000000000005, outlier0.getX(), 0.01);
      assertEquals((-2.3100000000000005), double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      Outlier outlier0 = new Outlier((-3397.5709531667), (-3397.5709531667), (-3557.295197778283));
      assertEquals(159.72424461158334, outlier0.getY(), 0.01);
      assertEquals(159.72424461158334, outlier0.getX(), 0.01);
      
      outlier0.setPoint((Point2D) null);
      outlier0.getPoint();
      assertEquals((-3557.295197778283), outlier0.getRadius(), 0.01);
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      Outlier outlier0 = new Outlier((-895.5247626026), (-895.5247626026), (-895.5247626026));
      outlier0.setPoint((Point2D) null);
      // Undeclared exception!
      try { 
        outlier0.toString();
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.jfree.chart.renderer.Outlier", e);
      }
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      Outlier outlier0 = new Outlier((-1086.3753974186768), 678.8, 678.8);
      // Undeclared exception!
      try { 
        outlier0.overlaps((Outlier) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.jfree.chart.renderer.Outlier", e);
      }
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      Outlier outlier0 = new Outlier((-3090.54218449), 3609.91, (-2833.180363570618));
      outlier0.setPoint((Point2D) null);
      // Undeclared exception!
      try { 
        outlier0.getY();
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.jfree.chart.renderer.Outlier", e);
      }
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      Outlier outlier0 = new Outlier(0.0, 0.0, 4648.4815);
      outlier0.setPoint((Point2D) null);
      Outlier outlier1 = new Outlier(0.0, 4648.4815, 0.0);
      // Undeclared exception!
      try { 
        outlier0.equals(outlier1);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.jfree.chart.renderer.Outlier", e);
      }
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      Outlier outlier0 = new Outlier((-1080.5213859027751), (-1080.5213859027751), (-1080.5213859027751));
      // Undeclared exception!
      try { 
        outlier0.compareTo((Object) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.jfree.chart.renderer.Outlier", e);
      }
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      Outlier outlier0 = new Outlier(0.0, 0.0, 0.0);
      Point2D.Float point2D_Float0 = new Point2D.Float(0.0F, (-1314.3768F));
      // Undeclared exception!
      try { 
        outlier0.compareTo(point2D_Float0);
        fail("Expecting exception: ClassCastException");
      
      } catch(ClassCastException e) {
         //
         // java.awt.geom.Point2D$Float cannot be cast to org.jfree.chart.renderer.Outlier
         //
         verifyException("org.jfree.chart.renderer.Outlier", e);
      }
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      Outlier outlier0 = new Outlier(0.0, 1.1, 0.0);
      double double0 = outlier0.getX();
      assertEquals(1.1, outlier0.getY(), 0.01);
      assertEquals(0.0, outlier0.getRadius(), 0.01);
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      Outlier outlier0 = new Outlier(0.0, 130.00978, (-112.5));
      double double0 = outlier0.getY();
      assertEquals(112.5, outlier0.getX(), 0.01);
      assertEquals(242.50978, double0, 0.01);
      assertEquals((-112.5), outlier0.getRadius(), 0.01);
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      Outlier outlier0 = new Outlier(0.0, 130.00978, (-112.5));
      outlier0.setRadius(0.0);
      Outlier outlier1 = new Outlier((-966.48479), (-966.48479), (-112.5));
      assertEquals((-853.98479), outlier1.getY(), 0.01);
      
      Point2D.Float point2D_Float0 = new Point2D.Float(0.0F, (-2464.619F));
      outlier1.setPoint(point2D_Float0);
      outlier0.setPoint(point2D_Float0);
      boolean boolean0 = outlier0.equals(outlier1);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      Outlier outlier0 = new Outlier(0.0, 1.1, 0.0);
      Outlier outlier1 = new Outlier(0.0, 1.1, 0.0);
      boolean boolean0 = outlier0.equals(outlier1);
      assertEquals(0.0, outlier1.getX(), 0.01);
      assertTrue(boolean0);
      assertEquals(0.0, outlier1.getRadius(), 0.01);
      assertEquals(1.1, outlier1.getY(), 0.01);
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      Outlier outlier0 = new Outlier((-2356.835908), 0.0, 0.0);
      Object object0 = new Object();
      boolean boolean0 = outlier0.equals(object0);
      assertEquals((-2356.835908), outlier0.getX(), 0.01);
      assertEquals(0.0, outlier0.getRadius(), 0.01);
      assertFalse(boolean0);
      assertEquals(0.0, outlier0.getY(), 0.01);
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      Outlier outlier0 = new Outlier(1.0, 1.0, 1.0);
      boolean boolean0 = outlier0.equals(outlier0);
      assertTrue(boolean0);
      assertEquals(0.0, outlier0.getY(), 0.01);
      assertEquals(0.0, outlier0.getX(), 0.01);
      assertEquals(1.0, outlier0.getRadius(), 0.01);
  }

  @Test(timeout = 4000)
  public void test22()  throws Throwable  {
      Outlier outlier0 = new Outlier(0.0, 1.1, 0.0);
      Outlier outlier1 = new Outlier(1.1, 0.0, 1.1);
      boolean boolean0 = outlier0.equals(outlier1);
      assertEquals(0.0, outlier1.getX(), 0.01);
      assertEquals(1.1, outlier0.getY(), 0.01);
      assertEquals(1.1, outlier1.getRadius(), 0.01);
      assertEquals((-1.1), outlier1.getY(), 0.01);
      assertFalse(boolean0);
      assertEquals(0.0, outlier0.getX(), 0.01);
  }

  @Test(timeout = 4000)
  public void test23()  throws Throwable  {
      Outlier outlier0 = new Outlier(0.0, 1.1, 0.0);
      Outlier outlier1 = new Outlier(1.1, 0.0, 1.1);
      boolean boolean0 = outlier1.overlaps(outlier0);
      assertEquals(1.1, outlier1.getRadius(), 0.01);
      assertEquals(0.0, outlier1.getX(), 0.01);
      assertEquals(1.1, outlier0.getY(), 0.01);
      assertFalse(boolean0);
      assertEquals(0.0, outlier0.getX(), 0.01);
  }

  @Test(timeout = 4000)
  public void test24()  throws Throwable  {
      Outlier outlier0 = new Outlier(0.0, 1.1, 0.0);
      Outlier outlier1 = new Outlier(1.1, 0.0, 1.1);
      boolean boolean0 = outlier0.overlaps(outlier1);
      assertEquals(0.0, outlier1.getX(), 0.01);
      assertEquals(1.1, outlier1.getRadius(), 0.01);
      assertEquals(0.0, outlier0.getX(), 0.01);
      assertFalse(boolean0);
      assertEquals((-1.1), outlier1.getY(), 0.01);
  }

  @Test(timeout = 4000)
  public void test25()  throws Throwable  {
      Outlier outlier0 = new Outlier((-1055.056618650211), 0.0, (-1055.056618650211));
      Outlier outlier1 = new Outlier(0.0, 0.0, (-3750.724463219814));
      boolean boolean0 = outlier0.overlaps(outlier1);
      assertEquals(3750.724463219814, outlier1.getY(), 0.01);
      assertEquals((-3750.724463219814), outlier1.getRadius(), 0.01);
      assertEquals(3750.724463219814, outlier1.getX(), 0.01);
      assertEquals(0.0, outlier0.getX(), 0.01);
      assertFalse(boolean0);
      assertEquals(1055.056618650211, outlier0.getY(), 0.01);
  }

  @Test(timeout = 4000)
  public void test26()  throws Throwable  {
      Outlier outlier0 = new Outlier((-1084.3961433812133), (-1084.3961433812133), (-1084.3961433812133));
      boolean boolean0 = outlier0.overlaps(outlier0);
      assertEquals(0.0, outlier0.getX(), 0.01);
      assertFalse(boolean0);
      assertEquals(0.0, outlier0.getY(), 0.01);
  }

  @Test(timeout = 4000)
  public void test27()  throws Throwable  {
      Outlier outlier0 = new Outlier(2660.955, 2660.955, 2660.955);
      Outlier outlier1 = new Outlier((-1531.517), 2660.955, (-628.774725298837));
      int int0 = outlier0.compareTo(outlier1);
      assertEquals((-1), int0);
      assertEquals((-628.774725298837), outlier1.getRadius(), 0.01);
      assertEquals(3289.729725298837, outlier1.getY(), 0.01);
      assertEquals((-902.7422747011631), outlier1.getX(), 0.01);
  }

  @Test(timeout = 4000)
  public void test28()  throws Throwable  {
      Outlier outlier0 = new Outlier(0.0, 0.0, 0.0);
      Outlier outlier1 = new Outlier(0.0, (-312.22689743), (-312.22689743));
      int int0 = outlier0.compareTo(outlier1);
      assertEquals(0.0, outlier0.getX(), 0.01);
      assertEquals((-312.22689743), outlier1.getRadius(), 0.01);
      assertEquals((-1), int0);
      assertEquals(0.0, outlier1.getY(), 0.01);
  }

  @Test(timeout = 4000)
  public void test29()  throws Throwable  {
      Outlier outlier0 = new Outlier((-1080.5213859027751), (-1080.5213859027751), (-1080.5213859027751));
      int int0 = outlier0.compareTo(outlier0);
      assertEquals(0, int0);
      assertEquals((-1080.5213859027751), outlier0.getRadius(), 0.01);
      assertEquals(0.0, outlier0.getX(), 0.01);
      assertEquals(0.0, outlier0.getY(), 0.01);
  }

  @Test(timeout = 4000)
  public void test30()  throws Throwable  {
      Outlier outlier0 = new Outlier(0.0, 1.1, 0.0);
      String string0 = outlier0.toString();
      assertEquals(0.0, outlier0.getRadius(), 0.01);
      assertEquals("{0.0,1.1}", string0);
  }

  @Test(timeout = 4000)
  public void test31()  throws Throwable  {
      Outlier outlier0 = new Outlier(0.0, 1.1, 0.0);
      assertEquals(0.0, outlier0.getRadius(), 0.01);
      
      outlier0.setRadius(1.1);
      double double0 = outlier0.getRadius();
      assertEquals(1.1, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test32()  throws Throwable  {
      Outlier outlier0 = new Outlier(0.0, 1.1, 0.0);
      double double0 = outlier0.getRadius();
      assertEquals(0.0, double0, 0.01);
      assertEquals(1.1, outlier0.getY(), 0.01);
      assertEquals(0.0, outlier0.getX(), 0.01);
  }

  @Test(timeout = 4000)
  public void test33()  throws Throwable  {
      Outlier outlier0 = new Outlier(1.0, 1.0, 1.0);
      boolean boolean0 = outlier0.overlaps(outlier0);
      assertTrue(boolean0);
      assertEquals(0.0, outlier0.getX(), 0.01);
      assertEquals(0.0, outlier0.getY(), 0.01);
  }
}
