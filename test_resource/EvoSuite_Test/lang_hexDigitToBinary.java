/*
 * This file was automatically generated by EvoSuite
 * Wed Jun 18 09:22:24 GMT 2025
 */

package org.apache.commons.lang3;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.util.Arrays;
import java.util.UUID;
import org.apache.commons.lang3.Conversion;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.evosuite.runtime.mock.java.util.MockUUID;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true) 
public class Conversion_ESTest extends Conversion_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test000()  throws Throwable  {
      byte[] byteArray0 = new byte[9];
      // Undeclared exception!
      try { 
        Conversion.shortToByteArray((short)1, 16, byteArray0, (byte) (-91), 1);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nBytes-1)*8+srcPos is greater or equal to than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test001()  throws Throwable  {
      short[] shortArray0 = new short[0];
      // Undeclared exception!
      try { 
        Conversion.intToShortArray((-1), (byte)4, shortArray0, 1656, (byte)43);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nShorts-1)*16+srcPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test002()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      // Undeclared exception!
      try { 
        Conversion.binaryToInt(booleanArray0, (short)3173, (byte)32, 1, (byte)32);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+dstPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test003()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      // Undeclared exception!
      try { 
        Conversion.binaryToLong(booleanArray0, (-347), 0, 32, 3332);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+dstPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test004()  throws Throwable  {
      byte[] byteArray0 = new byte[5];
      byteArray0[1] = (byte)105;
      int int0 = Conversion.byteArrayToInt(byteArray0, (byte)1, (byte)1, (byte)1, (byte)1);
      assertEquals(211, int0);
  }

  @Test(timeout = 4000)
  public void test005()  throws Throwable  {
      String string0 = Conversion.shortToHex((short) (-1790), 1172, "", (short) (-1790), 0);
      assertEquals("", string0);
  }

  @Test(timeout = 4000)
  public void test006()  throws Throwable  {
      short[] shortArray0 = new short[0];
      long long0 = Conversion.shortArrayToLong(shortArray0, 3421, 1, 1, (-3072));
      assertEquals(1L, long0);
  }

  @Test(timeout = 4000)
  public void test007()  throws Throwable  {
      short[] shortArray0 = new short[2];
      int int0 = Conversion.shortArrayToInt(shortArray0, (-160), 53, (short)0, (-160));
      assertEquals(53, int0);
  }

  @Test(timeout = 4000)
  public void test008()  throws Throwable  {
      int[] intArray0 = new int[4];
      int[] intArray1 = Conversion.longToIntArray(671L, 7, intArray0, 11, (-1252));
      assertSame(intArray1, intArray0);
  }

  @Test(timeout = 4000)
  public void test009()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      boolean[] booleanArray1 = Conversion.shortToBinary((short)2069, (-1195), booleanArray0, (-2638), (-122));
      boolean[] booleanArray2 = Conversion.longToBinary(56, 56, booleanArray1, 807, (-3179));
      assertEquals(0, booleanArray2.length);
  }

  @Test(timeout = 4000)
  public void test010()  throws Throwable  {
      char char0 = Conversion.intToHexDigit(15);
      assertEquals('f', char0);
  }

  @Test(timeout = 4000)
  public void test011()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      boolean[] booleanArray1 = Conversion.byteToBinary((byte) (-123), (byte) (-123), booleanArray0, (byte) (-123), 0);
      boolean[] booleanArray2 = Conversion.intToBinary((short) (-123), (-3156), booleanArray1, (-1240), (-29));
      assertSame(booleanArray2, booleanArray1);
  }

  @Test(timeout = 4000)
  public void test012()  throws Throwable  {
      int[] intArray0 = new int[8];
      long long0 = Conversion.intArrayToLong(intArray0, 0, (byte)57, 0, 0);
      assertEquals(57L, long0);
  }

  @Test(timeout = 4000)
  public void test013()  throws Throwable  {
      long long0 = Conversion.hexToLong("", 0, 0, (-465), (-3300));
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test014()  throws Throwable  {
      int int0 = Conversion.hexToInt("", 51, 99, 56, (-2638));
      assertEquals(99, int0);
  }

  @Test(timeout = 4000)
  public void test015()  throws Throwable  {
      byte byte0 = Conversion.hexToByte((String) null, 3, (byte)0, 1, (-1));
      assertEquals((byte)0, byte0);
  }

  @Test(timeout = 4000)
  public void test016()  throws Throwable  {
      byte byte0 = Conversion.hexToByte("", (-4727), (byte) (-61), (byte) (-61), (-410));
      assertEquals((byte) (-61), byte0);
  }

  @Test(timeout = 4000)
  public void test017()  throws Throwable  {
      int int0 = Conversion.hexDigitToInt('1');
      assertEquals(1, int0);
  }

  @Test(timeout = 4000)
  public void test018()  throws Throwable  {
      String string0 = Conversion.byteToHex((byte)1, (-3477), "", (-3477), (-3477));
      assertEquals("", string0);
  }

  @Test(timeout = 4000)
  public void test019()  throws Throwable  {
      byte[] byteArray0 = new byte[5];
      short short0 = Conversion.byteArrayToShort(byteArray0, (-3611), (short)0, 92, (-4727));
      assertEquals((short)0, short0);
  }

  @Test(timeout = 4000)
  public void test020()  throws Throwable  {
      byte[] byteArray0 = new byte[5];
      short short0 = Conversion.byteArrayToShort(byteArray0, (-3611), (short) (-1790), 92, (-4727));
      assertEquals((short) (-1790), short0);
  }

  @Test(timeout = 4000)
  public void test021()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[2];
      int int0 = Conversion.binaryToInt(booleanArray0, 0, 0, 3, 0);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test022()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      int int0 = Conversion.binaryToInt(booleanArray0, 0, (short) (-1790), (-3611), (-3611));
      assertEquals((-1790), int0);
  }

  @Test(timeout = 4000)
  public void test023()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.shortToHex((short)265, 2135, (String) null, 64, (-1643));
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test024()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.shortArrayToLong((short[]) null, 0, 2021L, (-430), 2);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test025()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.longToBinary(2534, (-1), (boolean[]) null, (-2795), 8);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test026()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.intToHex(91, 3, (String) null, (-1), (-3400));
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test027()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.intArrayToLong((int[]) null, (-1447), (byte)44, (-1), 1);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test028()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToLong("", (-1649), (-1649), (-3219), 11);
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
      }
  }

  @Test(timeout = 4000)
  public void test029()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToLong((String) null, 3559, (byte)103, (-175), (byte)13);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test030()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.byteArrayToInt((byte[]) null, 2318, 255, (byte)54, (-1245));
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test031()  throws Throwable  {
      byte[] byteArray0 = new byte[5];
      // Undeclared exception!
      try { 
        Conversion.byteArrayToInt(byteArray0, (byte) (-66), (byte)0, (-4715), 88);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -66
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test032()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.binaryToShort((boolean[]) null, (-1), (short)527, (-637), 662);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test033()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.binaryToInt((boolean[]) null, 0, (-581), (-1297), 0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test034()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      // Undeclared exception!
      try { 
        Conversion.binaryToInt(booleanArray0, (short)3172, (byte)32, 1, (byte)12);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 3172
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test035()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigitMsb0_4bits((boolean[]) null, (int) (short)3173);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test036()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigitMsb0_4bits((boolean[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test037()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigit((boolean[]) null, (int) (short)406);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test038()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigit((boolean[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test039()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[1];
      // Undeclared exception!
      try { 
        Conversion.binaryBeMsb0ToHexDigit(booleanArray0, 15);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
      }
  }

  @Test(timeout = 4000)
  public void test040()  throws Throwable  {
      byte[] byteArray0 = new byte[7];
      byte[] byteArray1 = Conversion.longToByteArray(0L, 32, byteArray0, (byte)1, (-1686));
      assertSame(byteArray1, byteArray0);
  }

  @Test(timeout = 4000)
  public void test041()  throws Throwable  {
      char char0 = Conversion.intToHexDigit(1);
      assertEquals('1', char0);
  }

  @Test(timeout = 4000)
  public void test042()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      // Undeclared exception!
      try { 
        Conversion.binaryBeMsb0ToHexDigit(booleanArray0, 106);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot convert an empty array.
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test043()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0, 1);
      assertEquals('0', char0);
  }

  @Test(timeout = 4000)
  public void test044()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      boolean[] booleanArray1 = Conversion.byteToBinary((byte) (-123), (byte) (-123), booleanArray0, (byte) (-123), 0);
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigitMsb0_4bits(booleanArray1, 97);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // src.length-srcPos<4: src.length=0, srcPos=97
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test045()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[15];
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, (int) (short)85);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // src.length>8: src.length=15
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test046()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[9];
      booleanArray0[0] = true;
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0, 0);
      assertEquals('9', char0);
  }

  @Test(timeout = 4000)
  public void test047()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[2];
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigit(booleanArray0, 75);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 75
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test048()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[9];
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0, 0);
      assertEquals('8', char0);
  }

  @Test(timeout = 4000)
  public void test049()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigit(booleanArray0, (-1));
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -1
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test050()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      // Undeclared exception!
      try { 
        Conversion.byteArrayToUuid(byteArray0, (byte)76);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Need at least 16 bytes for UUID
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test051()  throws Throwable  {
      byte[] byteArray0 = new byte[5];
      // Undeclared exception!
      try { 
        Conversion.byteArrayToUuid(byteArray0, (-1715));
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -1715
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test052()  throws Throwable  {
      UUID uUID0 = MockUUID.randomUUID();
      byte[] byteArray0 = new byte[2];
      // Undeclared exception!
      try { 
        Conversion.uuidToByteArray(uUID0, byteArray0, (byte)71, 14);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 71
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test053()  throws Throwable  {
      UUID uUID0 = MockUUID.randomUUID();
      byte[] byteArray0 = new byte[1];
      // Undeclared exception!
      try { 
        Conversion.uuidToByteArray(uUID0, byteArray0, 18, 839);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBytes is greater than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test054()  throws Throwable  {
      UUID uUID0 = MockUUID.fromString("");
      byte[] byteArray0 = new byte[7];
      byte[] byteArray1 = Conversion.uuidToByteArray(uUID0, byteArray0, (byte) (-61), (byte)0);
      assertEquals(7, byteArray1.length);
  }

  @Test(timeout = 4000)
  public void test055()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[3];
      // Undeclared exception!
      try { 
        Conversion.byteToBinary((byte)63, 1, booleanArray0, 101, 1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 101
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test056()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[3];
      boolean[] booleanArray1 = Conversion.byteToBinary((byte)115, (-2458), booleanArray0, 1, (-2458));
      assertTrue(Arrays.equals(new boolean[] {false, false, false}, booleanArray1));
  }

  @Test(timeout = 4000)
  public void test057()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[1];
      // Undeclared exception!
      try { 
        Conversion.byteToBinary((byte)34, (byte)115, booleanArray0, 99, (-1));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+srcPos is greater or equal to than 8
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test058()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.shortToBinary((short)53, (-467), (boolean[]) null, (-467), 3);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test059()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[7];
      // Undeclared exception!
      try { 
        Conversion.shortToBinary((short)3173, 97, booleanArray0, (-1841), 1316);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+srcPos is greater or equal to than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test060()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[3];
      boolean[] booleanArray1 = Conversion.shortToBinary((short)0, (-2948), booleanArray0, 131, 0);
      assertTrue(Arrays.equals(new boolean[] {false, false, false}, booleanArray1));
  }

  @Test(timeout = 4000)
  public void test061()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[1];
      boolean[] booleanArray1 = Conversion.intToBinary((-1263), (-1182), booleanArray0, (-2350), (-2074));
      assertSame(booleanArray1, booleanArray0);
  }

  @Test(timeout = 4000)
  public void test062()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[3];
      // Undeclared exception!
      try { 
        Conversion.intToBinary(80, (-1), booleanArray0, 61, 3);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 61
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test063()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[1];
      boolean[] booleanArray1 = Conversion.intToBinary((-123), (short)0, booleanArray0, 0, 0);
      assertSame(booleanArray0, booleanArray1);
  }

  @Test(timeout = 4000)
  public void test064()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[2];
      // Undeclared exception!
      try { 
        Conversion.intToBinary(2696, 1, booleanArray0, 872, 2197);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+srcPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test065()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[2];
      // Undeclared exception!
      try { 
        Conversion.longToBinary(872, (-296), booleanArray0, 100, 211);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 100
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test066()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.longToBinary(1L, 0, (boolean[]) null, (-1717), (-636));
      assertNull(booleanArray0);
  }

  @Test(timeout = 4000)
  public void test067()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[3];
      boolean[] booleanArray1 = Conversion.longToBinary(0L, (-1), booleanArray0, 91, 0);
      assertSame(booleanArray1, booleanArray0);
  }

  @Test(timeout = 4000)
  public void test068()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[1];
      // Undeclared exception!
      try { 
        Conversion.longToBinary((-413L), (-1), booleanArray0, 91, 91);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+srcPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test069()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.byteToHex((byte)25, 181, "0060000000600000006000000eater or equal to than 64", (-4727), 1522);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+srcPos is greater or equal to than 8
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test070()  throws Throwable  {
      String string0 = Conversion.byteToHex((byte)0, (-3477), "~", (-3477), (-3477));
      assertEquals("~", string0);
  }

  @Test(timeout = 4000)
  public void test071()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.shortToHex((short)3199, (-4727), "src.length-srcPos<4: src.length=", (-1), 1);
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
         //
         // String index out of range: -1
         //
         verifyException("java.lang.AbstractStringBuilder", e);
      }
  }

  @Test(timeout = 4000)
  public void test072()  throws Throwable  {
      String string0 = Conversion.shortToHex((short)518, (-1424), "src.length-srcPos<4: src.length=", (-4715), (-4715));
      assertEquals("src.length-srcPos<4: src.length=", string0);
  }

  @Test(timeout = 4000)
  public void test073()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.shortToHex((short)0, (short)0, "", 192, 192);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+srcPos is greater or equal to than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test074()  throws Throwable  {
      String string0 = Conversion.intToHex(4, (-3622), "src.length-srcPos<4: src.length=", (short)0, 173);
      assertEquals("00100000001000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100", string0);
  }

  @Test(timeout = 4000)
  public void test075()  throws Throwable  {
      String string0 = Conversion.intToHex(95, (-1692), "src.length-srcPos<4: src.length=", 101, (-4715));
      assertEquals("src.length-srcPos<4: src.length=", string0);
  }

  @Test(timeout = 4000)
  public void test076()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.intToHex(84, (short)3173, "", (short)3173, (short)3173);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+srcPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test077()  throws Throwable  {
      String string0 = Conversion.intToHex((-1), (short)3173, "src.length-srcPos<4: src.length=", (-1), 0);
      assertEquals("src.length-srcPos<4: src.length=", string0);
  }

  @Test(timeout = 4000)
  public void test078()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.longToHex(1293L, (-1512), "=0x&Ke", 4119, 52);
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
         //
         // String index out of range: 4119
         //
         verifyException("java.lang.AbstractStringBuilder", e);
      }
  }

  @Test(timeout = 4000)
  public void test079()  throws Throwable  {
      String string0 = Conversion.longToHex((byte) (-99), 1051, "0|HJ-%u%;p1{=0m", 0, 0);
      assertEquals("0|HJ-%u%;p1{=0m", string0);
  }

  @Test(timeout = 4000)
  public void test080()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.longToHex(0, 2217, (String) null, 0, 2217);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+srcPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test081()  throws Throwable  {
      byte[] byteArray0 = new byte[5];
      byte[] byteArray1 = Conversion.shortToByteArray((short) (-1214), (byte)94, byteArray0, (short) (-1801), (short) (-1801));
      assertArrayEquals(new byte[] {(byte)0, (byte)0, (byte)0, (byte)0, (byte)0}, byteArray1);
  }

  @Test(timeout = 4000)
  public void test082()  throws Throwable  {
      byte[] byteArray0 = new byte[1];
      byte[] byteArray1 = Conversion.shortToByteArray((short) (-813), 658, byteArray0, (byte)0, 0);
      assertEquals(1, byteArray1.length);
  }

  @Test(timeout = 4000)
  public void test083()  throws Throwable  {
      byte[] byteArray0 = new byte[1];
      // Undeclared exception!
      try { 
        Conversion.shortToByteArray((short)55, 85, byteArray0, (short) (-813), 658);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nBytes-1)*8+srcPos is greater or equal to than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test084()  throws Throwable  {
      byte[] byteArray0 = new byte[5];
      // Undeclared exception!
      try { 
        Conversion.intToByteArray(9977, (byte)0, byteArray0, 1, 4612);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nBytes-1)*8+srcPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test085()  throws Throwable  {
      byte[] byteArray0 = new byte[2];
      byte[] byteArray1 = Conversion.intToByteArray(75, 3, byteArray0, 1, (byte)0);
      assertSame(byteArray1, byteArray0);
  }

  @Test(timeout = 4000)
  public void test086()  throws Throwable  {
      byte[] byteArray0 = new byte[5];
      byte[] byteArray1 = Conversion.intToByteArray((-1635), 0, byteArray0, (-637), (-637));
      assertArrayEquals(new byte[] {(byte)0, (byte)0, (byte)0, (byte)0, (byte)0}, byteArray1);
  }

  @Test(timeout = 4000)
  public void test087()  throws Throwable  {
      UUID uUID0 = MockUUID.randomUUID();
      byte[] byteArray0 = new byte[6];
      byte[] byteArray1 = Conversion.uuidToByteArray(uUID0, byteArray0, 0, (byte) (-23));
      assertArrayEquals(new byte[] {(byte)0, (byte)0, (byte)0, (byte)0, (byte)0, (byte)0}, byteArray1);
  }

  @Test(timeout = 4000)
  public void test088()  throws Throwable  {
      byte[] byteArray0 = new byte[5];
      // Undeclared exception!
      try { 
        Conversion.longToByteArray((-1), (-898), byteArray0, 9578, 9);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 9578
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test089()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.longToByteArray((-501L), (short)3163, (byte[]) null, (-1), (short)518);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nBytes-1)*8+srcPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test090()  throws Throwable  {
      short[] shortArray0 = new short[9];
      // Undeclared exception!
      try { 
        Conversion.intToShortArray((short)265, 66, shortArray0, (short)0, (short) (-1));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nShorts-1)*16+srcPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test091()  throws Throwable  {
      short[] shortArray0 = new short[4];
      short[] shortArray1 = Conversion.intToShortArray(1, (byte) (-123), shortArray0, (short) (-1214), 0);
      assertEquals(4, shortArray1.length);
  }

  @Test(timeout = 4000)
  public void test092()  throws Throwable  {
      short[] shortArray0 = Conversion.intToShortArray(1513, (-3869), (short[]) null, (-1253), (-3869));
      assertNull(shortArray0);
  }

  @Test(timeout = 4000)
  public void test093()  throws Throwable  {
      short[] shortArray0 = new short[1];
      // Undeclared exception!
      try { 
        Conversion.longToShortArray(0L, 0, shortArray0, (short)1, (short)1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 1
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test094()  throws Throwable  {
      short[] shortArray0 = new short[4];
      // Undeclared exception!
      try { 
        Conversion.longToShortArray((-1147), 102, shortArray0, (byte)0, 3);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nShorts-1)*16+srcPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test095()  throws Throwable  {
      short[] shortArray0 = new short[0];
      short[] shortArray1 = Conversion.longToShortArray(99, 2360, shortArray0, 16, 0);
      assertArrayEquals(new short[] {}, shortArray1);
  }

  @Test(timeout = 4000)
  public void test096()  throws Throwable  {
      short[] shortArray0 = new short[1];
      short[] shortArray1 = Conversion.longToShortArray(0L, 0, shortArray0, 1931, (-1188));
      assertEquals(1, shortArray1.length);
  }

  @Test(timeout = 4000)
  public void test097()  throws Throwable  {
      int[] intArray0 = new int[5];
      // Undeclared exception!
      try { 
        Conversion.longToIntArray(0L, 1, intArray0, 50, 54);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nInts-1)*32+srcPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test098()  throws Throwable  {
      int[] intArray0 = new int[0];
      int[] intArray1 = Conversion.longToIntArray((-4723), (-4723), intArray0, 716, (-4723));
      assertSame(intArray1, intArray0);
  }

  @Test(timeout = 4000)
  public void test099()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[3];
      // Undeclared exception!
      try { 
        Conversion.binaryToByte(booleanArray0, 3727, (byte)0, (byte)0, 3727);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+dstPos is greater or equal to than 8
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test100()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[2];
      byte byte0 = Conversion.binaryToByte(booleanArray0, 3653, (byte)0, (-449), (-930));
      assertEquals((byte)0, byte0);
  }

  @Test(timeout = 4000)
  public void test101()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[2];
      byte byte0 = Conversion.binaryToByte(booleanArray0, (-1), (byte)57, (byte)57, 0);
      assertEquals((byte)57, byte0);
  }

  @Test(timeout = 4000)
  public void test102()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[6];
      booleanArray0[3] = true;
      // Undeclared exception!
      try { 
        Conversion.binaryToShort(booleanArray0, 0, (short) (-1450), (-414), 255);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 6
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test103()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[1];
      // Undeclared exception!
      try { 
        Conversion.binaryToShort(booleanArray0, (short)2, (short)52, (short)1333, (short) (-1214));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+dstPos is greater or equal to than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test104()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      short short0 = Conversion.binaryToShort(booleanArray0, (-3267), (short)0, (-1990), (short)0);
      assertEquals((short)0, short0);
  }

  @Test(timeout = 4000)
  public void test105()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      boolean[] booleanArray1 = Conversion.shortToBinary((short)661, 5, booleanArray0, (short)661, (-1));
      // Undeclared exception!
      try { 
        Conversion.binaryToShort(booleanArray1, (short)661, (short)0, (-766), 7);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 661
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test106()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[3];
      short short0 = Conversion.binaryToShort(booleanArray0, 97, (short)85, (-2083), (-1));
      assertEquals((short)85, short0);
  }

  @Test(timeout = 4000)
  public void test107()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      booleanArray0[1] = true;
      int int0 = Conversion.binaryToInt(booleanArray0, 1, 14, 5, 1);
      assertEquals(46, int0);
  }

  @Test(timeout = 4000)
  public void test108()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      int int0 = Conversion.binaryToInt(booleanArray0, 1, 14, 5, 1);
      assertEquals(14, int0);
  }

  @Test(timeout = 4000)
  public void test109()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[2];
      // Undeclared exception!
      try { 
        Conversion.binaryToInt(booleanArray0, 0, 0, 0, 56);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+dstPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test110()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      // Undeclared exception!
      try { 
        Conversion.binaryToLong(booleanArray0, 61, 61, (-1863), 76);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 61
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test111()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[1];
      long long0 = Conversion.binaryToLong(booleanArray0, (-2109), 1829L, 952, (short)0);
      assertEquals(1829L, long0);
  }

  @Test(timeout = 4000)
  public void test112()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      long long0 = Conversion.binaryToLong(booleanArray0, 0, 0, (-766), 3332);
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test113()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      boolean[] booleanArray1 = Conversion.shortToBinary((short)661, 5, booleanArray0, (short)661, (-1));
      long long0 = Conversion.binaryToLong(booleanArray1, 952, (short)0, 7, (-795));
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test114()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToByte("src.length-srcPos<4: src.length=", 12, (byte)57, 5, 1);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'r' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test115()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToByte("-IbzzHj", 1131, (byte)14, 54, 1131);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+dstPos is greater or equal to than 8
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test116()  throws Throwable  {
      byte byte0 = Conversion.hexToByte("1y%a", 3653, (byte)1, (-252), 0);
      assertEquals((byte)1, byte0);
  }

  @Test(timeout = 4000)
  public void test117()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToShort("BNj8%", 6, (short)2221, (-987), (byte)1);
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
      }
  }

  @Test(timeout = 4000)
  public void test118()  throws Throwable  {
      short short0 = Conversion.hexToShort("iP9mA[7aS3c7", 0, (short)0, 0, 0);
      assertEquals((short)0, short0);
  }

  @Test(timeout = 4000)
  public void test119()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToShort("", (short)3173, (short)3173, 95, 102);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+dstPos is greater or equal to than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test120()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToInt("", 140, 140, (-1738), 140);
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
      }
  }

  @Test(timeout = 4000)
  public void test121()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToInt("~H1kyVRm=2Ua%9j", 140, 140, 140, 140);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+dstPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test122()  throws Throwable  {
      int int0 = Conversion.hexToInt("*VPP@5E\"UP8$hU", (byte)126, (-464), 3, 0);
      assertEquals((-464), int0);
  }

  @Test(timeout = 4000)
  public void test123()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToLong(", srcPos=", (short)0, (-2331L), 0, 7);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret ',' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test124()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToLong("src.length-srcPos<4: src.length=", (-837), (-1492L), 59, 3421);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+dstPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test125()  throws Throwable  {
      long long0 = Conversion.hexToLong("@2z@NSBkw_G\"^9", 1728, 1294L, 1728, (-4727));
      assertEquals(1294L, long0);
  }

  @Test(timeout = 4000)
  public void test126()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      short short0 = Conversion.byteArrayToShort(byteArray0, (-518), (short)1333, (short)0, (short) (-463));
      assertEquals((short)1333, short0);
  }

  @Test(timeout = 4000)
  public void test127()  throws Throwable  {
      byte[] byteArray0 = new byte[1];
      // Undeclared exception!
      try { 
        Conversion.byteArrayToShort(byteArray0, 92, (short) (-2937), 96, (byte)43);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nBytes-1)*8+dstPos is greater or equal to than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test128()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      int int0 = Conversion.byteArrayToInt(byteArray0, (-3869), (-22), (byte)74, (-3869));
      assertEquals((-22), int0);
  }

  @Test(timeout = 4000)
  public void test129()  throws Throwable  {
      byte[] byteArray0 = new byte[5];
      // Undeclared exception!
      try { 
        Conversion.byteArrayToInt(byteArray0, 1151, (byte)0, 857, (-1));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nBytes-1)*8+dstPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test130()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      int int0 = Conversion.byteArrayToInt(byteArray0, (-465), (short)0, 5186, 0);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test131()  throws Throwable  {
      byte[] byteArray0 = new byte[9];
      // Undeclared exception!
      try { 
        Conversion.byteArrayToLong(byteArray0, (-175), (-3432L), 81, (byte)13);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nBytes-1)*8+dstPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test132()  throws Throwable  {
      byte[] byteArray0 = new byte[5];
      long long0 = Conversion.byteArrayToLong(byteArray0, 1051, 2485L, (byte)1, (byte)0);
      assertEquals(2485L, long0);
  }

  @Test(timeout = 4000)
  public void test133()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      // Undeclared exception!
      try { 
        Conversion.byteArrayToLong(byteArray0, 71, 0L, 0, 1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 71
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test134()  throws Throwable  {
      byte[] byteArray0 = new byte[1];
      long long0 = Conversion.byteArrayToLong(byteArray0, (byte)0, 0L, 1, (-4000));
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test135()  throws Throwable  {
      short[] shortArray0 = new short[2];
      // Undeclared exception!
      try { 
        Conversion.shortArrayToInt(shortArray0, 3296, (-2214), (short)0, 3793);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nShorts-1)*16+dstPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test136()  throws Throwable  {
      short[] shortArray0 = new short[8];
      int int0 = Conversion.shortArrayToInt(shortArray0, 0, (short)0, (short) (-1), 0);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test137()  throws Throwable  {
      short[] shortArray0 = new short[3];
      int int0 = Conversion.shortArrayToInt(shortArray0, (short)0, (short) (-123), 0, (byte) (-123));
      assertEquals((-123), int0);
  }

  @Test(timeout = 4000)
  public void test138()  throws Throwable  {
      short[] shortArray0 = new short[6];
      // Undeclared exception!
      try { 
        Conversion.shortArrayToLong(shortArray0, 1595, (byte)1, (short)0, (short)1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 1595
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test139()  throws Throwable  {
      short[] shortArray0 = new short[6];
      long long0 = Conversion.shortArrayToLong(shortArray0, 1595, 0L, (short)0, (short)0);
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test140()  throws Throwable  {
      short[] shortArray0 = new short[0];
      // Undeclared exception!
      try { 
        Conversion.shortArrayToLong(shortArray0, (-837), 65535L, 59, 3421);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nShorts-1)*16+dstPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test141()  throws Throwable  {
      short[] shortArray0 = new short[0];
      long long0 = Conversion.shortArrayToLong(shortArray0, 0, (-4715), (-1), (-2362));
      assertEquals((-4715L), long0);
  }

  @Test(timeout = 4000)
  public void test142()  throws Throwable  {
      int[] intArray0 = new int[3];
      // Undeclared exception!
      try { 
        Conversion.intArrayToLong(intArray0, (-331), 0, 0, 468);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nInts-1)*32+dstPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test143()  throws Throwable  {
      int[] intArray0 = new int[3];
      long long0 = Conversion.intArrayToLong(intArray0, 520, (-675L), 1, 0);
      assertEquals((-675L), long0);
  }

  @Test(timeout = 4000)
  public void test144()  throws Throwable  {
      char char0 = Conversion.intToHexDigitMsb0(8);
      assertEquals('1', char0);
  }

  @Test(timeout = 4000)
  public void test145()  throws Throwable  {
      char char0 = Conversion.intToHexDigitMsb0(6);
      assertEquals('6', char0);
  }

  @Test(timeout = 4000)
  public void test146()  throws Throwable  {
      char char0 = Conversion.intToHexDigitMsb0(3);
      assertEquals('c', char0);
  }

  @Test(timeout = 4000)
  public void test147()  throws Throwable  {
      char char0 = Conversion.intToHexDigitMsb0(1);
      assertEquals('8', char0);
  }

  @Test(timeout = 4000)
  public void test148()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.intToHexDigitMsb0((-1694));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nibble value not between 0 and 15: -1694
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test149()  throws Throwable  {
      char char0 = Conversion.intToHexDigitMsb0((short)0);
      assertEquals('0', char0);
  }

  @Test(timeout = 4000)
  public void test150()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.intToHexDigit(49);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nibble value not between 0 and 15: 49
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test151()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.intToHex(97, (-1692), "src.length-srcPos<4: src.length=", (-1692), 101);
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
         //
         // String index out of range: -1692
         //
         verifyException("java.lang.AbstractStringBuilder", e);
      }
  }

  @Test(timeout = 4000)
  public void test152()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[2];
      booleanArray0[0] = true;
      booleanArray0[1] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0);
      assertEquals('3', char0);
  }

  @Test(timeout = 4000)
  public void test153()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[3];
      booleanArray0[1] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0);
      assertEquals('2', char0);
  }

  @Test(timeout = 4000)
  public void test154()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[3];
      booleanArray0[0] = true;
      booleanArray0[2] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0);
      assertEquals('5', char0);
  }

  @Test(timeout = 4000)
  public void test155()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[3];
      booleanArray0[0] = true;
      booleanArray0[1] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0);
      assertEquals('6', char0);
  }

  @Test(timeout = 4000)
  public void test156()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[3];
      booleanArray0[0] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0);
      assertEquals('4', char0);
  }

  @Test(timeout = 4000)
  public void test157()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      booleanArray0[4] = true;
      booleanArray0[5] = true;
      booleanArray0[6] = true;
      booleanArray0[7] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0);
      assertEquals('f', char0);
  }

  @Test(timeout = 4000)
  public void test158()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      booleanArray0[4] = true;
      booleanArray0[5] = true;
      booleanArray0[6] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0);
      assertEquals('e', char0);
  }

  @Test(timeout = 4000)
  public void test159()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      booleanArray0[4] = true;
      booleanArray0[5] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0);
      assertEquals('c', char0);
  }

  @Test(timeout = 4000)
  public void test160()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      booleanArray0[4] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0);
      assertEquals('8', char0);
  }

  @Test(timeout = 4000)
  public void test161()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      // Undeclared exception!
      try { 
        Conversion.binaryBeMsb0ToHexDigit(booleanArray0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot convert an empty array.
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test162()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[7];
      booleanArray0[1] = true;
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, 0);
      assertEquals('4', char0);
  }

  @Test(timeout = 4000)
  public void test163()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[7];
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, 0);
      assertEquals('0', char0);
  }

  @Test(timeout = 4000)
  public void test164()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[3];
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, (-4715));
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -4712
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test165()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[9];
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigitMsb0_4bits(booleanArray0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // src.length>8: src.length=9
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test166()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[2];
      booleanArray0[0] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0);
      assertEquals('1', char0);
  }

  @Test(timeout = 4000)
  public void test167()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      booleanArray0[0] = true;
      booleanArray0[1] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0);
      assertEquals('3', char0);
  }

  @Test(timeout = 4000)
  public void test168()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[6];
      booleanArray0[1] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0);
      assertEquals('2', char0);
  }

  @Test(timeout = 4000)
  public void test169()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[3];
      booleanArray0[0] = true;
      booleanArray0[2] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0);
      assertEquals('5', char0);
  }

  @Test(timeout = 4000)
  public void test170()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[3];
      booleanArray0[2] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0);
      assertEquals('4', char0);
  }

  @Test(timeout = 4000)
  public void test171()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[6];
      char char0 = Conversion.binaryToHexDigit(booleanArray0);
      assertEquals('0', char0);
  }

  @Test(timeout = 4000)
  public void test172()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      boolean[] booleanArray1 = Conversion.shortToBinary((short)661, 5, booleanArray0, (short)661, (-1));
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigit(booleanArray1);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot convert an empty array.
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test173()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToBinary('$');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret '$' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test174()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitMsb0ToBinary('c');
      assertEquals(4, booleanArray0.length);
  }

  @Test(timeout = 4000)
  public void test175()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitMsb0ToBinary('b');
      assertEquals(4, booleanArray0.length);
  }

  @Test(timeout = 4000)
  public void test176()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToBinary('S');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'S' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test177()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToBinary('I');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'I' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test178()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToBinary('G');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'G' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test179()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToBinary('<');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret '<' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test180()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitMsb0ToBinary('6');
      assertTrue(Arrays.equals(new boolean[] {false, true, true, false}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test181()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitMsb0ToBinary('5');
      assertEquals(4, booleanArray0.length);
  }

  @Test(timeout = 4000)
  public void test182()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitMsb0ToBinary('1');
      assertEquals(4, booleanArray0.length);
  }

  @Test(timeout = 4000)
  public void test183()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitMsb0ToBinary('F');
      assertEquals(4, booleanArray0.length);
  }

  @Test(timeout = 4000)
  public void test184()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitMsb0ToBinary('0');
      assertTrue(Arrays.equals(new boolean[] {false, false, false, false}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test185()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitToBinary(']');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret ']' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test186()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitToBinary('Q');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'Q' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test187()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitToBinary('L');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'L' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test188()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitToBinary('K');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'K' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test189()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitToBinary('4');
      assertTrue(Arrays.equals(new boolean[] {false, false, true, false}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test190()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitToBinary('s');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 's' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test191()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitToBinary('1');
      assertTrue(Arrays.equals(new boolean[] {true, false, false, false}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test192()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToInt('s');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 's' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test193()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToInt('Z');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'Z' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test194()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToInt('Y');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'Y' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test195()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToInt('U');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'U' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test196()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToInt('T');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'T' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test197()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToInt('Q');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'Q' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test198()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToInt('H');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'H' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test199()  throws Throwable  {
      int int0 = Conversion.hexDigitMsb0ToInt('A');
      assertEquals(5, int0);
  }

  @Test(timeout = 4000)
  public void test200()  throws Throwable  {
      int int0 = Conversion.hexDigitMsb0ToInt('3');
      assertEquals(12, int0);
  }

  @Test(timeout = 4000)
  public void test201()  throws Throwable  {
      int int0 = Conversion.hexDigitMsb0ToInt('C');
      assertEquals(3, int0);
  }

  @Test(timeout = 4000)
  public void test202()  throws Throwable  {
      int int0 = Conversion.hexDigitMsb0ToInt('0');
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test203()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitToInt('t');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 't' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test204()  throws Throwable  {
      int int0 = Conversion.hexDigitToInt('0');
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test205()  throws Throwable  {
      Conversion conversion0 = new Conversion();
  }
}
