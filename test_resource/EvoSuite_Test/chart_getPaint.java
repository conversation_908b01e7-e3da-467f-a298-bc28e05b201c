/*
 * This file was automatically generated by EvoSuite
 * Wed Jun 18 07:17:55 GMT 2025
 */

package org.jfree.chart.ui;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.awt.Dimension;
import java.awt.Graphics;
import java.awt.Paint;
import java.awt.SystemColor;
import javax.swing.DebugGraphics;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.jfree.chart.ui.PaintSample;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true) 
public class PaintSample_ESTest extends PaintSample_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test0()  throws Throwable  {
      PaintSample paintSample0 = new PaintSample((Paint) null);
      paintSample0.setBounds(0, 0, 0, (-1));
      // Undeclared exception!
      try { 
        paintSample0.paintComponent((Graphics) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.jfree.chart.ui.PaintSample", e);
      }
  }

  @Test(timeout = 4000)
  public void test1()  throws Throwable  {
      PaintSample paintSample0 = new PaintSample((Paint) null);
      Paint paint0 = paintSample0.getPaint();
      assertNull(paint0);
  }

  @Test(timeout = 4000)
  public void test2()  throws Throwable  {
      SystemColor systemColor0 = SystemColor.text;
      PaintSample paintSample0 = new PaintSample(systemColor0);
      DebugGraphics debugGraphics0 = new DebugGraphics();
      // Undeclared exception!
      try { 
        paintSample0.paintComponent(debugGraphics0);
        fail("Expecting exception: ClassCastException");
      
      } catch(ClassCastException e) {
         //
         // javax.swing.DebugGraphics cannot be cast to java.awt.Graphics2D
         //
         verifyException("org.jfree.chart.ui.PaintSample", e);
      }
  }

  @Test(timeout = 4000)
  public void test3()  throws Throwable  {
      SystemColor systemColor0 = SystemColor.windowBorder;
      PaintSample paintSample0 = new PaintSample(systemColor0);
      Dimension dimension0 = paintSample0.getPreferredSize();
      assertEquals(12, dimension0.height);
      assertEquals(80, dimension0.width);
  }

  @Test(timeout = 4000)
  public void test4()  throws Throwable  {
      SystemColor systemColor0 = SystemColor.windowBorder;
      PaintSample paintSample0 = new PaintSample(systemColor0);
      Paint paint0 = paintSample0.getPaint();
      assertEquals(1, paint0.getTransparency());
  }

  @Test(timeout = 4000)
  public void test5()  throws Throwable  {
      PaintSample paintSample0 = new PaintSample((Paint) null);
      paintSample0.setPaint((Paint) null);
      assertFalse(paintSample0.isFocusTraversalPolicyProvider());
  }
}
