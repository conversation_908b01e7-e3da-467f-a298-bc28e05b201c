/*
 * This file was automatically generated by EvoSuite
 * Wed Jun 18 07:21:13 GMT 2025
 */

package org.jfree.chart.ui;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.awt.Dimension;
import java.awt.Point;
import java.awt.Rectangle;
import java.awt.geom.Line2D;
import java.awt.geom.Rectangle2D;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.jfree.chart.ui.LengthAdjustmentType;
import org.jfree.chart.ui.RectangleInsets;
import org.jfree.chart.util.UnitType;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true) 
public class RectangleInsets_ESTest extends RectangleInsets_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      Rectangle2D.Float rectangle2D_Float0 = new Rectangle2D.Float();
      Rectangle rectangle0 = rectangle2D_Float0.getBounds();
      Rectangle2D.Double rectangle2D_Double0 = (Rectangle2D.Double)rectangle0.createIntersection(rectangle2D_Float0);
      RectangleInsets rectangleInsets0 = new RectangleInsets(427.192156937, 1.0, 427.192156937, 1.0);
      rectangleInsets0.trim(rectangle2D_Double0);
      assertEquals(427.192156937, rectangle2D_Double0.y, 0.01);
      assertEquals((-427.192156937), rectangle2D_Double0.getMaxY(), 0.01);
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets((-1816.83), (-1816.83), 1633.317907936, (-1663.1));
      Line2D.Float line2D_Float0 = new Line2D.Float(1.0F, 5.4425F, 3967.72F, 0.0F);
      Rectangle2D.Float rectangle2D_Float0 = (Rectangle2D.Float)line2D_Float0.getBounds2D();
      rectangleInsets0.trim(rectangle2D_Float0);
      assertEquals((-1815.83F), rectangle2D_Float0.x, 0.01F);
      assertEquals(5630.8199462890625, rectangle2D_Float0.getMaxX(), 0.01);
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      UnitType unitType0 = UnitType.RELATIVE;
      RectangleInsets rectangleInsets0 = new RectangleInsets(unitType0, 0.0, 1481.7405786, 1481.7405786, 0.0);
      Rectangle2D.Double rectangle2D_Double0 = new Rectangle2D.Double(0.0, 534.7, 331.1955, 140.394848);
      Rectangle2D rectangle2D0 = rectangleInsets0.createInsetRectangle((Rectangle2D) rectangle2D_Double0, true, false);
      assertEquals(0.0, rectangleInsets0.getRight(), 0.01);
      assertEquals(245538.50364985812, rectangle2D0.getCenterX(), 0.01);
      assertEquals(1481.7405786, rectangleInsets0.getBottom(), 0.01);
      assertEquals(604.897424, rectangle2D0.getCenterY(), 0.01);
      assertEquals(0.0, rectangleInsets0.getTop(), 0.01);
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets(0.0, 0.0, 1.0, 0.0);
      Rectangle rectangle0 = new Rectangle((-1), (-1));
      Rectangle2D rectangle2D0 = rectangleInsets0.createInsetRectangle((Rectangle2D) rectangle0);
      assertEquals((-1.0), rectangle2D0.getCenterY(), 0.01);
      assertEquals((-0.5), rectangle2D0.getCenterX(), 0.01);
      assertEquals(0.0, rectangleInsets0.getRight(), 0.01);
      assertEquals(0.0, rectangleInsets0.getLeft(), 0.01);
      assertEquals(0.0, rectangleInsets0.getTop(), 0.01);
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      RectangleInsets rectangleInsets0 = RectangleInsets.ZERO_INSETS;
      Line2D.Float line2D_Float0 = new Line2D.Float();
      Rectangle2D rectangle2D0 = line2D_Float0.getBounds2D();
      Rectangle2D rectangle2D1 = rectangleInsets0.ZERO_INSETS.createInsetRectangle(rectangle2D0, false, true);
      assertEquals(0.0, rectangle2D1.getCenterX(), 0.01);
      assertEquals(0.0, rectangle2D1.getCenterY(), 0.01);
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets();
      Rectangle2D.Float rectangle2D_Float0 = new Rectangle2D.Float();
      Rectangle rectangle0 = rectangle2D_Float0.getBounds();
      LengthAdjustmentType lengthAdjustmentType0 = LengthAdjustmentType.CONTRACT;
      Rectangle2D.Double rectangle2D_Double0 = (Rectangle2D.Double)rectangleInsets0.createAdjustedRectangle(rectangle0, lengthAdjustmentType0, lengthAdjustmentType0);
      assertEquals(0.0, rectangle2D_Double0.getCenterX(), 0.01);
      assertEquals((-2.0), rectangle2D_Double0.height, 0.01);
      assertEquals((-2.0), rectangle2D_Double0.width, 0.01);
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets(408.7049607942, 408.7049607942, 408.7049607942, 0.0);
      Rectangle2D.Double rectangle2D_Double0 = new Rectangle2D.Double((-2739.3595834), (-2143.388760238445), (-2739.3595834), (-831.7063895557276));
      LengthAdjustmentType lengthAdjustmentType0 = LengthAdjustmentType.CONTRACT;
      Rectangle2D.Double rectangle2D_Double1 = (Rectangle2D.Double)rectangleInsets0.createAdjustedRectangle(rectangle2D_Double0, lengthAdjustmentType0, lengthAdjustmentType0);
      assertEquals((-1649.1163111441274), rectangle2D_Double1.height, 0.01);
      assertEquals(0.0, rectangleInsets0.getRight(), 0.01);
      assertEquals((-3904.6868947028997), rectangle2D_Double1.getCenterX(), 0.01);
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets(0.0, 0.0, 0.0, 0.0);
      Rectangle rectangle0 = new Rectangle(0, (-4223), 631, 0);
      LengthAdjustmentType lengthAdjustmentType0 = LengthAdjustmentType.EXPAND;
      Rectangle2D.Double rectangle2D_Double0 = (Rectangle2D.Double)rectangleInsets0.createAdjustedRectangle(rectangle0, lengthAdjustmentType0, lengthAdjustmentType0);
      assertEquals(0.0, rectangleInsets0.getBottom(), 0.01);
      assertEquals(0.0, rectangleInsets0.getLeft(), 0.01);
      assertEquals(0.0, rectangleInsets0.getRight(), 0.01);
      assertEquals(0.0, rectangleInsets0.getTop(), 0.01);
      assertEquals(315.5, rectangle2D_Double0.getCenterX(), 0.01);
      assertEquals((-4223.0), rectangle2D_Double0.y, 0.01);
      assertEquals(0.0, rectangle2D_Double0.x, 0.01);
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      RectangleInsets rectangleInsets0 = RectangleInsets.ZERO_INSETS;
      Point point0 = new Point(29, 29);
      Rectangle rectangle0 = new Rectangle(point0);
      LengthAdjustmentType lengthAdjustmentType0 = LengthAdjustmentType.CONTRACT;
      LengthAdjustmentType lengthAdjustmentType1 = LengthAdjustmentType.EXPAND;
      Rectangle2D.Double rectangle2D_Double0 = (Rectangle2D.Double)rectangleInsets0.createAdjustedRectangle(rectangle0, lengthAdjustmentType0, lengthAdjustmentType1);
      assertEquals(0.0, rectangle2D_Double0.width, 0.01);
      assertEquals(29.0, rectangle2D_Double0.getMaxX(), 0.01);
      assertEquals(29.0, rectangle2D_Double0.y, 0.01);
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      UnitType unitType0 = UnitType.RELATIVE;
      RectangleInsets rectangleInsets0 = new RectangleInsets(unitType0, (-2392.1), (-2143.388760238445), (-2143.388760238445), (-2143.388760238445));
      rectangleInsets0.hashCode();
      assertEquals((-2143.388760238445), rectangleInsets0.getLeft(), 0.01);
      assertEquals((-2392.1), rectangleInsets0.getTop(), 0.01);
      assertEquals((-2143.388760238445), rectangleInsets0.getBottom(), 0.01);
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      UnitType unitType0 = UnitType.RELATIVE;
      RectangleInsets rectangleInsets0 = new RectangleInsets(unitType0, 0.0, (-2392.1), (-2652.2), 0.0);
      rectangleInsets0.hashCode();
      assertEquals((-2652.2), rectangleInsets0.getBottom(), 0.01);
      assertEquals(0.0, rectangleInsets0.getTop(), 0.01);
      assertEquals((-2392.1), rectangleInsets0.getLeft(), 0.01);
      assertEquals(0.0, rectangleInsets0.getRight(), 0.01);
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      UnitType unitType0 = UnitType.RELATIVE;
      RectangleInsets rectangleInsets0 = new RectangleInsets(unitType0, (-2974.22229848238), (-307.79414), 0.0, 475.69013592);
      rectangleInsets0.hashCode();
      assertEquals((-2974.22229848238), rectangleInsets0.getTop(), 0.01);
      assertEquals(475.69013592, rectangleInsets0.getRight(), 0.01);
      assertEquals((-307.79414), rectangleInsets0.getLeft(), 0.01);
      assertEquals(0.0, rectangleInsets0.getBottom(), 0.01);
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets();
      UnitType unitType0 = UnitType.ABSOLUTE;
      RectangleInsets rectangleInsets1 = new RectangleInsets(unitType0, (-975.9), 1.0, 1.0, 1.0);
      boolean boolean0 = rectangleInsets0.equals(rectangleInsets1);
      assertEquals(1.0, rectangleInsets1.getBottom(), 0.01);
      assertFalse(boolean0);
      assertEquals(1.0, rectangleInsets0.getTop(), 0.01);
      assertEquals(1.0, rectangleInsets1.getRight(), 0.01);
      assertEquals(1.0, rectangleInsets1.getLeft(), 0.01);
      assertFalse(rectangleInsets1.equals((Object)rectangleInsets0));
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets();
      UnitType unitType0 = UnitType.ABSOLUTE;
      RectangleInsets rectangleInsets1 = new RectangleInsets(unitType0, (-2143.388760238445), 1.0, (-2143.388760238445), (-829.95484055067));
      boolean boolean0 = rectangleInsets1.equals(rectangleInsets0);
      assertEquals((-2143.388760238445), rectangleInsets1.getBottom(), 0.01);
      assertEquals((-829.95484055067), rectangleInsets1.getRight(), 0.01);
      assertEquals((-2143.388760238445), rectangleInsets1.getTop(), 0.01);
      assertEquals(1.0, rectangleInsets1.getLeft(), 0.01);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      RectangleInsets rectangleInsets0 = RectangleInsets.ZERO_INSETS;
      RectangleInsets rectangleInsets1 = new RectangleInsets();
      boolean boolean0 = rectangleInsets0.equals(rectangleInsets1);
      assertEquals(1.0, rectangleInsets1.getRight(), 0.01);
      assertFalse(boolean0);
      assertEquals(1.0, rectangleInsets1.getTop(), 0.01);
      assertEquals(1.0, rectangleInsets1.getLeft(), 0.01);
      assertEquals(1.0, rectangleInsets1.getBottom(), 0.01);
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      RectangleInsets rectangleInsets0 = RectangleInsets.ZERO_INSETS;
      double double0 = rectangleInsets0.trimWidth(0.0);
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      UnitType unitType0 = UnitType.ABSOLUTE;
      RectangleInsets rectangleInsets0 = new RectangleInsets(unitType0, 0.0, 0.0, 0.0, (-2422.8348));
      double double0 = rectangleInsets0.trimWidth(0.0);
      assertEquals(0.0, rectangleInsets0.getLeft(), 0.01);
      assertEquals(0.0, rectangleInsets0.getBottom(), 0.01);
      assertEquals(0.0, rectangleInsets0.getTop(), 0.01);
      assertEquals(2422.8348, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      RectangleInsets rectangleInsets0 = RectangleInsets.ZERO_INSETS;
      double double0 = rectangleInsets0.trimHeight(0.0);
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      UnitType unitType0 = UnitType.ABSOLUTE;
      RectangleInsets rectangleInsets0 = new RectangleInsets(unitType0, (-1.0), (-1.0), (-1.0), (-1.0));
      double double0 = rectangleInsets0.trimHeight(1332.0);
      assertEquals(1334.0, double0, 0.01);
      assertEquals((-1.0), rectangleInsets0.getRight(), 0.01);
      assertEquals((-1.0), rectangleInsets0.getLeft(), 0.01);
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets();
      double double0 = rectangleInsets0.getTop();
      assertEquals(1.0, rectangleInsets0.getBottom(), 0.01);
      assertEquals(1.0, rectangleInsets0.getRight(), 0.01);
      assertEquals(1.0, rectangleInsets0.getLeft(), 0.01);
      assertEquals(1.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      UnitType unitType0 = UnitType.RELATIVE;
      RectangleInsets rectangleInsets0 = new RectangleInsets(unitType0, (-2392.1), (-2143.388760238445), (-2143.388760238445), (-2143.388760238445));
      double double0 = rectangleInsets0.getTop();
      assertEquals((-2143.388760238445), rectangleInsets0.getBottom(), 0.01);
      assertEquals((-2143.388760238445), rectangleInsets0.getRight(), 0.01);
      assertEquals((-2143.388760238445), rectangleInsets0.getLeft(), 0.01);
      assertEquals((-2392.1), double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      UnitType unitType0 = UnitType.ABSOLUTE;
      RectangleInsets rectangleInsets0 = new RectangleInsets(unitType0, (-895.86), (-895.86), (-895.86), 2213.02353994);
      double double0 = rectangleInsets0.getRight();
      assertEquals(2213.02353994, double0, 0.01);
      assertEquals((-895.86), rectangleInsets0.getBottom(), 0.01);
      assertEquals((-895.86), rectangleInsets0.getTop(), 0.01);
      assertEquals((-895.86), rectangleInsets0.getLeft(), 0.01);
  }

  @Test(timeout = 4000)
  public void test22()  throws Throwable  {
      UnitType unitType0 = UnitType.ABSOLUTE;
      RectangleInsets rectangleInsets0 = new RectangleInsets(unitType0, 0.0, 0.0, 0.0, (-2422.8348));
      double double0 = rectangleInsets0.getRight();
      assertEquals(0.0, rectangleInsets0.getTop(), 0.01);
      assertEquals((-2422.8348), double0, 0.01);
      assertEquals(0.0, rectangleInsets0.getLeft(), 0.01);
      assertEquals(0.0, rectangleInsets0.getBottom(), 0.01);
  }

  @Test(timeout = 4000)
  public void test23()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets();
      double double0 = rectangleInsets0.getLeft();
      assertEquals(1.0, rectangleInsets0.getTop(), 0.01);
      assertEquals(1.0, rectangleInsets0.getRight(), 0.01);
      assertEquals(1.0, double0, 0.01);
      assertEquals(1.0, rectangleInsets0.getBottom(), 0.01);
  }

  @Test(timeout = 4000)
  public void test24()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets((-1686.413838154), (-1686.413838154), (-1686.413838154), (-1686.413838154));
      double double0 = rectangleInsets0.getLeft();
      assertEquals((-1686.413838154), double0, 0.01);
      assertEquals((-1686.413838154), rectangleInsets0.getBottom(), 0.01);
      assertEquals((-1686.413838154), rectangleInsets0.getRight(), 0.01);
      assertEquals((-1686.413838154), rectangleInsets0.getTop(), 0.01);
  }

  @Test(timeout = 4000)
  public void test25()  throws Throwable  {
      RectangleInsets rectangleInsets0 = RectangleInsets.ZERO_INSETS;
      double double0 = rectangleInsets0.getBottom();
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test26()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets();
      double double0 = rectangleInsets0.getBottom();
      assertEquals(1.0, rectangleInsets0.getTop(), 0.01);
      assertEquals(1.0, rectangleInsets0.getRight(), 0.01);
      assertEquals(1.0, double0, 0.01);
      assertEquals(1.0, rectangleInsets0.getLeft(), 0.01);
  }

  @Test(timeout = 4000)
  public void test27()  throws Throwable  {
      UnitType unitType0 = UnitType.RELATIVE;
      RectangleInsets rectangleInsets0 = new RectangleInsets(unitType0, 0.0, 0.0, 0.0, 0.0);
      double double0 = rectangleInsets0.extendWidth(0.0);
      assertEquals(0.0, rectangleInsets0.getLeft(), 0.01);
      assertEquals(0.0, double0, 0.01);
      assertEquals(0.0, rectangleInsets0.getBottom(), 0.01);
      assertEquals(0.0, rectangleInsets0.getRight(), 0.01);
      assertEquals(0.0, rectangleInsets0.getTop(), 0.01);
  }

  @Test(timeout = 4000)
  public void test28()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets();
      double double0 = rectangleInsets0.extendWidth(647.7766);
      assertEquals(649.7766, double0, 0.01);
      assertEquals(1.0, rectangleInsets0.getTop(), 0.01);
      assertEquals(1.0, rectangleInsets0.getBottom(), 0.01);
  }

  @Test(timeout = 4000)
  public void test29()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets(0.0, 0.0, 0.0, 0.0);
      double double0 = rectangleInsets0.extendHeight(0.0);
      assertEquals(0.0, double0, 0.01);
      assertEquals(0.0, rectangleInsets0.getBottom(), 0.01);
      assertEquals(0.0, rectangleInsets0.getRight(), 0.01);
      assertEquals(0.0, rectangleInsets0.getTop(), 0.01);
      assertEquals(0.0, rectangleInsets0.getLeft(), 0.01);
  }

  @Test(timeout = 4000)
  public void test30()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets((-1686.413838154), (-1686.413838154), (-1686.413838154), (-1686.413838154));
      double double0 = rectangleInsets0.extendHeight((-1686.413838154));
      assertEquals((-1686.413838154), rectangleInsets0.getLeft(), 0.01);
      assertEquals((-1686.413838154), rectangleInsets0.getRight(), 0.01);
      assertEquals((-5059.241514462), double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test31()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets(0.0, 0.0, 1.0, 0.0);
      Rectangle rectangle0 = new Rectangle(1932, 0, 0, 136);
      Rectangle2D rectangle2D0 = rectangleInsets0.ZERO_INSETS.createOutsetRectangle((Rectangle2D) rectangle0, true, true);
      assertEquals(1.0, rectangleInsets0.getBottom(), 0.01);
      assertEquals(1932.0, rectangle2D0.getMaxX(), 0.01);
      assertEquals(0.0, rectangleInsets0.getRight(), 0.01);
      assertEquals(0.0, rectangleInsets0.getTop(), 0.01);
      assertEquals(68.0, rectangle2D0.getCenterY(), 0.01);
      assertEquals(0.0, rectangleInsets0.getLeft(), 0.01);
  }

  @Test(timeout = 4000)
  public void test32()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets();
      Rectangle2D.Float rectangle2D_Float0 = new Rectangle2D.Float();
      Rectangle2D.Double rectangle2D_Double0 = (Rectangle2D.Double)rectangleInsets0.createOutsetRectangle((Rectangle2D) rectangle2D_Float0, false, false);
      assertEquals(1.0, rectangleInsets0.getTop(), 0.01);
      assertEquals(0.0, rectangle2D_Double0.x, 0.01);
      assertEquals(0.0, rectangle2D_Double0.height, 0.01);
      assertEquals(0.0, rectangle2D_Double0.width, 0.01);
      assertEquals(0.0, rectangle2D_Double0.getCenterY(), 0.01);
      assertEquals(1.0, rectangleInsets0.getLeft(), 0.01);
      assertEquals(1.0, rectangleInsets0.getRight(), 0.01);
      assertEquals(1.0, rectangleInsets0.getBottom(), 0.01);
  }

  @Test(timeout = 4000)
  public void test33()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets();
      Rectangle2D.Double rectangle2D_Double0 = new Rectangle2D.Double(0.0, (-597.686061), 1212.41867296, (-597.686061));
      Rectangle2D.Double rectangle2D_Double1 = (Rectangle2D.Double)rectangleInsets0.createOutsetRectangle((Rectangle2D) rectangle2D_Double0, true, true);
      assertEquals(606.20933648, rectangle2D_Double1.getCenterX(), 0.01);
      assertEquals((-598.686061), rectangle2D_Double1.y, 0.01);
      assertEquals(1213.41867296, rectangle2D_Double1.getMaxX(), 0.01);
      assertEquals((-896.5290915), rectangle2D_Double1.getCenterY(), 0.01);
  }

  @Test(timeout = 4000)
  public void test34()  throws Throwable  {
      UnitType unitType0 = UnitType.ABSOLUTE;
      RectangleInsets rectangleInsets0 = new RectangleInsets(unitType0, (-895.86), (-895.86), (-895.86), 2213.02353994);
      Rectangle2D.Double rectangle2D_Double0 = new Rectangle2D.Double(0.0, 3640.0474, 1.0, 2789.659);
      Rectangle2D.Double rectangle2D_Double1 = (Rectangle2D.Double)rectangleInsets0.createOutsetRectangle((Rectangle2D) rectangle2D_Double0);
      assertEquals(5034.8769, rectangle2D_Double1.getCenterY(), 0.01);
      assertEquals(997.939, rectangle2D_Double1.height, 0.01);
      assertEquals(1554.9417699699998, rectangle2D_Double1.getCenterX(), 0.01);
  }

  @Test(timeout = 4000)
  public void test35()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets();
      Rectangle2D.Double rectangle2D_Double0 = new Rectangle2D.Double((-1122.397960326171), (-1481.79), (-1122.397960326171), 2200.101);
      Rectangle2D rectangle2D0 = rectangleInsets0.ZERO_INSETS.createInsetRectangle((Rectangle2D) rectangle2D_Double0, false, false);
      assertEquals(1.0, rectangleInsets0.getTop(), 0.01);
      assertTrue(rectangle2D0.equals((Object)rectangle2D_Double0));
      assertEquals(1.0, rectangleInsets0.getBottom(), 0.01);
      assertEquals(1.0, rectangleInsets0.getRight(), 0.01);
      assertEquals(1.0, rectangleInsets0.getLeft(), 0.01);
  }

  @Test(timeout = 4000)
  public void test36()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets((-2186.848108233), 0.0, (-1880.93718), (-3798.895689532));
      Line2D.Float line2D_Float0 = new Line2D.Float();
      Rectangle2D rectangle2D0 = line2D_Float0.getBounds2D();
      Rectangle2D rectangle2D1 = rectangleInsets0.createOutsetRectangle(rectangle2D0);
      Rectangle2D rectangle2D2 = rectangleInsets0.createInsetRectangle(rectangle2D1, false, false);
      assertEquals(0.0, rectangleInsets0.getLeft(), 0.01);
      assertEquals((-1899.447844766), rectangle2D2.getCenterX(), 0.01);
      assertEquals(152.95546411650002, rectangle2D2.getCenterY(), 0.01);
  }

  @Test(timeout = 4000)
  public void test37()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets();
      Rectangle2D.Double rectangle2D_Double0 = new Rectangle2D.Double(0.0, (-597.686061), 1212.41867296, (-597.686061));
      Rectangle2D.Double rectangle2D_Double1 = (Rectangle2D.Double)rectangleInsets0.createInsetRectangle((Rectangle2D) rectangle2D_Double0, true, true);
      assertEquals(606.20933648, rectangle2D_Double1.getCenterX(), 0.01);
      assertEquals((-599.686061), rectangle2D_Double1.height, 0.01);
      assertEquals((-896.5290915), rectangle2D_Double1.getCenterY(), 0.01);
      assertEquals(1211.41867296, rectangle2D_Double1.getMaxX(), 0.01);
  }

  @Test(timeout = 4000)
  public void test38()  throws Throwable  {
      UnitType unitType0 = UnitType.ABSOLUTE;
      RectangleInsets rectangleInsets0 = new RectangleInsets(unitType0, (-2143.388760238445), 1.0, (-2143.388760238445), (-829.95484055067));
      Rectangle2D.Float rectangle2D_Float0 = new Rectangle2D.Float();
      Rectangle2D.Double rectangle2D_Double0 = (Rectangle2D.Double)rectangleInsets0.createInsetRectangle((Rectangle2D) rectangle2D_Float0);
      assertEquals((-2143.388760238445), rectangle2D_Double0.y, 0.01);
      assertEquals(415.477420275335, rectangle2D_Double0.getCenterX(), 0.01);
      assertEquals(0.0, rectangle2D_Double0.getCenterY(), 0.01);
  }

  @Test(timeout = 4000)
  public void test39()  throws Throwable  {
      RectangleInsets rectangleInsets0 = RectangleInsets.ZERO_INSETS;
      Point point0 = new Point(29, 29);
      Rectangle rectangle0 = new Rectangle(point0);
      Rectangle2D rectangle2D0 = rectangleInsets0.createInsetRectangle((Rectangle2D) rectangle0);
      assertEquals(29.0, rectangle2D0.getMaxX(), 0.01);
      assertEquals(29.0, rectangle2D0.getCenterY(), 0.01);
  }

  @Test(timeout = 4000)
  public void test40()  throws Throwable  {
      RectangleInsets rectangleInsets0 = RectangleInsets.ZERO_INSETS;
      Rectangle2D.Float rectangle2D_Float0 = new Rectangle2D.Float();
      Rectangle2D.Double rectangle2D_Double0 = (Rectangle2D.Double)rectangleInsets0.createAdjustedRectangle(rectangle2D_Float0, (LengthAdjustmentType) null, (LengthAdjustmentType) null);
      assertEquals(0.0, rectangle2D_Double0.height, 0.01);
  }

  @Test(timeout = 4000)
  public void test41()  throws Throwable  {
      Rectangle rectangle0 = new Rectangle((-1391), 0, (-1391), (-271));
      LengthAdjustmentType lengthAdjustmentType0 = LengthAdjustmentType.EXPAND;
      RectangleInsets rectangleInsets0 = new RectangleInsets();
      Rectangle2D rectangle2D0 = rectangleInsets0.createAdjustedRectangle(rectangle0, lengthAdjustmentType0, lengthAdjustmentType0);
      Rectangle2D.Double rectangle2D_Double0 = (Rectangle2D.Double)rectangleInsets0.createInsetRectangle(rectangle2D0);
      assertEquals((-135.5), rectangle2D_Double0.getCenterY(), 0.01);
      assertEquals((-2086.5), rectangle2D0.getCenterX(), 0.01);
      assertEquals((-271.0), rectangle2D_Double0.height, 0.01);
      assertEquals((-2086.5), rectangle2D_Double0.getCenterX(), 0.01);
      assertEquals((-1391.0), rectangle2D_Double0.width, 0.01);
  }

  @Test(timeout = 4000)
  public void test42()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets((-1816.83), (-1816.83), 1633.317907936, (-1663.1));
      double double0 = rectangleInsets0.calculateTopOutset(1011.0);
      assertEquals((-1816.83), rectangleInsets0.getLeft(), 0.01);
      assertEquals((-1663.1), rectangleInsets0.getRight(), 0.01);
      assertEquals(1633.317907936, rectangleInsets0.getBottom(), 0.01);
      assertEquals((-1816.83), double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test43()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets(0.0, 0.0, 0.0, 0.0);
      double double0 = rectangleInsets0.calculateTopInset((-1885));
      assertEquals(0.0, rectangleInsets0.getBottom(), 0.01);
      assertEquals(0.0, rectangleInsets0.getLeft(), 0.01);
      assertEquals(0.0, double0, 0.01);
      assertEquals(0.0, rectangleInsets0.getRight(), 0.01);
  }

  @Test(timeout = 4000)
  public void test44()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets(0.0, 0.0, 0.0, 0.0);
      double double0 = rectangleInsets0.calculateRightOutset((-1036.9));
      assertEquals(0.0, rectangleInsets0.getLeft(), 0.01);
      assertEquals(0.0, double0, 0.01);
      assertEquals(0.0, rectangleInsets0.getTop(), 0.01);
      assertEquals(0.0, rectangleInsets0.getBottom(), 0.01);
  }

  @Test(timeout = 4000)
  public void test45()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets((-1816.83), (-1816.83), 1633.317907936, (-1663.1));
      double double0 = rectangleInsets0.calculateRightInset((-1212.6120059419902));
      assertEquals(1633.317907936, rectangleInsets0.getBottom(), 0.01);
      assertEquals((-1816.83), rectangleInsets0.getTop(), 0.01);
      assertEquals((-1663.1), double0, 0.01);
      assertEquals((-1816.83), rectangleInsets0.getLeft(), 0.01);
  }

  @Test(timeout = 4000)
  public void test46()  throws Throwable  {
      UnitType unitType0 = UnitType.ABSOLUTE;
      RectangleInsets rectangleInsets0 = new RectangleInsets(unitType0, 0.0, 0.0, 0.0, (-2422.8348));
      double double0 = rectangleInsets0.calculateLeftOutset(0.0F);
      assertEquals(0.0, double0, 0.01);
      assertEquals(0.0, rectangleInsets0.getTop(), 0.01);
      assertEquals(0.0, rectangleInsets0.getBottom(), 0.01);
      assertEquals((-2422.8348), rectangleInsets0.getRight(), 0.01);
  }

  @Test(timeout = 4000)
  public void test47()  throws Throwable  {
      UnitType unitType0 = UnitType.ABSOLUTE;
      RectangleInsets rectangleInsets0 = new RectangleInsets(unitType0, (-1.0), (-1.0), (-1.0), (-1.0));
      double double0 = rectangleInsets0.calculateLeftInset(0.0);
      assertEquals((-1.0), double0, 0.01);
      assertEquals((-1.0), rectangleInsets0.getBottom(), 0.01);
      assertEquals((-1.0), rectangleInsets0.getRight(), 0.01);
      assertEquals((-1.0), rectangleInsets0.getTop(), 0.01);
  }

  @Test(timeout = 4000)
  public void test48()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets(0.0, 0.0, 0.0, 0.0);
      double double0 = rectangleInsets0.calculateBottomOutset((-1.0));
      assertEquals(0.0, double0, 0.01);
      assertEquals(0.0, rectangleInsets0.getLeft(), 0.01);
      assertEquals(0.0, rectangleInsets0.getTop(), 0.01);
      assertEquals(0.0, rectangleInsets0.getRight(), 0.01);
  }

  @Test(timeout = 4000)
  public void test49()  throws Throwable  {
      RectangleInsets rectangleInsets0 = RectangleInsets.ZERO_INSETS;
      double double0 = rectangleInsets0.calculateBottomInset((-1427.6646927216));
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test50()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets((-2186.848108233), 0.0, (-1880.93718), (-3798.895689532));
      double double0 = rectangleInsets0.calculateBottomInset(4920.5506);
      assertEquals((-3798.895689532), rectangleInsets0.getRight(), 0.01);
      assertEquals(0.0, rectangleInsets0.getLeft(), 0.01);
      assertEquals((-1880.93718), double0, 0.01);
      assertEquals((-2186.848108233), rectangleInsets0.getTop(), 0.01);
  }

  @Test(timeout = 4000)
  public void test51()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets();
      // Undeclared exception!
      try { 
        rectangleInsets0.ZERO_INSETS.trim((Rectangle2D) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.jfree.chart.ui.RectangleInsets", e);
      }
  }

  @Test(timeout = 4000)
  public void test52()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets();
      // Undeclared exception!
      try { 
        rectangleInsets0.createOutsetRectangle((Rectangle2D) null);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Null 'base' argument.
         //
         verifyException("org.jfree.chart.ui.RectangleInsets", e);
      }
  }

  @Test(timeout = 4000)
  public void test53()  throws Throwable  {
      RectangleInsets rectangleInsets0 = RectangleInsets.ZERO_INSETS;
      // Undeclared exception!
      try { 
        rectangleInsets0.createInsetRectangle((Rectangle2D) null);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Null 'base' argument.
         //
         verifyException("org.jfree.chart.ui.RectangleInsets", e);
      }
  }

  @Test(timeout = 4000)
  public void test54()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets((-1686.413838154), (-1686.413838154), (-1686.413838154), (-1686.413838154));
      double double0 = rectangleInsets0.calculateRightOutset((-1686.413838154));
      assertEquals((-1686.413838154), double0, 0.01);
      assertEquals((-1686.413838154), rectangleInsets0.getTop(), 0.01);
      assertEquals((-1686.413838154), rectangleInsets0.getBottom(), 0.01);
      assertEquals((-1686.413838154), rectangleInsets0.getLeft(), 0.01);
  }

  @Test(timeout = 4000)
  public void test55()  throws Throwable  {
      UnitType unitType0 = UnitType.RELATIVE;
      RectangleInsets rectangleInsets0 = new RectangleInsets(unitType0, 1.0, (-2739.3595834), (-2739.3595834), 1.0);
      double double0 = rectangleInsets0.calculateRightOutset(1.0);
      assertEquals(3.6504882603211736E-4, double0, 0.01);
      assertEquals(1.0, rectangleInsets0.getTop(), 0.01);
      assertEquals((-2739.3595834), rectangleInsets0.getBottom(), 0.01);
  }

  @Test(timeout = 4000)
  public void test56()  throws Throwable  {
      UnitType unitType0 = UnitType.RELATIVE;
      RectangleInsets rectangleInsets0 = new RectangleInsets(unitType0, (-1.0), (-3919.237668064), (-1.0), (-523.560635));
      double double0 = rectangleInsets0.calculateRightInset(0.0);
      assertEquals((-1.0), rectangleInsets0.getBottom(), 0.01);
      assertEquals((-523.560635), rectangleInsets0.getRight(), 0.01);
      assertEquals((-3919.237668064), rectangleInsets0.getLeft(), 0.01);
      assertEquals((-1.0), rectangleInsets0.getTop(), 0.01);
      assertEquals(-0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test57()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets();
      double double0 = rectangleInsets0.calculateRightInset(0);
      assertEquals(1.0, rectangleInsets0.getLeft(), 0.01);
      assertEquals(1.0, rectangleInsets0.getTop(), 0.01);
      assertEquals(1.0, double0, 0.01);
      assertEquals(1.0, rectangleInsets0.getBottom(), 0.01);
  }

  @Test(timeout = 4000)
  public void test58()  throws Throwable  {
      UnitType unitType0 = UnitType.RELATIVE;
      RectangleInsets rectangleInsets0 = new RectangleInsets(unitType0, (-1.0), (-3919.237668064), (-1.0), (-523.560635));
      double double0 = rectangleInsets0.calculateLeftOutset(1.0);
      assertEquals((-0.8819566957757027), double0, 0.01);
      assertEquals((-1.0), rectangleInsets0.getBottom(), 0.01);
      assertEquals((-1.0), rectangleInsets0.getTop(), 0.01);
  }

  @Test(timeout = 4000)
  public void test59()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets();
      double double0 = rectangleInsets0.calculateLeftOutset(2508.0);
      assertEquals(1.0, rectangleInsets0.getTop(), 0.01);
      assertEquals(1.0, rectangleInsets0.getRight(), 0.01);
      assertEquals(1.0, rectangleInsets0.getBottom(), 0.01);
      assertEquals(1.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test60()  throws Throwable  {
      RectangleInsets rectangleInsets0 = RectangleInsets.ZERO_INSETS;
      double double0 = rectangleInsets0.calculateLeftInset(29);
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test61()  throws Throwable  {
      UnitType unitType0 = UnitType.RELATIVE;
      RectangleInsets rectangleInsets0 = new RectangleInsets(unitType0, (-2392.1), (-2143.388760238445), (-2143.388760238445), (-2143.388760238445));
      double double0 = rectangleInsets0.calculateLeftInset((-2392.1));
      assertEquals(5127200.253366384, double0, 0.01);
      assertEquals((-2143.388760238445), rectangleInsets0.getBottom(), 0.01);
      assertEquals((-2392.1), rectangleInsets0.getTop(), 0.01);
      assertEquals((-2143.388760238445), rectangleInsets0.getRight(), 0.01);
  }

  @Test(timeout = 4000)
  public void test62()  throws Throwable  {
      UnitType unitType0 = UnitType.RELATIVE;
      RectangleInsets rectangleInsets0 = new RectangleInsets(unitType0, (-2392.1), (-2143.388760238445), (-2143.388760238445), (-2143.388760238445));
      double double0 = rectangleInsets0.calculateBottomOutset(5127200.253366384);
      assertEquals((-2143.388760238445), rectangleInsets0.getLeft(), 0.01);
      assertEquals((-2422486.6356727364), double0, 0.01);
      assertEquals((-2143.388760238445), rectangleInsets0.getRight(), 0.01);
  }

  @Test(timeout = 4000)
  public void test63()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets();
      double double0 = rectangleInsets0.calculateBottomOutset(647.7766);
      assertEquals(1.0, rectangleInsets0.getRight(), 0.01);
      assertEquals(1.0, rectangleInsets0.getLeft(), 0.01);
      assertEquals(1.0, rectangleInsets0.getTop(), 0.01);
      assertEquals(1.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test64()  throws Throwable  {
      UnitType unitType0 = UnitType.RELATIVE;
      RectangleInsets rectangleInsets0 = new RectangleInsets(unitType0, (-1.0), 0.0, (-132.540610061), 0.0);
      double double0 = rectangleInsets0.calculateBottomInset((-1972.740096483));
      assertEquals(261468.1758796528, double0, 0.01);
      assertEquals(0.0, rectangleInsets0.getRight(), 0.01);
      assertEquals(0.0, rectangleInsets0.getLeft(), 0.01);
      assertEquals((-1.0), rectangleInsets0.getTop(), 0.01);
  }

  @Test(timeout = 4000)
  public void test65()  throws Throwable  {
      UnitType unitType0 = UnitType.RELATIVE;
      RectangleInsets rectangleInsets0 = new RectangleInsets(unitType0, (-2427.811407691812), 0.0, (-2427.811407691812), (-1449.66317168356));
      double double0 = rectangleInsets0.calculateTopOutset((-1672.5062));
      assertEquals((-1449.66317168356), rectangleInsets0.getRight(), 0.01);
      assertEquals(836.0809118083721, double0, 0.01);
      assertEquals(0.0, rectangleInsets0.getLeft(), 0.01);
  }

  @Test(timeout = 4000)
  public void test66()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets(0.0, 0.0, 0.0, 0.0);
      double double0 = rectangleInsets0.calculateTopOutset(631);
      assertEquals(0.0, double0, 0.01);
      assertEquals(0.0, rectangleInsets0.getLeft(), 0.01);
      assertEquals(0.0, rectangleInsets0.getRight(), 0.01);
      assertEquals(0.0, rectangleInsets0.getBottom(), 0.01);
  }

  @Test(timeout = 4000)
  public void test67()  throws Throwable  {
      UnitType unitType0 = UnitType.RELATIVE;
      RectangleInsets rectangleInsets0 = new RectangleInsets(unitType0, (-1.0), (-3919.237668064), (-1.0), (-523.560635));
      double double0 = rectangleInsets0.calculateTopInset(1213.9165078119);
      assertEquals((-1.0), rectangleInsets0.getBottom(), 0.01);
      assertEquals((-3919.237668064), rectangleInsets0.getLeft(), 0.01);
      assertEquals((-523.560635), rectangleInsets0.getRight(), 0.01);
      assertEquals((-1213.9165078119), double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test68()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets();
      double double0 = rectangleInsets0.calculateTopInset(0);
      assertEquals(1.0, rectangleInsets0.getBottom(), 0.01);
      assertEquals(1.0, rectangleInsets0.getRight(), 0.01);
      assertEquals(1.0, double0, 0.01);
      assertEquals(1.0, rectangleInsets0.getLeft(), 0.01);
  }

  @Test(timeout = 4000)
  public void test69()  throws Throwable  {
      UnitType unitType0 = UnitType.RELATIVE;
      RectangleInsets rectangleInsets0 = new RectangleInsets(unitType0, (-1364.2), (-132.540610061), (-1364.2), (-1364.2));
      Rectangle rectangle0 = new Rectangle();
      Rectangle2D.Double rectangle2D_Double0 = (Rectangle2D.Double)rectangleInsets0.createOutsetRectangle((Rectangle2D) rectangle0);
      Rectangle2D.Double rectangle2D_Double1 = (Rectangle2D.Double)rectangleInsets0.createOutsetRectangle((Rectangle2D) rectangle2D_Double0, false, true);
      assertEquals((-132.540610061), rectangleInsets0.getLeft(), 0.01);
      assertEquals(0.0, rectangle2D_Double1.getCenterX(), 0.01);
      assertEquals(0.0, rectangle2D_Double0.height, 0.01);
      assertEquals(0.0, rectangle2D_Double1.y, 0.01);
      assertEquals((-1364.2), rectangleInsets0.getRight(), 0.01);
      assertEquals((-1364.2), rectangleInsets0.getTop(), 0.01);
  }

  @Test(timeout = 4000)
  public void test70()  throws Throwable  {
      UnitType unitType0 = UnitType.RELATIVE;
      RectangleInsets rectangleInsets0 = new RectangleInsets(unitType0, (-2974.22229848238), (-307.79414), 0.0, 475.69013592);
      Rectangle rectangle0 = new Rectangle();
      Rectangle2D.Double rectangle2D_Double0 = (Rectangle2D.Double)rectangleInsets0.createInsetRectangle((Rectangle2D) rectangle0);
      assertEquals(0.0, rectangle2D_Double0.getCenterX(), 0.01);
      assertEquals(0.0, rectangle2D_Double0.y, 0.01);
      assertEquals(0.0, rectangle2D_Double0.x, 0.01);
      assertEquals((-2974.22229848238), rectangleInsets0.getTop(), 0.01);
      assertEquals(0.0, rectangleInsets0.getBottom(), 0.01);
      assertEquals(0.0, rectangle2D_Double0.getCenterY(), 0.01);
      assertEquals((-307.79414), rectangleInsets0.getLeft(), 0.01);
      assertEquals(0.0, rectangle2D_Double0.width, 0.01);
      assertEquals(475.69013592, rectangleInsets0.getRight(), 0.01);
  }

  @Test(timeout = 4000)
  public void test71()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets();
      Point point0 = new Point((-329), 1544);
      Dimension dimension0 = new Dimension();
      Rectangle rectangle0 = new Rectangle(point0, dimension0);
      Rectangle2D rectangle2D0 = rectangleInsets0.createOutsetRectangle((Rectangle2D) rectangle0, true, true);
      Rectangle2D.Double rectangle2D_Double0 = (Rectangle2D.Double)rectangleInsets0.ZERO_INSETS.createOutsetRectangle(rectangle2D0, false, false);
      assertEquals((-329.0), rectangle2D_Double0.getCenterX(), 0.01);
      assertEquals((-328.0), rectangle2D_Double0.getMaxX(), 0.01);
      assertEquals(1544.0, rectangle2D_Double0.getCenterY(), 0.01);
      assertEquals(1543.0, rectangle2D_Double0.y, 0.01);
  }

  @Test(timeout = 4000)
  public void test72()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets();
      // Undeclared exception!
      try { 
        rectangleInsets0.createOutsetRectangle((Rectangle2D) null, true, true);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Null 'base' argument.
         //
         verifyException("org.jfree.chart.ui.RectangleInsets", e);
      }
  }

  @Test(timeout = 4000)
  public void test73()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets();
      // Undeclared exception!
      try { 
        rectangleInsets0.createInsetRectangle((Rectangle2D) null, true, true);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Null 'base' argument.
         //
         verifyException("org.jfree.chart.ui.RectangleInsets", e);
      }
  }

  @Test(timeout = 4000)
  public void test74()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets();
      LengthAdjustmentType lengthAdjustmentType0 = LengthAdjustmentType.CONTRACT;
      // Undeclared exception!
      try { 
        rectangleInsets0.createAdjustedRectangle((Rectangle2D) null, lengthAdjustmentType0, lengthAdjustmentType0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Null 'base' argument.
         //
         verifyException("org.jfree.chart.ui.RectangleInsets", e);
      }
  }

  @Test(timeout = 4000)
  public void test75()  throws Throwable  {
      RectangleInsets rectangleInsets0 = RectangleInsets.ZERO_INSETS;
      rectangleInsets0.hashCode();
  }

  @Test(timeout = 4000)
  public void test76()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets();
      rectangleInsets0.hashCode();
      assertEquals(1.0, rectangleInsets0.getRight(), 0.01);
      assertEquals(1.0, rectangleInsets0.getLeft(), 0.01);
      assertEquals(1.0, rectangleInsets0.getTop(), 0.01);
  }

  @Test(timeout = 4000)
  public void test77()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets();
      UnitType unitType0 = UnitType.ABSOLUTE;
      RectangleInsets rectangleInsets1 = new RectangleInsets(unitType0, 2356.43996933, 1.0, (-329), 1.0);
      boolean boolean0 = rectangleInsets0.equals(rectangleInsets1);
      assertEquals(2356.43996933, rectangleInsets1.getTop(), 0.01);
      assertEquals(1.0, rectangleInsets1.getRight(), 0.01);
      assertEquals(1.0, rectangleInsets1.getLeft(), 0.01);
      assertEquals((-329.0), rectangleInsets1.getBottom(), 0.01);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test78()  throws Throwable  {
      RectangleInsets rectangleInsets0 = RectangleInsets.ZERO_INSETS;
      RectangleInsets rectangleInsets1 = new RectangleInsets(38.9, 0.0, (-1.0), (-867.754));
      boolean boolean0 = rectangleInsets0.equals(rectangleInsets1);
      assertFalse(boolean0);
      assertEquals((-867.754), rectangleInsets1.getRight(), 0.01);
      assertEquals((-1.0), rectangleInsets1.getBottom(), 0.01);
      assertEquals(38.9, rectangleInsets1.getTop(), 0.01);
      assertEquals(0.0, rectangleInsets1.getLeft(), 0.01);
  }

  @Test(timeout = 4000)
  public void test79()  throws Throwable  {
      RectangleInsets rectangleInsets0 = RectangleInsets.ZERO_INSETS;
      RectangleInsets rectangleInsets1 = new RectangleInsets();
      boolean boolean0 = rectangleInsets1.equals(rectangleInsets0);
      assertEquals(1.0, rectangleInsets1.getLeft(), 0.01);
      assertEquals(1.0, rectangleInsets1.getTop(), 0.01);
      assertEquals(1.0, rectangleInsets1.getRight(), 0.01);
      assertEquals(1.0, rectangleInsets1.getBottom(), 0.01);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test80()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets();
      UnitType unitType0 = UnitType.RELATIVE;
      RectangleInsets rectangleInsets1 = new RectangleInsets(unitType0, 192.5647, 192.5647, 192.5647, 192.5647);
      boolean boolean0 = rectangleInsets0.equals(rectangleInsets1);
      assertEquals(192.5647, rectangleInsets1.getRight(), 0.01);
      assertEquals(192.5647, rectangleInsets1.getLeft(), 0.01);
      assertFalse(boolean0);
      assertEquals(192.5647, rectangleInsets1.getTop(), 0.01);
      assertEquals(192.5647, rectangleInsets1.getBottom(), 0.01);
  }

  @Test(timeout = 4000)
  public void test81()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets();
      Object object0 = new Object();
      boolean boolean0 = rectangleInsets0.equals(object0);
      assertEquals(1.0, rectangleInsets0.getLeft(), 0.01);
      assertFalse(boolean0);
      assertEquals(1.0, rectangleInsets0.getTop(), 0.01);
      assertEquals(1.0, rectangleInsets0.getBottom(), 0.01);
      assertEquals(1.0, rectangleInsets0.getRight(), 0.01);
  }

  @Test(timeout = 4000)
  public void test82()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets();
      boolean boolean0 = rectangleInsets0.equals(rectangleInsets0);
      assertTrue(boolean0);
      assertEquals(1.0, rectangleInsets0.getTop(), 0.01);
      assertEquals(1.0, rectangleInsets0.getLeft(), 0.01);
      assertEquals(1.0, rectangleInsets0.getBottom(), 0.01);
      assertEquals(1.0, rectangleInsets0.getRight(), 0.01);
  }

  @Test(timeout = 4000)
  public void test83()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets();
      RectangleInsets rectangleInsets1 = new RectangleInsets();
      boolean boolean0 = rectangleInsets1.equals(rectangleInsets0);
      assertEquals(1.0, rectangleInsets1.getRight(), 0.01);
      assertEquals(1.0, rectangleInsets1.getLeft(), 0.01);
      assertEquals(1.0, rectangleInsets1.getBottom(), 0.01);
      assertTrue(boolean0);
      assertEquals(1.0, rectangleInsets1.getTop(), 0.01);
  }

  @Test(timeout = 4000)
  public void test84()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets(408.7049607942, 408.7049607942, 408.7049607942, 0.0);
      rectangleInsets0.getUnitType();
      assertEquals(408.7049607942, rectangleInsets0.getLeft(), 0.01);
      assertEquals(0.0, rectangleInsets0.getRight(), 0.01);
      assertEquals(408.7049607942, rectangleInsets0.getTop(), 0.01);
      assertEquals(408.7049607942, rectangleInsets0.getBottom(), 0.01);
  }

  @Test(timeout = 4000)
  public void test85()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets();
      double double0 = rectangleInsets0.trimWidth((-1.0));
      assertEquals(1.0, rectangleInsets0.getBottom(), 0.01);
      assertEquals(1.0, rectangleInsets0.getTop(), 0.01);
      assertEquals((-3.0), double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test86()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets();
      double double0 = rectangleInsets0.extendHeight(0.0);
      assertEquals(1.0, rectangleInsets0.getRight(), 0.01);
      assertEquals(2.0, double0, 0.01);
      assertEquals(1.0, rectangleInsets0.getLeft(), 0.01);
  }

  @Test(timeout = 4000)
  public void test87()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets((-1686.413838154), (-1686.413838154), (-1686.413838154), (-1686.413838154));
      double double0 = rectangleInsets0.getBottom();
      assertEquals((-1686.413838154), rectangleInsets0.getTop(), 0.01);
      assertEquals((-1686.413838154), double0, 0.01);
      assertEquals((-1686.413838154), rectangleInsets0.getRight(), 0.01);
      assertEquals((-1686.413838154), rectangleInsets0.getLeft(), 0.01);
  }

  @Test(timeout = 4000)
  public void test88()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets();
      double double0 = rectangleInsets0.trimHeight((-1122.397960326171));
      assertEquals(1.0, rectangleInsets0.getLeft(), 0.01);
      assertEquals(1.0, rectangleInsets0.getRight(), 0.01);
      assertEquals((-1124.397960326171), double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test89()  throws Throwable  {
      UnitType unitType0 = UnitType.RELATIVE;
      RectangleInsets rectangleInsets0 = new RectangleInsets(unitType0, 0.0, (-2392.1), (-2652.2), 0.0);
      double double0 = rectangleInsets0.extendWidth((-2652.2));
      assertEquals((-2652.2), rectangleInsets0.getBottom(), 0.01);
      assertEquals((-1.1082696084577037), double0, 0.01);
      assertEquals(0.0, rectangleInsets0.getRight(), 0.01);
      assertEquals(0.0, rectangleInsets0.getTop(), 0.01);
  }

  @Test(timeout = 4000)
  public void test90()  throws Throwable  {
      RectangleInsets rectangleInsets0 = RectangleInsets.ZERO_INSETS;
      double double0 = rectangleInsets0.getRight();
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test91()  throws Throwable  {
      RectangleInsets rectangleInsets0 = RectangleInsets.ZERO_INSETS;
      double double0 = rectangleInsets0.getLeft();
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test92()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets(408.7049607942, 408.7049607942, 408.7049607942, 0.0);
      Rectangle2D.Double rectangle2D_Double0 = new Rectangle2D.Double((-2739.3595834), (-2143.388760238445), (-2739.3595834), (-831.7063895557276));
      Rectangle2D.Double rectangle2D_Double1 = (Rectangle2D.Double)rectangleInsets0.createOutsetRectangle((Rectangle2D) rectangle2D_Double0);
      assertEquals((-4313.3918554971), rectangle2D_Double1.getCenterX(), 0.01);
      assertEquals((-14.296467967327544), rectangle2D_Double1.height, 0.01);
      assertEquals(0.0, rectangleInsets0.getRight(), 0.01);
      assertEquals((-2559.2419550163086), rectangle2D_Double1.getCenterY(), 0.01);
  }

  @Test(timeout = 4000)
  public void test93()  throws Throwable  {
      RectangleInsets rectangleInsets0 = new RectangleInsets();
      String string0 = rectangleInsets0.toString();
      assertEquals("RectangleInsets[t=1.0,l=1.0,b=1.0,r=1.0]", string0);
  }

  @Test(timeout = 4000)
  public void test94()  throws Throwable  {
      UnitType unitType0 = UnitType.RELATIVE;
      RectangleInsets rectangleInsets0 = new RectangleInsets(unitType0, 0.0, 1481.7405786, 1481.7405786, 0.0);
      double double0 = rectangleInsets0.getTop();
      assertEquals(1481.7405786, rectangleInsets0.getBottom(), 0.01);
      assertEquals(1481.7405786, rectangleInsets0.getLeft(), 0.01);
      assertEquals(0.0, double0, 0.01);
      assertEquals(0.0, rectangleInsets0.getRight(), 0.01);
  }
}
