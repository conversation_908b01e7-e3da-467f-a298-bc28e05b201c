/*
 * This file was automatically generated by EvoSuite
 * Wed Jun 18 07:26:08 GMT 2025
 */

package org.jfree.chart.renderer;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.awt.geom.Point2D;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.jfree.chart.renderer.Outlier;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true) 
public class Outlier_ESTest extends Outlier_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      Outlier outlier0 = new Outlier(1.0, 1.0, 1.0);
      outlier0.setRadius((-1.0));
      Point2D point2D0 = outlier0.getPoint();
      Outlier outlier1 = new Outlier(0.0, (-3226.1), 0.0);
      assertEquals((-3226.1), outlier1.getY(), 0.01);
      
      outlier1.setPoint(point2D0);
      boolean boolean0 = outlier0.equals(outlier1);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      Outlier outlier0 = new Outlier(2803.0168594, 3229.0, 2803.0168594);
      boolean boolean0 = outlier0.overlaps(outlier0);
      assertEquals(0.0, outlier0.getX(), 0.01);
      assertTrue(boolean0);
      assertEquals(425.98314059999984, outlier0.getY(), 0.01);
      assertEquals(2803.0168594, outlier0.getRadius(), 0.01);
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      Outlier outlier0 = new Outlier(3.753129060373438, 0.0, 0.0);
      Outlier outlier1 = new Outlier((-48.436526438927494), (-48.436526438927494), 1.0);
      assertEquals((-49.436526438927494), outlier1.getX(), 0.01);
      
      Point2D.Float point2D_Float0 = new Point2D.Float();
      outlier1.setPoint(point2D_Float0);
      int int0 = outlier0.compareTo(outlier1);
      assertEquals(1, int0);
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      Outlier outlier0 = new Outlier(0.0, (-3749.3), 1.0);
      assertEquals((-1.0), outlier0.getX(), 0.01);
      
      Point2D.Double point2D_Double0 = new Point2D.Double(0.0, 337.926143916536);
      outlier0.setPoint(point2D_Double0);
      Point2D.Double point2D_Double1 = new Point2D.Double();
      point2D_Double0.setLocation((Point2D) point2D_Double1);
      outlier0.getY();
      assertEquals(1.0, outlier0.getRadius(), 0.01);
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      Outlier outlier0 = new Outlier(2851.26334944819, (-323.9033570512465), 0.0);
      assertEquals((-323.9033570512465), outlier0.getY(), 0.01);
      
      Point2D.Float point2D_Float0 = new Point2D.Float(0.0F, 1.0F);
      outlier0.setPoint(point2D_Float0);
      outlier0.getY();
      assertEquals(0.0, outlier0.getRadius(), 0.01);
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      Outlier outlier0 = new Outlier(1.0, 1.0, 1.0);
      double double0 = outlier0.getX();
      assertEquals(0.0, double0, 0.01);
      assertEquals(0.0, outlier0.getY(), 0.01);
      assertEquals(1.0, outlier0.getRadius(), 0.01);
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      Outlier outlier0 = new Outlier(1875.078309754, (-1060.8870669370804), (-2811.6746408283484));
      double double0 = outlier0.getX();
      assertEquals(4686.752950582349, double0, 0.01);
      assertEquals(1750.787573891268, outlier0.getY(), 0.01);
      assertEquals((-2811.6746408283484), outlier0.getRadius(), 0.01);
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      Outlier outlier0 = new Outlier(0.0, 0.0, 0.0);
      double double0 = outlier0.getRadius();
      assertEquals(0.0, outlier0.getX(), 0.01);
      assertEquals(0.0, outlier0.getY(), 0.01);
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      Outlier outlier0 = new Outlier(1.1, 1.1, 1.1);
      double double0 = outlier0.getRadius();
      assertEquals(1.1, double0, 0.01);
      assertEquals(0.0, outlier0.getX(), 0.01);
      assertEquals(0.0, outlier0.getY(), 0.01);
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      Outlier outlier0 = new Outlier(1617.412106657169, 125.0, (-842.24));
      assertEquals(2459.652106657169, outlier0.getX(), 0.01);
      assertEquals(967.24, outlier0.getY(), 0.01);
      
      outlier0.setPoint((Point2D) null);
      outlier0.getPoint();
      assertEquals((-842.24), outlier0.getRadius(), 0.01);
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      Outlier outlier0 = new Outlier((-1302.0713863796482), (-1302.0713863796482), (-1302.0713863796482));
      outlier0.setPoint((Point2D) null);
      // Undeclared exception!
      try { 
        outlier0.toString();
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.jfree.chart.renderer.Outlier", e);
      }
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      Outlier outlier0 = new Outlier((-2067.05936986454), 297.1434140795, (-2067.05936986454));
      // Undeclared exception!
      try { 
        outlier0.overlaps((Outlier) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.jfree.chart.renderer.Outlier", e);
      }
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      Outlier outlier0 = new Outlier((-1302.0713863796482), (-1302.0713863796482), (-1302.0713863796482));
      // Undeclared exception!
      try { 
        outlier0.compareTo((Object) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.jfree.chart.renderer.Outlier", e);
      }
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      Outlier outlier0 = new Outlier(0.0, (-3749.3), 1.0);
      // Undeclared exception!
      try { 
        outlier0.compareTo("{0.0,337.926143916536}");
        fail("Expecting exception: ClassCastException");
      
      } catch(ClassCastException e) {
         //
         // java.lang.String cannot be cast to org.jfree.chart.renderer.Outlier
         //
         verifyException("org.jfree.chart.renderer.Outlier", e);
      }
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      Outlier outlier0 = new Outlier(0.0, (-3749.3), 1.0);
      double double0 = outlier0.getY();
      assertEquals((-1.0), outlier0.getX(), 0.01);
      assertEquals((-3750.3), double0, 0.01);
      assertEquals(1.0, outlier0.getRadius(), 0.01);
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      Outlier outlier0 = new Outlier(1.0, 1.0, 1.0);
      Point2D point2D0 = outlier0.getPoint();
      Outlier outlier1 = new Outlier((-2793.01), (-1.0), 0.0);
      assertEquals((-2793.01), outlier1.getX(), 0.01);
      
      outlier1.setPoint(point2D0);
      boolean boolean0 = outlier0.equals(outlier1);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      Outlier outlier0 = new Outlier(0.0, 0.0, 1.0);
      assertEquals((-1.0), outlier0.getX(), 0.01);
      
      Point2D.Float point2D_Float0 = new Point2D.Float(1431.837F, 1431.837F);
      outlier0.setPoint(point2D_Float0);
      Outlier outlier1 = new Outlier(0.0, 0.0, 1.0);
      boolean boolean0 = outlier0.equals(outlier1);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      Outlier outlier0 = new Outlier(0.0, 0.0, 1.0);
      Outlier outlier1 = new Outlier(0.0, 0.0, 1.0);
      boolean boolean0 = outlier0.equals(outlier1);
      assertEquals((-1.0), outlier1.getY(), 0.01);
      assertEquals((-1.0), outlier1.getX(), 0.01);
      assertTrue(boolean0);
      assertEquals(1.0, outlier1.getRadius(), 0.01);
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      Outlier outlier0 = new Outlier(2851.26334944819, (-323.9033570512465), 0.0);
      boolean boolean0 = outlier0.equals(outlier0);
      assertTrue(boolean0);
      assertEquals(0.0, outlier0.getRadius(), 0.01);
      assertEquals((-323.9033570512465), outlier0.getY(), 0.01);
      assertEquals(2851.26334944819, outlier0.getX(), 0.01);
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      Outlier outlier0 = new Outlier((-1705.4827), 0.0, 0.0);
      Object object0 = new Object();
      boolean boolean0 = outlier0.equals(object0);
      assertEquals(0.0, outlier0.getY(), 0.01);
      assertEquals((-1705.4827), outlier0.getX(), 0.01);
      assertFalse(boolean0);
      assertEquals(0.0, outlier0.getRadius(), 0.01);
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      Outlier outlier0 = new Outlier(0.0, 0.0, 0.0);
      Outlier outlier1 = new Outlier((-918.72), 0.0, (-918.72));
      boolean boolean0 = outlier0.overlaps(outlier1);
      assertEquals(918.72, outlier1.getY(), 0.01);
      assertFalse(boolean0);
      assertEquals(0.0, outlier1.getX(), 0.01);
      assertEquals(0.0, outlier0.getY(), 0.01);
      assertEquals((-918.72), outlier1.getRadius(), 0.01);
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      Outlier outlier0 = new Outlier(0.0, (-3749.3), 1.0);
      Point2D.Double point2D_Double0 = new Point2D.Double(0.0, 337.926143916536);
      outlier0.setPoint(point2D_Double0);
      Outlier outlier1 = new Outlier((-2431.42464), 1.0, (-1707.9123916479));
      assertEquals(1708.9123916479, outlier1.getY(), 0.01);
      
      Point2D.Double point2D_Double1 = new Point2D.Double(1.0, (-3303.664059));
      outlier1.setPoint(point2D_Double1);
      boolean boolean0 = outlier0.overlaps(outlier1);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test22()  throws Throwable  {
      Outlier outlier0 = new Outlier(2851.26334944819, (-323.9033570512465), 0.0);
      Outlier outlier1 = new Outlier((-323.9033570512465), 1197.243679, (-849.4760698));
      boolean boolean0 = outlier1.overlaps(outlier0);
      assertEquals(2046.7197488, outlier1.getY(), 0.01);
      assertEquals((-849.4760698), outlier1.getRadius(), 0.01);
      assertEquals(525.5727127487535, outlier1.getX(), 0.01);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test23()  throws Throwable  {
      Outlier outlier0 = new Outlier(2851.26334944819, (-323.9033570512465), 0.0);
      boolean boolean0 = outlier0.overlaps(outlier0);
      assertEquals(2851.26334944819, outlier0.getX(), 0.01);
      assertEquals((-323.9033570512465), outlier0.getY(), 0.01);
      assertEquals(0.0, outlier0.getRadius(), 0.01);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test24()  throws Throwable  {
      Outlier outlier0 = new Outlier((-1489.14575153908), (-1.0), (-1489.14575153908));
      boolean boolean0 = outlier0.overlaps(outlier0);
      assertEquals(1488.14575153908, outlier0.getY(), 0.01);
      assertEquals(0.0, outlier0.getX(), 0.01);
      assertFalse(boolean0);
      assertEquals((-1489.14575153908), outlier0.getRadius(), 0.01);
  }

  @Test(timeout = 4000)
  public void test25()  throws Throwable  {
      Outlier outlier0 = new Outlier(1761.58210536, 2.58210536, 2.58210536);
      Outlier outlier1 = new Outlier(579.08, 1.0, (-279.0));
      int int0 = outlier0.compareTo(outlier1);
      assertEquals(858.08, outlier1.getX(), 0.01);
      assertEquals((-1), int0);
      assertEquals((-279.0), outlier1.getRadius(), 0.01);
      assertEquals(280.0, outlier1.getY(), 0.01);
  }

  @Test(timeout = 4000)
  public void test26()  throws Throwable  {
      Outlier outlier0 = new Outlier((-1489.14575153908), (-1.0), (-1489.14575153908));
      Outlier outlier1 = new Outlier(1.0, 0.0, (-1641.2207));
      assertEquals(1641.2207, outlier1.getY(), 0.01);
      
      Point2D.Double point2D_Double0 = new Point2D.Double(0.0, 1.0);
      outlier1.setPoint(point2D_Double0);
      int int0 = outlier0.compareTo(outlier1);
      assertEquals(1, int0);
  }

  @Test(timeout = 4000)
  public void test27()  throws Throwable  {
      Outlier outlier0 = new Outlier(23.611686598432286, (-279.0), (-1759.0));
      int int0 = outlier0.compareTo(outlier0);
      assertEquals(0, int0);
      assertEquals(1782.6116865984322, outlier0.getX(), 0.01);
      assertEquals(1480.0, outlier0.getY(), 0.01);
      assertEquals((-1759.0), outlier0.getRadius(), 0.01);
  }

  @Test(timeout = 4000)
  public void test28()  throws Throwable  {
      Outlier outlier0 = new Outlier((-1489.14575153908), (-1.0), (-1489.14575153908));
      Outlier outlier1 = new Outlier(1.0, 0.0, (-1641.2207));
      int int0 = outlier0.compareTo(outlier1);
      assertEquals(1642.2207, outlier1.getX(), 0.01);
      assertEquals((-1), int0);
      assertEquals(1641.2207, outlier1.getY(), 0.01);
      assertEquals(1488.14575153908, outlier0.getY(), 0.01);
      assertEquals((-1641.2207), outlier1.getRadius(), 0.01);
  }

  @Test(timeout = 4000)
  public void test29()  throws Throwable  {
      Outlier outlier0 = new Outlier((-1489.14575153908), (-1.0), (-1489.14575153908));
      double double0 = outlier0.getRadius();
      assertEquals(1488.14575153908, outlier0.getY(), 0.01);
      assertEquals(0.0, outlier0.getX(), 0.01);
      assertEquals((-1489.14575153908), double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test30()  throws Throwable  {
      Outlier outlier0 = new Outlier(0.0, 0.0, 0.0);
      String string0 = outlier0.toString();
      assertEquals("{0.0,0.0}", string0);
      assertEquals(0.0, outlier0.getRadius(), 0.01);
  }

  @Test(timeout = 4000)
  public void test31()  throws Throwable  {
      Outlier outlier0 = new Outlier((-2811.6746408283484), 1875.078309754, (-1060.8870669370804));
      double double0 = outlier0.getX();
      assertEquals((-1060.8870669370804), outlier0.getRadius(), 0.01);
      assertEquals((-1750.787573891268), double0, 0.01);
      assertEquals(2935.9653766910806, outlier0.getY(), 0.01);
  }
}
