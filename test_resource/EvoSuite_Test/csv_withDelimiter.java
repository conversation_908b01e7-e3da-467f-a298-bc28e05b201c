/*
 * This file was automatically generated by EvoSuite
 * Wed Jun 18 08:37:06 GMT 2025
 */

package org.apache.commons.csv;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.shaded.org.mockito.Mockito.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.io.CharArrayWriter;
import java.io.PipedReader;
import java.io.PipedWriter;
import java.io.StringWriter;
import java.nio.BufferOverflowException;
import java.nio.CharBuffer;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.util.Locale;
import javax.sql.rowset.RowSetMetaDataImpl;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVPrinter;
import org.apache.commons.csv.DuplicateHeaderMode;
import org.apache.commons.csv.QuoteMode;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.evosuite.runtime.ViolatedAssumptionAnswer;
import org.evosuite.runtime.mock.java.io.MockFile;
import org.evosuite.runtime.mock.java.io.MockFileWriter;
import org.evosuite.runtime.mock.java.io.MockPrintWriter;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true) 
public class CSVFormat_ESTest extends CSVFormat_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test000()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.withCommentMarker('j');
      CSVFormat cSVFormat2 = cSVFormat1.withTrim(true);
      assertTrue(cSVFormat2.getTrim());
      assertEquals('j', (char)cSVFormat2.getCommentMarker());
  }

  @Test(timeout = 4000)
  public void test001()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat cSVFormat1 = cSVFormat0.EXCEL.withSkipHeaderRecord();
      CSVFormat cSVFormat2 = cSVFormat1.withTrim(false);
      assertTrue(cSVFormat2.getSkipHeaderRecord());
      assertFalse(cSVFormat2.getTrim());
  }

  @Test(timeout = 4000)
  public void test002()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      CSVFormat cSVFormat1 = cSVFormat0.withTrailingDelimiter();
      CSVFormat cSVFormat2 = cSVFormat1.withTrim();
      assertTrue(cSVFormat2.getTrim());
      assertTrue(cSVFormat2.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test003()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      CSVFormat cSVFormat1 = cSVFormat0.withSkipHeaderRecord();
      CSVFormat cSVFormat2 = cSVFormat1.withTrim();
      assertTrue(cSVFormat2.getSkipHeaderRecord());
      assertTrue(cSVFormat2.getTrim());
  }

  @Test(timeout = 4000)
  public void test004()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreSurroundingSpaces();
      CSVFormat cSVFormat2 = cSVFormat1.withTrim();
      assertTrue(cSVFormat2.getTrim());
      assertTrue(cSVFormat2.getIgnoreSurroundingSpaces());
  }

  @Test(timeout = 4000)
  public void test005()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat cSVFormat1 = cSVFormat0.withAutoFlush(true);
      CSVFormat cSVFormat2 = cSVFormat1.withTrim();
      assertTrue(cSVFormat2.getAutoFlush());
      assertTrue(cSVFormat2.getTrim());
  }

  @Test(timeout = 4000)
  public void test006()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat cSVFormat1 = cSVFormat0.POSTGRESQL_TEXT.withTrailingDelimiter(true);
      assertTrue(cSVFormat1.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test007()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.withTrim(true);
      CSVFormat cSVFormat2 = cSVFormat1.withTrailingDelimiter(true);
      assertTrue(cSVFormat2.getTrim());
      assertTrue(cSVFormat2.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test008()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withSkipHeaderRecord();
      CSVFormat cSVFormat2 = cSVFormat1.withTrailingDelimiter();
      assertTrue(cSVFormat2.getSkipHeaderRecord());
      assertTrue(cSVFormat2.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test009()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.EXCEL.withTrailingDelimiter();
      assertTrue(cSVFormat1.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test010()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.TDF.withSystemRecordSeparator();
      assertEquals("\t", cSVFormat1.getDelimiterString());
  }

  @Test(timeout = 4000)
  public void test011()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreHeaderCase();
      CSVFormat cSVFormat2 = cSVFormat1.withSkipHeaderRecord(false);
      assertTrue(cSVFormat2.getIgnoreHeaderCase());
      assertFalse(cSVFormat2.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test012()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withAllowDuplicateHeaderNames(false);
      CSVFormat cSVFormat2 = cSVFormat1.withSkipHeaderRecord(true);
      assertTrue(cSVFormat2.getSkipHeaderRecord());
      assertEquals(DuplicateHeaderMode.ALLOW_EMPTY, cSVFormat2.getDuplicateHeaderMode());
  }

  @Test(timeout = 4000)
  public void test013()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('d');
      CSVFormat cSVFormat1 = cSVFormat0.withSkipHeaderRecord();
      assertFalse(cSVFormat1.getIgnoreHeaderCase());
      assertFalse(cSVFormat1.equals((Object)cSVFormat0));
      assertFalse(cSVFormat1.getTrim());
      assertFalse(cSVFormat1.isNullStringSet());
      assertFalse(cSVFormat1.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat1.getAutoFlush());
      assertFalse(cSVFormat1.getAllowMissingColumnNames());
      assertEquals("d", cSVFormat1.getDelimiterString());
      assertFalse(cSVFormat1.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test014()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat cSVFormat1 = cSVFormat0.withAutoFlush(true);
      CSVFormat cSVFormat2 = cSVFormat1.withSkipHeaderRecord();
      assertTrue(cSVFormat2.getSkipHeaderRecord());
      assertTrue(cSVFormat2.getAutoFlush());
  }

  @Test(timeout = 4000)
  public void test015()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.withRecordSeparator("E6L0");
      assertFalse(cSVFormat1.getTrim());
  }

  @Test(timeout = 4000)
  public void test016()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat cSVFormat1 = cSVFormat0.withRecordSeparator('5');
      assertEquals("5", cSVFormat1.getRecordSeparator());
  }

  @Test(timeout = 4000)
  public void test017()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      CSVFormat cSVFormat1 = cSVFormat0.withRecordSeparator('{');
      assertEquals("{", cSVFormat1.getRecordSeparator());
  }

  @Test(timeout = 4000)
  public void test018()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreHeaderCase();
      CSVFormat cSVFormat2 = cSVFormat1.withRecordSeparator('{');
      assertEquals("{", cSVFormat2.getRecordSeparator());
      assertTrue(cSVFormat2.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test019()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.EXCEL.withRecordSeparator('u');
      assertEquals("u", cSVFormat1.getRecordSeparator());
  }

  @Test(timeout = 4000)
  public void test020()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      QuoteMode quoteMode0 = QuoteMode.ALL;
      CSVFormat cSVFormat1 = cSVFormat0.POSTGRESQL_TEXT.withQuoteMode(quoteMode0);
      assertFalse(cSVFormat1.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test021()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      CSVFormat cSVFormat1 = cSVFormat0.withTrailingDelimiter();
      QuoteMode quoteMode0 = QuoteMode.ALL_NON_NULL;
      CSVFormat cSVFormat2 = cSVFormat1.withQuoteMode(quoteMode0);
      assertTrue(cSVFormat2.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test022()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      QuoteMode quoteMode0 = QuoteMode.NON_NUMERIC;
      CSVFormat cSVFormat1 = cSVFormat0.withQuoteMode(quoteMode0);
      assertFalse(cSVFormat1.equals((Object)cSVFormat0));
  }

  @Test(timeout = 4000)
  public void test023()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      Character character0 = Character.valueOf('u');
      CSVFormat cSVFormat1 = cSVFormat0.withQuote(character0);
      assertFalse(cSVFormat1.isCommentMarkerSet());
  }

  @Test(timeout = 4000)
  public void test024()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      Character character0 = Character.valueOf('W');
      CSVFormat cSVFormat1 = cSVFormat0.withQuote(character0);
      assertFalse(cSVFormat1.getTrim());
  }

  @Test(timeout = 4000)
  public void test025()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.MYSQL.withQuote('#');
      assertEquals('#', (char)cSVFormat1.getQuoteCharacter());
  }

  @Test(timeout = 4000)
  public void test026()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat cSVFormat1 = cSVFormat0.withFirstRecordAsHeader();
      CSVFormat cSVFormat2 = cSVFormat1.withTrim(true);
      CSVFormat cSVFormat3 = cSVFormat2.withQuote('B');
      assertEquals('B', (char)cSVFormat3.getQuoteCharacter());
      assertTrue(cSVFormat3.getSkipHeaderRecord());
      assertTrue(cSVFormat3.getTrim());
  }

  @Test(timeout = 4000)
  public void test027()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreEmptyLines(true);
      CSVFormat cSVFormat2 = cSVFormat1.withQuote('f');
      assertTrue(cSVFormat2.getIgnoreEmptyLines());
      assertEquals('f', (char)cSVFormat2.getQuoteCharacter());
  }

  @Test(timeout = 4000)
  public void test028()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withNullString("u");
      assertNotSame(cSVFormat0, cSVFormat1);
  }

  @Test(timeout = 4000)
  public void test029()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.withFirstRecordAsHeader();
      CSVFormat cSVFormat2 = cSVFormat1.withNullString("E6L0");
      assertTrue(cSVFormat2.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test030()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat cSVFormat1 = cSVFormat0.withTrim();
      CSVFormat cSVFormat2 = cSVFormat1.withIgnoreSurroundingSpaces(false);
      assertTrue(cSVFormat2.getTrim());
      assertFalse(cSVFormat2.getIgnoreSurroundingSpaces());
  }

  @Test(timeout = 4000)
  public void test031()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreSurroundingSpaces(false);
      assertFalse(cSVFormat1.getIgnoreSurroundingSpaces());
  }

  @Test(timeout = 4000)
  public void test032()  throws Throwable  {
      CSVFormat.Predefined cSVFormat_Predefined0 = CSVFormat.Predefined.RFC4180;
      CSVFormat cSVFormat0 = cSVFormat_Predefined0.getFormat();
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreHeaderCase(true);
      CSVFormat cSVFormat2 = cSVFormat1.withIgnoreSurroundingSpaces(true);
      assertTrue(cSVFormat2.getIgnoreSurroundingSpaces());
      assertTrue(cSVFormat2.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test033()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreSurroundingSpaces();
      assertTrue(cSVFormat1.getIgnoreSurroundingSpaces());
  }

  @Test(timeout = 4000)
  public void test034()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withSkipHeaderRecord();
      CSVFormat cSVFormat2 = cSVFormat1.withIgnoreSurroundingSpaces();
      assertTrue(cSVFormat2.getSkipHeaderRecord());
      assertTrue(cSVFormat2.getIgnoreSurroundingSpaces());
  }

  @Test(timeout = 4000)
  public void test035()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreSurroundingSpaces();
      assertTrue(cSVFormat1.getIgnoreSurroundingSpaces());
  }

  @Test(timeout = 4000)
  public void test036()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat cSVFormat1 = cSVFormat0.EXCEL.withIgnoreSurroundingSpaces();
      assertTrue(cSVFormat1.getIgnoreSurroundingSpaces());
  }

  @Test(timeout = 4000)
  public void test037()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('`');
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreHeaderCase(true);
      assertFalse(cSVFormat1.getTrailingDelimiter());
      assertFalse(cSVFormat1.getAutoFlush());
      assertFalse(cSVFormat1.getSkipHeaderRecord());
      assertFalse(cSVFormat1.getAllowMissingColumnNames());
      assertFalse(cSVFormat1.equals((Object)cSVFormat0));
      assertEquals("`", cSVFormat1.getDelimiterString());
      assertFalse(cSVFormat1.getIgnoreEmptyLines());
      assertFalse(cSVFormat1.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat1.getTrim());
      assertFalse(cSVFormat1.isNullStringSet());
  }

  @Test(timeout = 4000)
  public void test038()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      CSVFormat cSVFormat1 = cSVFormat0.withTrim(true);
      CSVFormat cSVFormat2 = cSVFormat1.withIgnoreHeaderCase(true);
      assertTrue(cSVFormat2.getTrim());
      assertTrue(cSVFormat2.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test039()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      CSVFormat cSVFormat1 = cSVFormat0.withSkipHeaderRecord();
      CSVFormat cSVFormat2 = cSVFormat1.withIgnoreHeaderCase(false);
      assertTrue(cSVFormat2.getSkipHeaderRecord());
      assertFalse(cSVFormat2.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test040()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreHeaderCase();
      assertTrue(cSVFormat1.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test041()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat cSVFormat1 = cSVFormat0.withTrim();
      CSVFormat cSVFormat2 = cSVFormat1.withSystemRecordSeparator();
      CSVFormat cSVFormat3 = cSVFormat2.withIgnoreEmptyLines(true);
      assertTrue(cSVFormat3.getTrim());
      assertTrue(cSVFormat3.getIgnoreEmptyLines());
  }

  @Test(timeout = 4000)
  public void test042()  throws Throwable  {
      CSVFormat.Predefined cSVFormat_Predefined0 = CSVFormat.Predefined.RFC4180;
      CSVFormat cSVFormat0 = cSVFormat_Predefined0.getFormat();
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreSurroundingSpaces(true);
      CSVFormat cSVFormat2 = cSVFormat1.withIgnoreEmptyLines(false);
      assertFalse(cSVFormat2.getIgnoreEmptyLines());
      assertTrue(cSVFormat2.getIgnoreSurroundingSpaces());
  }

  @Test(timeout = 4000)
  public void test043()  throws Throwable  {
      CSVFormat.Predefined cSVFormat_Predefined0 = CSVFormat.Predefined.RFC4180;
      CSVFormat cSVFormat0 = cSVFormat_Predefined0.getFormat();
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreHeaderCase(true);
      CSVFormat cSVFormat2 = cSVFormat1.withIgnoreEmptyLines(false);
      assertTrue(cSVFormat2.getIgnoreHeaderCase());
      assertFalse(cSVFormat2.getIgnoreEmptyLines());
  }

  @Test(timeout = 4000)
  public void test044()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_CSV;
      Character character0 = new Character('Y');
      CSVFormat cSVFormat1 = cSVFormat0.withCommentMarker(character0);
      CSVFormat cSVFormat2 = cSVFormat1.withIgnoreEmptyLines();
      assertTrue(cSVFormat2.getIgnoreEmptyLines());
  }

  @Test(timeout = 4000)
  public void test045()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreEmptyLines();
      assertTrue(cSVFormat1.getIgnoreEmptyLines());
  }

  @Test(timeout = 4000)
  public void test046()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreEmptyLines();
      assertTrue(cSVFormat1.getIgnoreEmptyLines());
  }

  @Test(timeout = 4000)
  public void test047()  throws Throwable  {
      CSVFormat.Predefined cSVFormat_Predefined0 = CSVFormat.Predefined.RFC4180;
      CSVFormat cSVFormat0 = cSVFormat_Predefined0.getFormat();
      Object[] objectArray0 = new Object[0];
      CSVFormat cSVFormat1 = cSVFormat0.TDF.withHeaderComments(objectArray0);
      assertNull(cSVFormat1.getNullString());
  }

  @Test(timeout = 4000)
  public void test048()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      Object[] objectArray0 = new Object[7];
      CSVFormat cSVFormat1 = cSVFormat0.MONGODB_CSV.withHeaderComments(objectArray0);
      assertEquals('\"', (char)cSVFormat1.getEscapeCharacter());
  }

  @Test(timeout = 4000)
  public void test049()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      String[] stringArray0 = new String[9];
      CSVFormat cSVFormat1 = cSVFormat0.withHeader(stringArray0);
      assertFalse(cSVFormat1.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test050()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_CSV;
      RowSetMetaDataImpl rowSetMetaDataImpl0 = new RowSetMetaDataImpl();
      CSVFormat cSVFormat1 = cSVFormat0.POSTGRESQL_TEXT.withHeader((ResultSetMetaData) rowSetMetaDataImpl0);
      CSVFormat cSVFormat2 = cSVFormat1.withNullString("E6L0");
      assertFalse(cSVFormat2.getIgnoreSurroundingSpaces());
  }

  @Test(timeout = 4000)
  public void test051()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.withCommentMarker('j');
      CSVFormat cSVFormat2 = cSVFormat1.withHeader((ResultSetMetaData) null);
      assertEquals('j', (char)cSVFormat2.getCommentMarker());
  }

  @Test(timeout = 4000)
  public void test052()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      RowSetMetaDataImpl rowSetMetaDataImpl0 = new RowSetMetaDataImpl();
      CSVFormat cSVFormat1 = cSVFormat0.ORACLE.withHeader((ResultSetMetaData) rowSetMetaDataImpl0);
      assertNotSame(cSVFormat0, cSVFormat1);
  }

  @Test(timeout = 4000)
  public void test053()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat cSVFormat1 = cSVFormat0.withSkipHeaderRecord();
      CSVFormat cSVFormat2 = cSVFormat1.withHeader((ResultSetMetaData) null);
      assertTrue(cSVFormat2.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test054()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      RowSetMetaDataImpl rowSetMetaDataImpl0 = new RowSetMetaDataImpl();
      CSVFormat cSVFormat1 = cSVFormat0.withHeader((ResultSetMetaData) rowSetMetaDataImpl0);
      assertNull(cSVFormat1.getQuoteMode());
  }

  @Test(timeout = 4000)
  public void test055()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreHeaderCase();
      CSVFormat cSVFormat2 = cSVFormat1.withHeader((ResultSetMetaData) null);
      assertTrue(cSVFormat2.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test056()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat cSVFormat1 = cSVFormat0.withAutoFlush(true);
      CSVFormat cSVFormat2 = cSVFormat1.withHeader((ResultSetMetaData) null);
      assertTrue(cSVFormat2.getAutoFlush());
  }

  @Test(timeout = 4000)
  public void test057()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      ResultSet resultSet0 = mock(ResultSet.class, new ViolatedAssumptionAnswer());
      doReturn((ResultSetMetaData) null).when(resultSet0).getMetaData();
      CSVFormat cSVFormat1 = cSVFormat0.RFC4180.withHeader(resultSet0);
      assertFalse(cSVFormat1.getAllowMissingColumnNames());
  }

  @Test(timeout = 4000)
  public void test058()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MYSQL;
      Class<Locale.FilteringMode> class0 = Locale.FilteringMode.class;
      CSVFormat cSVFormat1 = cSVFormat0.withHeader(class0);
      assertFalse(cSVFormat1.equals((Object)cSVFormat0));
  }

  @Test(timeout = 4000)
  public void test059()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      Class<Locale.FilteringMode> class0 = Locale.FilteringMode.class;
      CSVFormat cSVFormat1 = cSVFormat0.withSkipHeaderRecord();
      CSVFormat cSVFormat2 = cSVFormat1.withHeader(class0);
      assertTrue(cSVFormat2.getSkipHeaderRecord());
      assertFalse(cSVFormat2.equals((Object)cSVFormat1));
  }

  @Test(timeout = 4000)
  public void test060()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreHeaderCase();
      Class<Locale.FilteringMode> class0 = Locale.FilteringMode.class;
      CSVFormat cSVFormat2 = cSVFormat1.withHeader(class0);
      assertTrue(cSVFormat2.getIgnoreHeaderCase());
      assertFalse(cSVFormat2.equals((Object)cSVFormat1));
  }

  @Test(timeout = 4000)
  public void test061()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat cSVFormat1 = cSVFormat0.withTrim();
      CSVFormat cSVFormat2 = cSVFormat1.withFirstRecordAsHeader();
      assertTrue(cSVFormat2.getTrim());
      assertTrue(cSVFormat2.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test062()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      CSVFormat cSVFormat1 = cSVFormat0.withTrailingDelimiter();
      CSVFormat cSVFormat2 = cSVFormat1.withFirstRecordAsHeader();
      assertTrue(cSVFormat2.getSkipHeaderRecord());
      assertTrue(cSVFormat2.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test063()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      CSVFormat cSVFormat1 = cSVFormat0.withFirstRecordAsHeader();
      assertTrue(cSVFormat1.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test064()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_CSV;
      Character character0 = Character.valueOf('7');
      CSVFormat cSVFormat1 = cSVFormat0.ORACLE.withEscape(character0);
      assertEquals(',', cSVFormat1.getDelimiter());
  }

  @Test(timeout = 4000)
  public void test065()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.POSTGRESQL_TEXT.withEscape('M');
      assertEquals('M', (char)cSVFormat1.getEscapeCharacter());
  }

  @Test(timeout = 4000)
  public void test066()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withAllowDuplicateHeaderNames(false);
      CSVFormat cSVFormat2 = cSVFormat1.withEscape('C');
      assertEquals('C', (char)cSVFormat2.getEscapeCharacter());
      assertEquals(DuplicateHeaderMode.ALLOW_EMPTY, cSVFormat2.getDuplicateHeaderMode());
  }

  @Test(timeout = 4000)
  public void test067()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      CSVFormat cSVFormat1 = cSVFormat0.withTrailingDelimiter();
      CSVFormat cSVFormat2 = cSVFormat1.withDelimiter(',');
      assertEquals(",", cSVFormat2.getDelimiterString());
      assertTrue(cSVFormat2.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test068()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      CSVFormat cSVFormat1 = cSVFormat0.TDF.withSkipHeaderRecord();
      CSVFormat cSVFormat2 = cSVFormat1.withDelimiter('?');
      assertTrue(cSVFormat2.getSkipHeaderRecord());
      assertEquals("?", cSVFormat2.getDelimiterString());
  }

  @Test(timeout = 4000)
  public void test069()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreHeaderCase();
      CSVFormat cSVFormat2 = cSVFormat1.withDelimiter('i');
      assertEquals("i", cSVFormat2.getDelimiterString());
      assertTrue(cSVFormat2.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test070()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      Character character0 = new Character('&');
      CSVFormat cSVFormat1 = cSVFormat0.withCommentMarker(character0);
      assertFalse(cSVFormat1.getAllowMissingColumnNames());
  }

  @Test(timeout = 4000)
  public void test071()  throws Throwable  {
      CSVFormat.Predefined cSVFormat_Predefined0 = CSVFormat.Predefined.RFC4180;
      CSVFormat cSVFormat0 = cSVFormat_Predefined0.getFormat();
      CSVFormat cSVFormat1 = cSVFormat0.DEFAULT.withAllowMissingColumnNames();
      Character character0 = cSVFormat1.getEscapeCharacter();
      CSVFormat cSVFormat2 = cSVFormat1.withCommentMarker(character0);
      assertTrue(cSVFormat2.getAllowMissingColumnNames());
  }

  @Test(timeout = 4000)
  public void test072()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      CSVFormat cSVFormat1 = cSVFormat0.withTrailingDelimiter();
      CSVFormat cSVFormat2 = cSVFormat1.withCommentMarker('C');
      assertTrue(cSVFormat2.getTrailingDelimiter());
      assertEquals('C', (char)cSVFormat2.getCommentMarker());
  }

  @Test(timeout = 4000)
  public void test073()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MYSQL;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreHeaderCase();
      CSVFormat cSVFormat2 = cSVFormat1.withHeaderComments((Object[]) null);
      CSVFormat cSVFormat3 = cSVFormat2.withCommentMarker('A');
      assertTrue(cSVFormat3.getIgnoreHeaderCase());
      assertEquals('A', (char)cSVFormat3.getCommentMarker());
  }

  @Test(timeout = 4000)
  public void test074()  throws Throwable  {
      CSVFormat.Predefined cSVFormat_Predefined0 = CSVFormat.Predefined.RFC4180;
      CSVFormat cSVFormat0 = cSVFormat_Predefined0.getFormat();
      CSVFormat cSVFormat1 = cSVFormat0.withAllowMissingColumnNames(true);
      CSVFormat cSVFormat2 = cSVFormat1.withCommentMarker('!');
      assertTrue(cSVFormat2.getAllowMissingColumnNames());
      assertEquals('!', (char)cSVFormat2.getCommentMarker());
  }

  @Test(timeout = 4000)
  public void test075()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      CSVFormat cSVFormat1 = cSVFormat0.withAllowDuplicateHeaderNames(false);
      CSVFormat cSVFormat2 = cSVFormat1.withCommentMarker('C');
      CSVFormat cSVFormat3 = cSVFormat2.withTrailingDelimiter(false);
      assertEquals('C', (char)cSVFormat3.getCommentMarker());
      assertFalse(cSVFormat3.getTrailingDelimiter());
      assertEquals(DuplicateHeaderMode.ALLOW_EMPTY, cSVFormat3.getDuplicateHeaderMode());
  }

  @Test(timeout = 4000)
  public void test076()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('v');
      CSVFormat cSVFormat1 = cSVFormat0.withAutoFlush(true);
      assertFalse(cSVFormat1.getTrailingDelimiter());
      assertFalse(cSVFormat1.getSkipHeaderRecord());
      assertFalse(cSVFormat1.equals((Object)cSVFormat0));
      assertFalse(cSVFormat1.getAllowMissingColumnNames());
      assertFalse(cSVFormat1.getTrim());
      assertFalse(cSVFormat1.isNullStringSet());
      assertFalse(cSVFormat1.getIgnoreEmptyLines());
      assertFalse(cSVFormat1.getIgnoreHeaderCase());
      assertFalse(cSVFormat1.getIgnoreSurroundingSpaces());
      assertEquals('v', cSVFormat1.getDelimiter());
  }

  @Test(timeout = 4000)
  public void test077()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      CSVFormat cSVFormat1 = cSVFormat0.withAutoFlush(true);
      assertTrue(cSVFormat1.getAutoFlush());
  }

  @Test(timeout = 4000)
  public void test078()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withAllowMissingColumnNames(true);
      QuoteMode quoteMode0 = QuoteMode.NON_NUMERIC;
      CSVFormat cSVFormat2 = cSVFormat1.withQuoteMode(quoteMode0);
      assertTrue(cSVFormat2.getAllowMissingColumnNames());
  }

  @Test(timeout = 4000)
  public void test079()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat cSVFormat1 = cSVFormat0.withSkipHeaderRecord(true);
      CSVFormat cSVFormat2 = cSVFormat1.withAllowMissingColumnNames(true);
      assertTrue(cSVFormat2.getSkipHeaderRecord());
      assertTrue(cSVFormat2.getAllowMissingColumnNames());
  }

  @Test(timeout = 4000)
  public void test080()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat cSVFormat1 = cSVFormat0.withAutoFlush(true);
      CSVFormat cSVFormat2 = cSVFormat1.withAllowMissingColumnNames(true);
      assertTrue(cSVFormat2.getAllowMissingColumnNames());
      assertTrue(cSVFormat2.getAutoFlush());
  }

  @Test(timeout = 4000)
  public void test081()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      CSVFormat cSVFormat1 = cSVFormat0.withAllowMissingColumnNames(false);
      assertFalse(cSVFormat1.getAllowMissingColumnNames());
  }

  @Test(timeout = 4000)
  public void test082()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      CSVFormat cSVFormat1 = cSVFormat0.withTrim(true);
      CSVFormat cSVFormat2 = cSVFormat1.withAllowMissingColumnNames();
      assertTrue(cSVFormat2.getAllowMissingColumnNames());
      assertTrue(cSVFormat2.getTrim());
  }

  @Test(timeout = 4000)
  public void test083()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      CSVFormat cSVFormat1 = cSVFormat0.withAllowMissingColumnNames();
      assertTrue(cSVFormat1.getAllowMissingColumnNames());
  }

  @Test(timeout = 4000)
  public void test084()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat cSVFormat1 = cSVFormat0.withAllowMissingColumnNames();
      CSVFormat cSVFormat2 = cSVFormat1.withDelimiter('v');
      assertEquals("v", cSVFormat2.getDelimiterString());
      assertTrue(cSVFormat2.getAllowMissingColumnNames());
  }

  @Test(timeout = 4000)
  public void test085()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat cSVFormat1 = cSVFormat0.withTrim(true);
      CSVFormat cSVFormat2 = cSVFormat1.withAllowDuplicateHeaderNames(true);
      assertTrue(cSVFormat2.getTrim());
      assertTrue(cSVFormat2.getAllowDuplicateHeaderNames());
  }

  @Test(timeout = 4000)
  public void test086()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      CSVFormat cSVFormat1 = cSVFormat0.TDF.withSkipHeaderRecord();
      CSVFormat cSVFormat2 = cSVFormat1.withAllowDuplicateHeaderNames(false);
      assertFalse(cSVFormat2.getAllowDuplicateHeaderNames());
      assertTrue(cSVFormat2.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test087()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withAllowDuplicateHeaderNames();
      assertEquals("\\N", cSVFormat1.getNullString());
  }

  @Test(timeout = 4000)
  public void test088()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('d');
      cSVFormat0.MONGODB_CSV.withAllowDuplicateHeaderNames();
      assertFalse(cSVFormat0.getTrailingDelimiter());
      assertFalse(cSVFormat0.getAutoFlush());
      assertFalse(cSVFormat0.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat0.getIgnoreEmptyLines());
      assertEquals('d', cSVFormat0.getDelimiter());
      assertFalse(cSVFormat0.getIgnoreHeaderCase());
      assertFalse(cSVFormat0.getTrim());
      assertFalse(cSVFormat0.getSkipHeaderRecord());
      assertFalse(cSVFormat0.getAllowMissingColumnNames());
  }

  @Test(timeout = 4000)
  public void test089()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.valueOf("RFC4180");
      assertFalse(cSVFormat0.isEscapeCharacterSet());
  }

  @Test(timeout = 4000)
  public void test090()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      String string0 = cSVFormat0.trim("");
      assertEquals("", string0);
  }

  @Test(timeout = 4000)
  public void test091()  throws Throwable  {
      String[] stringArray0 = CSVFormat.toStringArray((Object[]) null);
      assertNull(stringArray0);
  }

  @Test(timeout = 4000)
  public void test092()  throws Throwable  {
      Object[] objectArray0 = new Object[0];
      String[] stringArray0 = CSVFormat.toStringArray(objectArray0);
      assertEquals(0, stringArray0.length);
  }

  @Test(timeout = 4000)
  public void test093()  throws Throwable  {
      CSVFormat.Predefined cSVFormat_Predefined0 = CSVFormat.Predefined.RFC4180;
      CSVFormat cSVFormat0 = cSVFormat_Predefined0.getFormat();
      CSVFormat cSVFormat1 = cSVFormat0.withAllowMissingColumnNames(true);
      boolean boolean0 = cSVFormat1.getAllowMissingColumnNames();
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test094()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      Object[] objectArray0 = new Object[0];
      String string0 = cSVFormat0.format(objectArray0);
      assertEquals("", string0);
  }

  @Test(timeout = 4000)
  public void test095()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.copy();
      assertFalse(cSVFormat1.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test096()  throws Throwable  {
      Locale.FilteringMode[] locale_FilteringModeArray0 = new Locale.FilteringMode[9];
      Locale.FilteringMode[] locale_FilteringModeArray1 = CSVFormat.clone(locale_FilteringModeArray0);
      assertNotSame(locale_FilteringModeArray0, locale_FilteringModeArray1);
  }

  @Test(timeout = 4000)
  public void test097()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      CharBuffer charBuffer0 = CharBuffer.allocate(33);
      Object[] objectArray0 = new Object[5];
      objectArray0[0] = (Object) "The comment start character and the quoteChar cannot be the same ('";
      // Undeclared exception!
      try { 
        cSVFormat0.printRecord(charBuffer0, objectArray0);
        fail("Expecting exception: BufferOverflowException");
      
      } catch(BufferOverflowException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("java.nio.CharBuffer", e);
      }
  }

  @Test(timeout = 4000)
  public void test098()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      StringWriter stringWriter0 = new StringWriter();
      StringBuffer stringBuffer0 = stringWriter0.getBuffer();
      // Undeclared exception!
      try { 
        cSVFormat0.printRecord(stringBuffer0, (Object[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.csv.CSVFormat", e);
      }
  }

  @Test(timeout = 4000)
  public void test099()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      CharArrayWriter charArrayWriter0 = new CharArrayWriter(0);
      cSVFormat0.MONGODB_CSV.println(charArrayWriter0);
      assertEquals("\r\n", charArrayWriter0.toString());
      assertEquals(2, charArrayWriter0.size());
  }

  @Test(timeout = 4000)
  public void test100()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      boolean boolean0 = cSVFormat0.isQuoteCharacterSet();
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test101()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat cSVFormat1 = cSVFormat0.POSTGRESQL_TEXT.withFirstRecordAsHeader();
      cSVFormat1.isQuoteCharacterSet();
      assertTrue(cSVFormat1.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test102()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      boolean boolean0 = cSVFormat0.isEscapeCharacterSet();
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test103()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      boolean boolean0 = cSVFormat0.isEscapeCharacterSet();
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test104()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      boolean boolean0 = cSVFormat0.isCommentMarkerSet();
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test105()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      String[] stringArray0 = cSVFormat0.getHeaderComments();
      assertNull(stringArray0);
  }

  @Test(timeout = 4000)
  public void test106()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      Character character0 = Character.valueOf('4');
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setQuote(character0);
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test107()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      CSVFormat.Builder cSVFormat_Builder0 = cSVFormat0.MONGODB_TSV.builder();
      RowSetMetaDataImpl rowSetMetaDataImpl0 = new RowSetMetaDataImpl();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setHeader((ResultSetMetaData) rowSetMetaDataImpl0);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test108()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      ResultSet resultSet0 = mock(ResultSet.class, new ViolatedAssumptionAnswer());
      doReturn((ResultSetMetaData) null).when(resultSet0).getMetaData();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setHeader(resultSet0);
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test109()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      Class<Locale.FilteringMode> class0 = Locale.FilteringMode.class;
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setHeader(class0);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test110()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setDelimiter("Z5p-Y807/F+GWC");
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test111()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVFormat.Builder cSVFormat_Builder0 = cSVFormat0.builder();
      Character character0 = Character.valueOf('.');
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setCommentMarker(character0);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test112()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setSkipHeaderRecord(false);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test113()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      cSVFormat0.getQuoteMode();
  }

  @Test(timeout = 4000)
  public void test114()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setTrim(false);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test115()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      String[] stringArray0 = new String[2];
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setHeader(stringArray0);
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test116()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setAllowMissingColumnNames(true);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test117()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat cSVFormat0 = cSVFormat_Builder0.build();
      Character character0 = cSVFormat0.getCommentMarker();
      assertNull(character0);
  }

  @Test(timeout = 4000)
  public void test118()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setDelimiter('[');
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test119()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      boolean boolean0 = cSVFormat0.getIgnoreHeaderCase();
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test120()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setTrailingDelimiter(false);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test121()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      boolean boolean0 = cSVFormat0.getAutoFlush();
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test122()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      DuplicateHeaderMode duplicateHeaderMode0 = DuplicateHeaderMode.DISALLOW;
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setDuplicateHeaderMode(duplicateHeaderMode0);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test123()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setCommentMarker('?');
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test124()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      QuoteMode quoteMode0 = QuoteMode.MINIMAL;
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setQuoteMode(quoteMode0);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test125()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      boolean boolean0 = cSVFormat0.getIgnoreSurroundingSpaces();
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test126()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      CSVFormat cSVFormat1 = cSVFormat0.withAllowDuplicateHeaderNames(false);
      CSVFormat cSVFormat2 = cSVFormat1.withFirstRecordAsHeader();
      CSVFormat cSVFormat3 = cSVFormat2.withSkipHeaderRecord();
      assertTrue(cSVFormat3.getSkipHeaderRecord());
      assertEquals(DuplicateHeaderMode.ALLOW_EMPTY, cSVFormat3.getDuplicateHeaderMode());
      assertTrue(cSVFormat3.equals((Object)cSVFormat2));
  }

  @Test(timeout = 4000)
  public void test127()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      QuoteMode quoteMode0 = QuoteMode.NONE;
      // Undeclared exception!
      try { 
        cSVFormat0.withQuoteMode(quoteMode0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // No quotes mode set but no escape character is set
         //
         verifyException("org.apache.commons.csv.CSVFormat", e);
      }
  }

  @Test(timeout = 4000)
  public void test128()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_CSV;
      Character character0 = new Character('Y');
      CSVFormat cSVFormat1 = cSVFormat0.withCommentMarker(character0);
      // Undeclared exception!
      try { 
        cSVFormat1.withEscape(character0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The comment start and the escape character cannot be the same ('Y')
         //
         verifyException("org.apache.commons.csv.CSVFormat", e);
      }
  }

  @Test(timeout = 4000)
  public void test129()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      CSVFormat cSVFormat1 = cSVFormat0.withDelimiter('~');
      // Undeclared exception!
      try { 
        cSVFormat1.withCommentMarker('~');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The comment start character and the delimiter cannot be the same ('~')
         //
         verifyException("org.apache.commons.csv.CSVFormat", e);
      }
  }

  @Test(timeout = 4000)
  public void test130()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      // Undeclared exception!
      try { 
        cSVFormat0.withQuote(',');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The quoteChar character and the delimiter cannot be the same (',')
         //
         verifyException("org.apache.commons.csv.CSVFormat", e);
      }
  }

  @Test(timeout = 4000)
  public void test131()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      String string0 = cSVFormat0.trim("t$~jN0rqZyak");
      assertEquals("t$~jN0rqZyak", string0);
  }

  @Test(timeout = 4000)
  public void test132()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      CSVFormat cSVFormat1 = cSVFormat0.TDF.withSkipHeaderRecord();
      assertTrue(cSVFormat1.getSkipHeaderRecord());
      
      CSVFormat.Builder cSVFormat_Builder0 = cSVFormat0.MONGODB_TSV.builder();
      Object[] objectArray0 = new Object[3];
      objectArray0[1] = (Object) cSVFormat1;
      cSVFormat_Builder0.setHeaderComments(objectArray0);
      assertFalse(cSVFormat0.isCommentMarkerSet());
      assertTrue(cSVFormat0.isQuoteCharacterSet());
      assertFalse(cSVFormat0.isNullStringSet());
  }

  @Test(timeout = 4000)
  public void test133()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      String string0 = cSVFormat0.DEFAULT.toString();
      assertEquals("Delimiter=<,> QuoteChar=<\"> RecordSeparator=<\r\n> EmptyLines:ignored SkipHeaderRecord:false", string0);
  }

  @Test(timeout = 4000)
  public void test134()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      Class<Locale.FilteringMode> class0 = Locale.FilteringMode.class;
      CSVFormat cSVFormat1 = cSVFormat0.withHeader(class0);
      Object[] objectArray0 = new Object[0];
      String string0 = cSVFormat1.format(objectArray0);
      assertEquals("AUTOSELECT_FILTERING,EXTENDED_FILTERING,IGNORE_EXTENDED_RANGES,MAP_EXTENDED_RANGES,REJECT_EXTENDED_RANGES\r\n", string0);
      assertTrue(cSVFormat1.isQuoteCharacterSet());
  }

  @Test(timeout = 4000)
  public void test135()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      MockFileWriter mockFileWriter0 = new MockFileWriter("\u0085");
      Object[] objectArray0 = new Object[4];
      cSVFormat0.printRecord(mockFileWriter0, objectArray0);
      assertFalse(cSVFormat0.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test136()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.POSTGRESQL_TEXT.withDelimiter('`');
      Object[] objectArray0 = new Object[1];
      objectArray0[0] = (Object) cSVFormat0;
      cSVFormat1.format(objectArray0);
      assertEquals('`', cSVFormat1.getDelimiter());
  }

  @Test(timeout = 4000)
  public void test137()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      Object[] objectArray0 = new Object[5];
      objectArray0[2] = (Object) cSVFormat0;
      cSVFormat_Builder0.setHeaderComments(objectArray0);
      assertTrue(cSVFormat0.isNullStringSet());
      assertFalse(cSVFormat0.isCommentMarkerSet());
      assertTrue(cSVFormat0.isEscapeCharacterSet());
  }

  @Test(timeout = 4000)
  public void test138()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      Object[] objectArray0 = new Object[3];
      objectArray0[2] = (Object) "Delimiter=<,> QuoteChar=<\"> RecordSeparator=<\r\n> SkipHeaderRecord:false";
      String string0 = cSVFormat0.format(objectArray0);
      assertEquals("\\N,\\N,\"Delimiter=<,> QuoteChar=<\\\"> RecordSeparator=<\r\n> SkipHeaderRecord:false\"", string0);
  }

  @Test(timeout = 4000)
  public void test139()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      RowSetMetaDataImpl rowSetMetaDataImpl0 = new RowSetMetaDataImpl();
      CSVFormat cSVFormat1 = cSVFormat0.withHeader((ResultSetMetaData) rowSetMetaDataImpl0);
      CSVPrinter cSVPrinter0 = cSVFormat1.printer();
      assertNotNull(cSVPrinter0);
  }

  @Test(timeout = 4000)
  public void test140()  throws Throwable  {
      CSVFormat.Predefined cSVFormat_Predefined0 = CSVFormat.Predefined.RFC4180;
      CSVFormat cSVFormat0 = cSVFormat_Predefined0.getFormat();
      // Undeclared exception!
      try { 
        cSVFormat0.format((Object[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("java.util.Objects", e);
      }
  }

  @Test(timeout = 4000)
  public void test141()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('W');
      Object[] objectArray0 = new Object[2];
      String string0 = cSVFormat0.format(objectArray0);
      assertEquals("W", string0);
      assertFalse(cSVFormat0.getIgnoreEmptyLines());
      assertFalse(cSVFormat0.getIgnoreHeaderCase());
      assertFalse(cSVFormat0.getAllowMissingColumnNames());
      assertFalse(cSVFormat0.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat0.getTrim());
      assertFalse(cSVFormat0.getAutoFlush());
  }

  @Test(timeout = 4000)
  public void test142()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      Character character0 = new Character('4');
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setEscape(character0);
      CSVFormat cSVFormat1 = cSVFormat_Builder1.build();
      boolean boolean0 = cSVFormat0.equals(cSVFormat1);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test143()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      CSVFormat cSVFormat1 = cSVFormat_Builder0.build();
      boolean boolean0 = cSVFormat0.equals(cSVFormat1);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test144()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      Class<Locale.FilteringMode> class0 = Locale.FilteringMode.class;
      CSVFormat cSVFormat1 = cSVFormat0.MONGODB_TSV.withHeader(class0);
      boolean boolean0 = cSVFormat0.equals(cSVFormat1);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test145()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      Locale.FilteringMode locale_FilteringMode0 = Locale.FilteringMode.REJECT_EXTENDED_RANGES;
      boolean boolean0 = cSVFormat0.equals(locale_FilteringMode0);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test146()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat cSVFormat1 = cSVFormat0.EXCEL.withSkipHeaderRecord();
      boolean boolean0 = cSVFormat1.equals(cSVFormat0);
      assertFalse(boolean0);
      assertTrue(cSVFormat1.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test147()  throws Throwable  {
      boolean boolean0 = CSVFormat.isBlank("");
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test148()  throws Throwable  {
      boolean boolean0 = CSVFormat.isBlank("uO22");
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test149()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('d');
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      // Undeclared exception!
      try { 
        cSVFormat_Builder0.setDelimiter("");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The delimiter cannot be empty
         //
         verifyException("org.apache.commons.csv.CSVFormat$Builder", e);
      }
  }

  @Test(timeout = 4000)
  public void test150()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      // Undeclared exception!
      try { 
        cSVFormat_Builder0.setDelimiter("Delimiter=<,> QuoteChar=<\"> RecordSeparator=<\r\n> SkipHeaderRecord:false");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The delimiter cannot be a line break
         //
         verifyException("org.apache.commons.csv.CSVFormat$Builder", e);
      }
  }

  @Test(timeout = 4000)
  public void test151()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      CSVFormat cSVFormat1 = cSVFormat0.withEscape('^');
      assertEquals('^', (char)cSVFormat1.getEscapeCharacter());
  }

  @Test(timeout = 4000)
  public void test152()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      CSVFormat cSVFormat1 = cSVFormat0.withTrim(false);
      assertFalse(cSVFormat1.getTrim());
  }

  @Test(timeout = 4000)
  public void test153()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      String string0 = cSVFormat0.toString();
      assertEquals("Delimiter=<,> QuoteChar=<\"> RecordSeparator=<\r\n> SkipHeaderRecord:false", string0);
  }

  @Test(timeout = 4000)
  public void test154()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreEmptyLines();
      assertTrue(cSVFormat1.getIgnoreEmptyLines());
  }

  @Test(timeout = 4000)
  public void test155()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      CSVFormat cSVFormat1 = cSVFormat0.withAllowMissingColumnNames();
      CSVFormat cSVFormat2 = cSVFormat1.withIgnoreHeaderCase(true);
      assertTrue(cSVFormat2.getIgnoreHeaderCase());
      assertTrue(cSVFormat2.getAllowMissingColumnNames());
  }

  @Test(timeout = 4000)
  public void test156()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      CSVFormat cSVFormat1 = cSVFormat0.RFC4180.withAutoFlush(false);
      CSVFormat cSVFormat2 = cSVFormat1.withRecordSeparator("2uKh,|v3(nPQDSdv>");
      assertFalse(cSVFormat2.getAutoFlush());
  }

  @Test(timeout = 4000)
  public void test157()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_CSV;
      String[] stringArray0 = new String[3];
      CSVFormat cSVFormat1 = cSVFormat0.withHeader(stringArray0);
      assertNull(cSVFormat1.getNullString());
  }

  @Test(timeout = 4000)
  public void test158()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreEmptyLines(false);
      assertFalse(cSVFormat1.getIgnoreEmptyLines());
  }

  @Test(timeout = 4000)
  public void test159()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat cSVFormat1 = cSVFormat0.MONGODB_TSV.withIgnoreSurroundingSpaces(true);
      assertTrue(cSVFormat1.getIgnoreSurroundingSpaces());
  }

  @Test(timeout = 4000)
  public void test160()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MYSQL;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreHeaderCase();
      CSVFormat cSVFormat2 = cSVFormat1.withAllowMissingColumnNames(false);
      assertTrue(cSVFormat2.getIgnoreHeaderCase());
      assertFalse(cSVFormat2.getAllowMissingColumnNames());
  }

  @Test(timeout = 4000)
  public void test161()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreSurroundingSpaces();
      boolean boolean0 = cSVFormat1.getIgnoreSurroundingSpaces();
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test162()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      CSVFormat cSVFormat1 = cSVFormat0.withSkipHeaderRecord(false);
      assertFalse(cSVFormat1.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test163()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      MockFile mockFile0 = new MockFile("FRdJT1rX[&5}FKq@m");
      MockPrintWriter mockPrintWriter0 = new MockPrintWriter(mockFile0);
      CSVPrinter cSVPrinter0 = cSVFormat0.MONGODB_CSV.print((Appendable) mockPrintWriter0);
      assertNotNull(cSVPrinter0);
  }

  @Test(timeout = 4000)
  public void test164()  throws Throwable  {
      CSVFormat.Predefined cSVFormat_Predefined0 = CSVFormat.Predefined.RFC4180;
      CSVFormat cSVFormat0 = cSVFormat_Predefined0.getFormat();
      CSVFormat cSVFormat1 = cSVFormat0.withTrailingDelimiter(true);
      CSVFormat cSVFormat2 = cSVFormat1.withHeader((ResultSetMetaData) null);
      assertTrue(cSVFormat2.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test165()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      boolean boolean0 = cSVFormat0.getAllowMissingColumnNames();
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test166()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat cSVFormat0 = cSVFormat_Builder0.build();
      CSVFormat cSVFormat1 = cSVFormat0.withNullString("inputStream");
      assertEquals('\"', (char)cSVFormat1.getQuoteCharacter());
  }

  @Test(timeout = 4000)
  public void test167()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      PipedWriter pipedWriter0 = new PipedWriter();
      PipedReader pipedReader0 = new PipedReader(pipedWriter0);
      CSVParser cSVParser0 = cSVFormat0.parse(pipedReader0);
      assertNull(cSVParser0.getHeaderComment());
  }

  @Test(timeout = 4000)
  public void test168()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      Character character0 = Character.valueOf('z');
      CSVFormat cSVFormat1 = cSVFormat0.withEscape(character0);
      assertEquals(DuplicateHeaderMode.ALLOW_ALL, cSVFormat1.getDuplicateHeaderMode());
  }

  @Test(timeout = 4000)
  public void test169()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      CSVFormat cSVFormat1 = cSVFormat0.withQuote((Character) null);
      Object[] objectArray0 = new Object[2];
      objectArray0[0] = (Object) cSVFormat0;
      String string0 = cSVFormat1.format(objectArray0);
      assertNotNull(string0);
  }

  @Test(timeout = 4000)
  public void test170()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      ResultSet resultSet0 = mock(ResultSet.class, new ViolatedAssumptionAnswer());
      doReturn((ResultSetMetaData) null).when(resultSet0).getMetaData();
      CSVFormat cSVFormat1 = cSVFormat0.withHeader(resultSet0);
      assertFalse(cSVFormat1.getIgnoreSurroundingSpaces());
  }

  @Test(timeout = 4000)
  public void test171()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      String string0 = cSVFormat0.getRecordSeparator();
      assertEquals("\r\n", string0);
  }

  @Test(timeout = 4000)
  public void test172()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      Class<Locale.FilteringMode> class0 = Locale.FilteringMode.class;
      CSVFormat cSVFormat1 = cSVFormat0.withHeader(class0);
      assertFalse(cSVFormat1.equals((Object)cSVFormat0));
  }

  @Test(timeout = 4000)
  public void test173()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat(' ');
      boolean boolean0 = cSVFormat0.getIgnoreEmptyLines();
      assertFalse(cSVFormat0.getTrim());
      assertFalse(cSVFormat0.getIgnoreHeaderCase());
      assertFalse(cSVFormat0.getIgnoreSurroundingSpaces());
      assertEquals(" ", cSVFormat0.getDelimiterString());
      assertFalse(cSVFormat0.getAutoFlush());
      assertFalse(cSVFormat0.getAllowMissingColumnNames());
      assertFalse(boolean0);
      assertFalse(cSVFormat0.getSkipHeaderRecord());
      assertFalse(cSVFormat0.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test174()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.withAllowDuplicateHeaderNames();
      assertFalse(cSVFormat1.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test175()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      boolean boolean0 = cSVFormat0.getTrailingDelimiter();
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test176()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      CSVFormat cSVFormat1 = cSVFormat0.copy();
      assertEquals('\t', cSVFormat1.getDelimiter());
  }

  @Test(timeout = 4000)
  public void test177()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      boolean boolean0 = cSVFormat0.getSkipHeaderRecord();
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test178()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_CSV;
      char char0 = cSVFormat0.getDelimiter();
      assertEquals(',', char0);
  }

  @Test(timeout = 4000)
  public void test179()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      CSVFormat cSVFormat1 = cSVFormat0.MYSQL.withAllowDuplicateHeaderNames(true);
      assertTrue(cSVFormat1.getAllowDuplicateHeaderNames());
  }

  @Test(timeout = 4000)
  public void test180()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setRecordSeparator('e');
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test181()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setAutoFlush(false);
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test182()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      CSVFormat cSVFormat1 = cSVFormat0.withTrailingDelimiter();
      CSVFormat cSVFormat2 = cSVFormat1.withAllowDuplicateHeaderNames(false);
      CSVFormat cSVFormat3 = cSVFormat2.withSkipHeaderRecord();
      assertTrue(cSVFormat3.getTrailingDelimiter());
      assertTrue(cSVFormat3.getSkipHeaderRecord());
      assertFalse(cSVFormat3.getAllowDuplicateHeaderNames());
  }

  @Test(timeout = 4000)
  public void test183()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MYSQL;
      CSVFormat cSVFormat1 = cSVFormat0.withCommentMarker('A');
      Character character0 = new Character('A');
      // Undeclared exception!
      try { 
        cSVFormat1.withQuote(character0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The comment start character and the quoteChar cannot be the same ('A')
         //
         verifyException("org.apache.commons.csv.CSVFormat", e);
      }
  }

  @Test(timeout = 4000)
  public void test184()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setQuote('X');
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test185()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      String[] stringArray0 = new String[4];
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setHeaderComments(stringArray0);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test186()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setIgnoreSurroundingSpaces(false);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test187()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVFormat.Builder cSVFormat_Builder0 = cSVFormat0.builder();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setIgnoreEmptyLines(true);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test188()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setRecordSeparator("Z5p-Y807/F+GWC");
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test189()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setNullString("");
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test190()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setEscape('%');
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test191()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setIgnoreHeaderCase(false);
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }
}
