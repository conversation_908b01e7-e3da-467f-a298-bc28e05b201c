/*
 * This file was automatically generated by EvoSuite
 * Wed Jun 18 08:59:39 GMT 2025
 */

package software.amazon.event.ruler;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.util.LinkedHashSet;
import java.util.Set;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.junit.runner.RunWith;
import software.amazon.event.ruler.AnythingBut;
import software.amazon.event.ruler.AnythingButValuesSet;
import software.amazon.event.ruler.MatchType;
import software.amazon.event.ruler.Patterns;
import software.amazon.event.ruler.ValuePatterns;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true) 
public class Patterns_ESTest extends Patterns_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      LinkedHashSet<String> linkedHashSet0 = new LinkedHashSet<String>();
      linkedHashSet0.add("");
      AnythingButValuesSet anythingButValuesSet0 = Patterns.anythingButSuffix((Set<String>) linkedHashSet0);
      assertNull(anythingButValuesSet0.pattern());
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      MatchType matchType0 = MatchType.ANYTHING_BUT_IGNORE_CASE;
      Patterns patterns0 = new Patterns(matchType0);
      assertEquals(MatchType.ANYTHING_BUT_IGNORE_CASE, patterns0.type());
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      ValuePatterns valuePatterns0 = Patterns.prefixMatch("N");
      String string0 = valuePatterns0.pattern();
      assertEquals("N", string0);
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      ValuePatterns valuePatterns0 = Patterns.equalsIgnoreCaseMatch("");
      String string0 = valuePatterns0.pattern();
      assertEquals("", string0);
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      Patterns patterns0 = Patterns.absencePatterns();
      boolean boolean0 = patterns0.equals(patterns0);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      LinkedHashSet<String> linkedHashSet0 = new LinkedHashSet<String>();
      AnythingButValuesSet anythingButValuesSet0 = Patterns.anythingButWildcard((Set<String>) linkedHashSet0);
      assertNull(anythingButValuesSet0.pattern());
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      AnythingButValuesSet anythingButValuesSet0 = Patterns.anythingButSuffix("");
      assertEquals(MatchType.ANYTHING_BUT_SUFFIX, anythingButValuesSet0.type());
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      LinkedHashSet<String> linkedHashSet0 = new LinkedHashSet<String>();
      AnythingButValuesSet anythingButValuesSet0 = Patterns.anythingButPrefix((Set<String>) linkedHashSet0);
      assertEquals(MatchType.ANYTHING_BUT_PREFIX, anythingButValuesSet0.type());
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      // Undeclared exception!
      try { 
        Patterns.suffixMatch((String) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
      }
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      // Undeclared exception!
      try { 
        Patterns.suffixEqualsIgnoreCaseMatch((String) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
      }
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      // Undeclared exception!
      try { 
        Patterns.anythingButNumbersMatch((Set<String>) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("software.amazon.event.ruler.Patterns", e);
      }
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      // Undeclared exception!
      try { 
        Patterns.anythingButNumberMatch((Set<Double>) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("software.amazon.event.ruler.Patterns", e);
      }
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      // Undeclared exception!
      try { 
        Patterns.anythingButMatch((Set<String>) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("java.util.Collections$UnmodifiableCollection", e);
      }
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      // Undeclared exception!
      try { 
        Patterns.anythingButIgnoreCaseMatch((Set<String>) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("java.util.Collections$UnmodifiableCollection", e);
      }
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      LinkedHashSet<String> linkedHashSet0 = new LinkedHashSet<String>();
      AnythingBut anythingBut0 = Patterns.anythingButNumbersMatch(linkedHashSet0);
      assertNull(anythingBut0.pattern());
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      LinkedHashSet<String> linkedHashSet0 = new LinkedHashSet<String>();
      linkedHashSet0.add((String) null);
      // Undeclared exception!
      try { 
        Patterns.anythingButNumbersMatch(linkedHashSet0);
        fail("Expecting exception: NoClassDefFoundError");
      
      } catch(NoClassDefFoundError e) {
         //
         // ch/randelshofer/fastdoubleparser/JavaBigDecimalParser
         //
         verifyException("software.amazon.event.ruler.ComparableNumber", e);
      }
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      // Undeclared exception!
      try { 
        Patterns.numericEquals("");
        fail("Expecting exception: NoClassDefFoundError");
      
      } catch(NoClassDefFoundError e) {
         //
         // ch/randelshofer/fastdoubleparser/JavaBigDecimalParser
         //
         verifyException("software.amazon.event.ruler.ComparableNumber", e);
      }
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      // Undeclared exception!
      try { 
        Patterns.anythingButNumberMatch("jpDU?$)$b( Q |U");
        fail("Expecting exception: NoClassDefFoundError");
      
      } catch(NoClassDefFoundError e) {
         //
         // ch/randelshofer/fastdoubleparser/JavaBigDecimalParser
         //
         verifyException("software.amazon.event.ruler.ComparableNumber", e);
      }
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      Patterns patterns0 = Patterns.absencePatterns();
      boolean boolean0 = patterns0.equals("N");
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      Patterns patterns0 = Patterns.absencePatterns();
      boolean boolean0 = patterns0.equals((Object) null);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      LinkedHashSet<String> linkedHashSet0 = new LinkedHashSet<String>();
      AnythingButValuesSet anythingButValuesSet0 = Patterns.anythingButIgnoreCaseMatch((Set<String>) linkedHashSet0);
      AnythingButValuesSet anythingButValuesSet1 = Patterns.anythingButIgnoreCaseMatch("N");
      boolean boolean0 = anythingButValuesSet0.equals(anythingButValuesSet1);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      LinkedHashSet<Double> linkedHashSet0 = new LinkedHashSet<Double>();
      Double double0 = new Double((-1676.752));
      linkedHashSet0.add(double0);
      // Undeclared exception!
      try { 
        Patterns.anythingButNumberMatch((Set<Double>) linkedHashSet0);
        fail("Expecting exception: NoClassDefFoundError");
      
      } catch(NoClassDefFoundError e) {
         //
         // ch/randelshofer/fastdoubleparser/JavaBigDecimalParser
         //
         verifyException("software.amazon.event.ruler.ComparableNumber", e);
      }
  }

  @Test(timeout = 4000)
  public void test22()  throws Throwable  {
      AnythingButValuesSet anythingButValuesSet0 = Patterns.anythingButWildcard("N");
      assertNull(anythingButValuesSet0.pattern());
  }

  @Test(timeout = 4000)
  public void test23()  throws Throwable  {
      ValuePatterns valuePatterns0 = Patterns.suffixEqualsIgnoreCaseMatch("N");
      assertEquals(MatchType.SUFFIX_EQUALS_IGNORE_CASE, valuePatterns0.type());
  }

  @Test(timeout = 4000)
  public void test24()  throws Throwable  {
      LinkedHashSet<String> linkedHashSet0 = new LinkedHashSet<String>();
      AnythingBut anythingBut0 = Patterns.anythingButMatch((Set<String>) linkedHashSet0);
      anythingBut0.hashCode();
  }

  @Test(timeout = 4000)
  public void test25()  throws Throwable  {
      LinkedHashSet<Double> linkedHashSet0 = new LinkedHashSet<Double>();
      AnythingBut anythingBut0 = Patterns.anythingButNumberMatch((Set<Double>) linkedHashSet0);
      assertEquals(MatchType.ANYTHING_BUT, anythingBut0.type());
  }

  @Test(timeout = 4000)
  public void test26()  throws Throwable  {
      ValuePatterns valuePatterns0 = Patterns.exactMatch("T:");
      ValuePatterns valuePatterns1 = Patterns.suffixEqualsIgnoreCaseMatch("N");
      valuePatterns0.equals(valuePatterns1);
      assertEquals("T:", valuePatterns0.pattern());
  }

  @Test(timeout = 4000)
  public void test27()  throws Throwable  {
      LinkedHashSet<String> linkedHashSet0 = new LinkedHashSet<String>();
      AnythingButValuesSet anythingButValuesSet0 = Patterns.anythingButIgnoreCaseMatch((Set<String>) linkedHashSet0);
      Object object0 = anythingButValuesSet0.clone();
      assertNotSame(anythingButValuesSet0, object0);
  }

  @Test(timeout = 4000)
  public void test28()  throws Throwable  {
      AnythingBut anythingBut0 = Patterns.anythingButMatch("6jpDU?C$)$b( Q |4U");
      String string0 = anythingBut0.pattern();
      assertNull(string0);
  }

  @Test(timeout = 4000)
  public void test29()  throws Throwable  {
      ValuePatterns valuePatterns0 = Patterns.wildcardMatch((String) null);
      assertEquals(MatchType.WILDCARD, valuePatterns0.type());
  }

  @Test(timeout = 4000)
  public void test30()  throws Throwable  {
      ValuePatterns valuePatterns0 = Patterns.suffixMatch("X>]e=mi:");
      assertEquals(":im=e]>X", valuePatterns0.pattern());
  }

  @Test(timeout = 4000)
  public void test31()  throws Throwable  {
      // Undeclared exception!
      try { 
        Patterns.anythingButPrefix((Set<String>) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("java.util.Collections$UnmodifiableCollection", e);
      }
  }

  @Test(timeout = 4000)
  public void test32()  throws Throwable  {
      // Undeclared exception!
      try { 
        Patterns.anythingButMatch((-1.0));
        fail("Expecting exception: NoClassDefFoundError");
      
      } catch(NoClassDefFoundError e) {
         //
         // ch/randelshofer/fastdoubleparser/JavaBigDecimalParser
         //
         verifyException("software.amazon.event.ruler.ComparableNumber", e);
      }
  }

  @Test(timeout = 4000)
  public void test33()  throws Throwable  {
      // Undeclared exception!
      try { 
        Patterns.numericEquals(0.0);
        fail("Expecting exception: NoClassDefFoundError");
      
      } catch(NoClassDefFoundError e) {
         //
         // ch/randelshofer/fastdoubleparser/JavaBigDecimalParser
         //
         verifyException("software.amazon.event.ruler.ComparableNumber", e);
      }
  }

  @Test(timeout = 4000)
  public void test34()  throws Throwable  {
      // Undeclared exception!
      try { 
        Patterns.anythingButSuffix((String) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
      }
  }

  @Test(timeout = 4000)
  public void test35()  throws Throwable  {
      ValuePatterns valuePatterns0 = Patterns.prefixEqualsIgnoreCaseMatch("N");
      assertEquals("N", valuePatterns0.pattern());
  }

  @Test(timeout = 4000)
  public void test36()  throws Throwable  {
      // Undeclared exception!
      try { 
        Patterns.anythingButSuffix((Set<String>) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("software.amazon.event.ruler.Patterns", e);
      }
  }

  @Test(timeout = 4000)
  public void test37()  throws Throwable  {
      AnythingButValuesSet anythingButValuesSet0 = Patterns.anythingButPrefix("N");
      assertNull(anythingButValuesSet0.pattern());
  }

  @Test(timeout = 4000)
  public void test38()  throws Throwable  {
      Patterns patterns0 = Patterns.existencePatterns();
      String string0 = patterns0.toString();
      assertEquals("T:EXISTS", string0);
  }

  @Test(timeout = 4000)
  public void test39()  throws Throwable  {
      Patterns patterns0 = Patterns.absencePatterns();
      MatchType matchType0 = patterns0.type();
      assertEquals(MatchType.ABSENT, matchType0);
  }

  @Test(timeout = 4000)
  public void test40()  throws Throwable  {
      // Undeclared exception!
      try { 
        Patterns.anythingButWildcard((Set<String>) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("java.util.Collections$UnmodifiableCollection", e);
      }
  }
}
