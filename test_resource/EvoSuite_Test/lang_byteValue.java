/*
 * This file was automatically generated by EvoSuite
 * Wed Jun 18 09:09:17 GMT 2025
 */

package org.apache.commons.lang3.mutable;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import org.apache.commons.lang3.mutable.MutableByte;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true) 
public class MutableByte_ESTest extends MutableByte_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      MutableByte mutableByte0 = new MutableByte((byte)27);
      MutableByte mutableByte1 = new MutableByte();
      boolean boolean0 = mutableByte1.equals(mutableByte0);
      assertFalse(boolean0);
      assertFalse(mutableByte0.equals((Object)mutableByte1));
      assertEquals((byte)27, mutableByte0.byteValue());
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      MutableByte mutableByte0 = new MutableByte();
      Long long0 = new Long(564L);
      mutableByte0.subtract((Number) long0);
      assertEquals((-52.0F), mutableByte0.floatValue(), 0.01F);
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      Float float0 = new Float(0.0);
      MutableByte mutableByte0 = new MutableByte(float0);
      mutableByte0.setValue((byte)1);
      Byte byte0 = mutableByte0.toByte();
      assertEquals((byte)1, (byte)byte0);
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      MutableByte mutableByte0 = new MutableByte();
      mutableByte0.getAndAdd((byte) (-38));
      mutableByte0.toByte();
      assertEquals((short) (-38), mutableByte0.shortValue());
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      MutableByte mutableByte0 = new MutableByte((byte)2);
      long long0 = mutableByte0.longValue();
      assertEquals(2L, long0);
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      MutableByte mutableByte0 = new MutableByte((byte)0);
      mutableByte0.getAndDecrement();
      long long0 = mutableByte0.longValue();
      assertEquals((byte) (-1), (byte)mutableByte0.toByte());
      assertEquals((-1L), long0);
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      MutableByte mutableByte0 = new MutableByte((byte)0);
      int int0 = mutableByte0.intValue();
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      MutableByte mutableByte0 = new MutableByte((byte)0);
      mutableByte0.getAndDecrement();
      int int0 = mutableByte0.intValue();
      assertEquals((byte) (-1), (byte)mutableByte0.getValue());
      assertEquals((-1), int0);
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      MutableByte mutableByte0 = new MutableByte((byte)6);
      mutableByte0.decrementAndGet();
      mutableByte0.decrementAndGet();
      mutableByte0.subtract((byte)5);
      byte byte0 = mutableByte0.incrementAndGet();
      assertEquals(0.0F, mutableByte0.floatValue(), 0.01F);
      assertEquals((byte)0, byte0);
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      Short short0 = new Short((short) (-372));
      MutableByte mutableByte0 = new MutableByte(short0);
      byte byte0 = mutableByte0.incrementAndGet();
      assertEquals((byte) (-115), byte0);
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      MutableByte mutableByte0 = new MutableByte((byte)1);
      Byte byte0 = mutableByte0.getValue();
      assertEquals((byte)1, (byte)byte0);
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      MutableByte mutableByte0 = new MutableByte();
      mutableByte0.decrement();
      mutableByte0.getValue();
      assertEquals((-1.0), mutableByte0.doubleValue(), 0.01);
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      Integer integer0 = new Integer((-957));
      MutableByte mutableByte0 = new MutableByte(integer0);
      byte byte0 = mutableByte0.getAndIncrement();
      assertEquals((short)68, mutableByte0.shortValue());
      assertEquals((byte)67, byte0);
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      MutableByte mutableByte0 = new MutableByte();
      mutableByte0.getAndAdd((byte) (-38));
      byte byte0 = mutableByte0.getAndIncrement();
      assertEquals("-37", mutableByte0.toString());
      assertEquals((byte) (-38), byte0);
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      MutableByte mutableByte0 = new MutableByte();
      MutableByte mutableByte1 = new MutableByte((byte)2);
      mutableByte0.add((Number) mutableByte1);
      byte byte0 = mutableByte0.getAndDecrement();
      assertEquals(1, mutableByte0.intValue());
      assertEquals((byte)2, byte0);
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      MutableByte mutableByte0 = new MutableByte();
      mutableByte0.getAndAdd((byte) (-38));
      byte byte0 = mutableByte0.getAndDecrement();
      assertEquals((byte) (-39), (byte)mutableByte0.toByte());
      assertEquals((byte) (-38), byte0);
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      MutableByte mutableByte0 = new MutableByte((byte)0);
      byte byte0 = mutableByte0.getAndAdd((Number) mutableByte0);
      assertEquals((byte)0, byte0);
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      Float float0 = new Float((-1135.41F));
      MutableByte mutableByte0 = new MutableByte(float0);
      mutableByte0.getAndAdd((byte) (-110));
      float float1 = mutableByte0.floatValue();
      assertEquals((byte)35, (byte)mutableByte0.toByte());
      assertEquals(35.0F, float1, 0.01F);
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      MutableByte mutableByte0 = new MutableByte();
      mutableByte0.getAndAdd((byte) (-38));
      float float0 = mutableByte0.floatValue();
      assertEquals((byte) (-38), (byte)mutableByte0.getValue());
      assertEquals((-38.0F), float0, 0.01F);
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      Short short0 = new Short((short)0);
      MutableByte mutableByte0 = new MutableByte(short0);
      mutableByte0.increment();
      double double0 = mutableByte0.doubleValue();
      assertEquals(1L, mutableByte0.longValue());
      assertEquals(1.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      MutableByte mutableByte0 = new MutableByte((byte)1);
      byte byte0 = mutableByte0.decrementAndGet();
      assertEquals(0L, mutableByte0.longValue());
      assertEquals((byte)0, byte0);
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      MutableByte mutableByte0 = new MutableByte();
      MutableByte mutableByte1 = new MutableByte((byte)2);
      mutableByte0.setValue((Number) mutableByte1);
      byte byte0 = mutableByte0.decrementAndGet();
      assertEquals((byte)1, byte0);
  }

  @Test(timeout = 4000)
  public void test22()  throws Throwable  {
      MutableByte mutableByte0 = new MutableByte();
      MutableByte mutableByte1 = new MutableByte((byte)2);
      int int0 = mutableByte1.compareTo(mutableByte0);
      assertEquals(2, int0);
  }

  @Test(timeout = 4000)
  public void test23()  throws Throwable  {
      MutableByte mutableByte0 = new MutableByte();
      MutableByte mutableByte1 = new MutableByte((byte)2);
      int int0 = mutableByte0.compareTo(mutableByte1);
      assertEquals((-2), int0);
  }

  @Test(timeout = 4000)
  public void test24()  throws Throwable  {
      MutableByte mutableByte0 = new MutableByte((byte)2);
      byte byte0 = mutableByte0.byteValue();
      assertEquals((byte)2, byte0);
  }

  @Test(timeout = 4000)
  public void test25()  throws Throwable  {
      MutableByte mutableByte0 = new MutableByte((byte)0);
      mutableByte0.getAndDecrement();
      byte byte0 = mutableByte0.byteValue();
      assertEquals((-1.0), mutableByte0.doubleValue(), 0.01);
      assertEquals((byte) (-1), byte0);
  }

  @Test(timeout = 4000)
  public void test26()  throws Throwable  {
      MutableByte mutableByte0 = new MutableByte((byte)1);
      Short short0 = new Short((short)62);
      byte byte0 = mutableByte0.addAndGet((Number) short0);
      assertEquals((byte)63, byte0);
  }

  @Test(timeout = 4000)
  public void test27()  throws Throwable  {
      MutableByte mutableByte0 = new MutableByte((byte)0);
      mutableByte0.getAndDecrement();
      byte byte0 = mutableByte0.addAndGet((Number) mutableByte0);
      assertEquals((-2), mutableByte0.intValue());
      assertEquals((byte) (-2), byte0);
  }

  @Test(timeout = 4000)
  public void test28()  throws Throwable  {
      MutableByte mutableByte0 = new MutableByte();
      byte byte0 = mutableByte0.addAndGet((byte)0);
      assertEquals((byte)0, byte0);
  }

  @Test(timeout = 4000)
  public void test29()  throws Throwable  {
      MutableByte mutableByte0 = new MutableByte();
      mutableByte0.subtract((byte) (-59));
      byte byte0 = mutableByte0.addAndGet((byte)101);
      assertEquals((short) (-96), mutableByte0.shortValue());
      assertEquals((byte) (-96), byte0);
  }

  @Test(timeout = 4000)
  public void test30()  throws Throwable  {
      MutableByte mutableByte0 = new MutableByte();
      // Undeclared exception!
      try { 
        mutableByte0.setValue((Number) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.mutable.MutableByte", e);
      }
  }

  @Test(timeout = 4000)
  public void test31()  throws Throwable  {
      MutableByte mutableByte0 = new MutableByte();
      // Undeclared exception!
      try { 
        mutableByte0.getAndAdd((Number) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.mutable.MutableByte", e);
      }
  }

  @Test(timeout = 4000)
  public void test32()  throws Throwable  {
      Short short0 = new Short((short)0);
      MutableByte mutableByte0 = new MutableByte(short0);
      // Undeclared exception!
      try { 
        mutableByte0.compareTo((MutableByte) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.mutable.MutableByte", e);
      }
  }

  @Test(timeout = 4000)
  public void test33()  throws Throwable  {
      MutableByte mutableByte0 = new MutableByte();
      // Undeclared exception!
      try { 
        mutableByte0.addAndGet((Number) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.mutable.MutableByte", e);
      }
  }

  @Test(timeout = 4000)
  public void test34()  throws Throwable  {
      MutableByte mutableByte0 = null;
      try {
        mutableByte0 = new MutableByte("org.apache.commons.lang3.mutable.MutableByte");
        fail("Expecting exception: NumberFormatException");
      
      } catch(NumberFormatException e) {
         //
         // For input string: \"org.apache.commons.lang3.mutable.MutableByte\"
         //
         verifyException("java.lang.NumberFormatException", e);
      }
  }

  @Test(timeout = 4000)
  public void test35()  throws Throwable  {
      MutableByte mutableByte0 = null;
      try {
        mutableByte0 = new MutableByte((Number) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.mutable.MutableByte", e);
      }
  }

  @Test(timeout = 4000)
  public void test36()  throws Throwable  {
      MutableByte mutableByte0 = new MutableByte();
      byte byte0 = mutableByte0.byteValue();
      assertEquals((byte)0, byte0);
  }

  @Test(timeout = 4000)
  public void test37()  throws Throwable  {
      MutableByte mutableByte0 = new MutableByte((byte)1);
      MutableByte mutableByte1 = new MutableByte(mutableByte0);
      mutableByte1.add((Number) mutableByte0);
      boolean boolean0 = mutableByte1.equals(mutableByte0);
      assertEquals((byte)2, mutableByte1.byteValue());
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test38()  throws Throwable  {
      MutableByte mutableByte0 = new MutableByte();
      boolean boolean0 = mutableByte0.equals(mutableByte0);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test39()  throws Throwable  {
      MutableByte mutableByte0 = new MutableByte();
      boolean boolean0 = mutableByte0.equals((Object) null);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test40()  throws Throwable  {
      MutableByte mutableByte0 = new MutableByte((byte)1);
      mutableByte0.add((byte)3);
      assertEquals(4.0, mutableByte0.doubleValue(), 0.01);
  }

  @Test(timeout = 4000)
  public void test41()  throws Throwable  {
      MutableByte mutableByte0 = new MutableByte((byte)0);
      float float0 = mutableByte0.floatValue();
      assertEquals(0.0F, float0, 0.01F);
  }

  @Test(timeout = 4000)
  public void test42()  throws Throwable  {
      MutableByte mutableByte0 = new MutableByte();
      mutableByte0.hashCode();
  }

  @Test(timeout = 4000)
  public void test43()  throws Throwable  {
      MutableByte mutableByte0 = new MutableByte();
      byte byte0 = mutableByte0.incrementAndGet();
      assertEquals(1, mutableByte0.intValue());
      assertEquals((byte)1, byte0);
  }

  @Test(timeout = 4000)
  public void test44()  throws Throwable  {
      MutableByte mutableByte0 = new MutableByte();
      mutableByte0.getAndDecrement();
      Long long0 = new Long(1L);
      byte byte0 = mutableByte0.getAndAdd((Number) long0);
      assertEquals((byte)0, (byte)mutableByte0.toByte());
      assertEquals((byte) (-1), byte0);
  }

  @Test(timeout = 4000)
  public void test45()  throws Throwable  {
      MutableByte mutableByte0 = new MutableByte();
      byte byte0 = mutableByte0.decrementAndGet();
      assertEquals((byte) (-1), (byte)mutableByte0.toByte());
      assertEquals((byte) (-1), byte0);
  }

  @Test(timeout = 4000)
  public void test46()  throws Throwable  {
      MutableByte mutableByte0 = new MutableByte((byte)1);
      int int0 = mutableByte0.intValue();
      assertEquals(1, int0);
  }

  @Test(timeout = 4000)
  public void test47()  throws Throwable  {
      MutableByte mutableByte0 = new MutableByte((byte)1);
      byte byte0 = mutableByte0.getAndAdd((byte)0);
      assertEquals((byte)1, byte0);
      assertEquals(1.0F, mutableByte0.floatValue(), 0.01F);
  }

  @Test(timeout = 4000)
  public void test48()  throws Throwable  {
      MutableByte mutableByte0 = new MutableByte();
      byte byte0 = mutableByte0.getAndIncrement();
      assertEquals("1", mutableByte0.toString());
      assertEquals((byte)0, byte0);
  }

  @Test(timeout = 4000)
  public void test49()  throws Throwable  {
      MutableByte mutableByte0 = new MutableByte();
      String string0 = mutableByte0.toString();
      assertEquals("0", string0);
  }

  @Test(timeout = 4000)
  public void test50()  throws Throwable  {
      MutableByte mutableByte0 = new MutableByte();
      double double0 = mutableByte0.doubleValue();
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test51()  throws Throwable  {
      MutableByte mutableByte0 = new MutableByte("100");
      assertEquals(100.0F, mutableByte0.floatValue(), 0.01F);
  }

  @Test(timeout = 4000)
  public void test52()  throws Throwable  {
      MutableByte mutableByte0 = new MutableByte();
      // Undeclared exception!
      try { 
        mutableByte0.subtract((Number) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.mutable.MutableByte", e);
      }
  }

  @Test(timeout = 4000)
  public void test53()  throws Throwable  {
      MutableByte mutableByte0 = new MutableByte();
      byte byte0 = mutableByte0.addAndGet((byte)2);
      assertEquals((byte)2, mutableByte0.byteValue());
      assertEquals((byte)2, byte0);
  }

  @Test(timeout = 4000)
  public void test54()  throws Throwable  {
      MutableByte mutableByte0 = new MutableByte((byte)1);
      byte byte0 = mutableByte0.getAndAdd((Number) mutableByte0);
      assertEquals((byte)2, (byte)mutableByte0.getValue());
      assertEquals((byte)1, byte0);
  }

  @Test(timeout = 4000)
  public void test55()  throws Throwable  {
      MutableByte mutableByte0 = new MutableByte();
      int int0 = mutableByte0.compareTo(mutableByte0);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test56()  throws Throwable  {
      MutableByte mutableByte0 = new MutableByte();
      // Undeclared exception!
      try { 
        mutableByte0.add((Number) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.mutable.MutableByte", e);
      }
  }

  @Test(timeout = 4000)
  public void test57()  throws Throwable  {
      MutableByte mutableByte0 = new MutableByte();
      Byte byte0 = mutableByte0.getValue();
      assertEquals((byte)0, (byte)byte0);
  }

  @Test(timeout = 4000)
  public void test58()  throws Throwable  {
      MutableByte mutableByte0 = new MutableByte();
      long long0 = mutableByte0.longValue();
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test59()  throws Throwable  {
      MutableByte mutableByte0 = new MutableByte();
      mutableByte0.decrement();
      double double0 = mutableByte0.doubleValue();
      assertEquals((byte) (-1), (byte)mutableByte0.getValue());
      assertEquals((-1.0), double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test60()  throws Throwable  {
      MutableByte mutableByte0 = new MutableByte();
      byte byte0 = mutableByte0.addAndGet((Number) 0.0);
      assertEquals((byte)0, byte0);
  }

  @Test(timeout = 4000)
  public void test61()  throws Throwable  {
      MutableByte mutableByte0 = new MutableByte();
      Byte byte0 = mutableByte0.toByte();
      assertEquals((byte)0, (byte)byte0);
  }
}
