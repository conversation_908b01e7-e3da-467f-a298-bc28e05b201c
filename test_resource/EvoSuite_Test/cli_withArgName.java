/*
 * This file was automatically generated by EvoSuite
 * Wed Jun 18 07:58:36 GMT 2025
 */

package org.apache.commons.cli;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import org.apache.commons.cli.Option;
import org.apache.commons.cli.OptionBuilder;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true) 
public class OptionBuilder_ESTest extends OptionBuilder_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      Class<Object> class0 = Object.class;
      OptionBuilder optionBuilder0 = OptionBuilder.withType((Object) class0);
      assertNotNull(optionBuilder0);
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      OptionBuilder.isRequired();
      Option option0 = OptionBuilder.create((String) null);
      assertEquals((-1), option0.getArgs());
      assertTrue(option0.isRequired());
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      OptionBuilder.withValueSeparator();
      Option option0 = OptionBuilder.create((String) null);
      assertEquals('=', option0.getValueSeparator());
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      OptionBuilder.withLongOpt(">)|N");
      Option option0 = OptionBuilder.create("");
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      OptionBuilder.withArgName("e2sx|*");
      Option option0 = OptionBuilder.create("");
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      Option option0 = OptionBuilder.create("K");
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      OptionBuilder.hasOptionalArgs(0);
      Option option0 = OptionBuilder.create("");
      assertTrue(option0.hasOptionalArg());
      assertEquals(0, option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      OptionBuilder.withLongOpt("U`)OD@!)5~T>I");
      Option option0 = OptionBuilder.create('2');
      assertEquals((-1), option0.getArgs());
      assertEquals("2", option0.getOpt());
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      OptionBuilder.hasArgs();
      Option option0 = OptionBuilder.create('4');
      assertEquals((-2), option0.getArgs());
      assertEquals(52, option0.getId());
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      OptionBuilder.withArgName("e2sx|*");
      Option option0 = OptionBuilder.create('1');
      assertEquals((-1), option0.getArgs());
      assertEquals(49, option0.getId());
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      OptionBuilder.hasOptionalArgs(0);
      Option option0 = OptionBuilder.create('a');
      assertEquals(97, option0.getId());
      assertTrue(option0.hasOptionalArg());
      assertEquals(0, option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      OptionBuilder.hasArg();
      Option option0 = OptionBuilder.create('W');
      assertEquals(87, option0.getId());
      assertEquals(1, option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      OptionBuilder.withLongOpt("");
      OptionBuilder.isRequired();
      Option option0 = OptionBuilder.create();
      assertTrue(option0.isRequired());
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      OptionBuilder.withValueSeparator('\'');
      OptionBuilder.withLongOpt("}(9C");
      Option option0 = OptionBuilder.create();
      assertEquals('\'', option0.getValueSeparator());
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      OptionBuilder.withLongOpt("}(9C");
      OptionBuilder.withArgName("}(9C");
      Option option0 = OptionBuilder.create();
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      OptionBuilder.withLongOpt("");
      OptionBuilder.hasOptionalArgs(0);
      Option option0 = OptionBuilder.create();
      assertEquals(0, option0.getArgs());
      assertTrue(option0.hasOptionalArg());
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      OptionBuilder.hasArg();
      OptionBuilder.withLongOpt("");
      Option option0 = OptionBuilder.create();
      assertEquals(1, option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      try { 
        OptionBuilder.create("Qn;&MLRx=SvC}^?P");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The option 'Qn;&MLRx=SvC}^?P' contains an illegal character : ';'
         //
         verifyException("org.apache.commons.cli.OptionValidator", e);
      }
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      try { 
        OptionBuilder.create(']');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Illegal option name ']'
         //
         verifyException("org.apache.commons.cli.OptionValidator", e);
      }
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      OptionBuilder.hasArg(true);
      Option option0 = OptionBuilder.create("");
      assertEquals(1, option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      OptionBuilder optionBuilder0 = OptionBuilder.hasArg(false);
      assertNotNull(optionBuilder0);
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      try { 
        OptionBuilder.create();
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // must specify longopt
         //
         verifyException("org.apache.commons.cli.OptionBuilder", e);
      }
  }

  @Test(timeout = 4000)
  public void test22()  throws Throwable  {
      OptionBuilder optionBuilder0 = OptionBuilder.withDescription("}(9C");
      assertNotNull(optionBuilder0);
  }

  @Test(timeout = 4000)
  public void test23()  throws Throwable  {
      OptionBuilder.hasOptionalArgs();
      Option option0 = OptionBuilder.create("");
      assertEquals((-2), option0.getArgs());
      assertTrue(option0.hasOptionalArg());
  }

  @Test(timeout = 4000)
  public void test24()  throws Throwable  {
      OptionBuilder.hasArgs();
      OptionBuilder.withLongOpt("");
      Option option0 = OptionBuilder.create();
      assertEquals((-2), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test25()  throws Throwable  {
      OptionBuilder.withValueSeparator();
      Option option0 = OptionBuilder.create('W');
      assertEquals('=', option0.getValueSeparator());
      assertEquals((-1), option0.getArgs());
      assertEquals(87, option0.getId());
  }

  @Test(timeout = 4000)
  public void test26()  throws Throwable  {
      Class<Object> class0 = Object.class;
      OptionBuilder optionBuilder0 = OptionBuilder.withType(class0);
      assertNotNull(optionBuilder0);
  }

  @Test(timeout = 4000)
  public void test27()  throws Throwable  {
      OptionBuilder optionBuilder0 = OptionBuilder.hasOptionalArg();
      assertNotNull(optionBuilder0);
  }

  @Test(timeout = 4000)
  public void test28()  throws Throwable  {
      OptionBuilder optionBuilder0 = OptionBuilder.isRequired(true);
      assertNotNull(optionBuilder0);
  }

  @Test(timeout = 4000)
  public void test29()  throws Throwable  {
      OptionBuilder.isRequired();
      Option option0 = OptionBuilder.create('W');
      assertEquals((-1), option0.getArgs());
      assertTrue(option0.isRequired());
      assertEquals("W", option0.getOpt());
  }

  @Test(timeout = 4000)
  public void test30()  throws Throwable  {
      OptionBuilder optionBuilder0 = OptionBuilder.hasArg();
      // Undeclared exception!
      try { 
        OptionBuilder.withType((Object) optionBuilder0);
        fail("Expecting exception: ClassCastException");
      
      } catch(ClassCastException e) {
         //
         // org.apache.commons.cli.OptionBuilder cannot be cast to java.lang.Class
         //
         verifyException("org.apache.commons.cli.OptionBuilder", e);
      }
  }

  @Test(timeout = 4000)
  public void test31()  throws Throwable  {
      OptionBuilder optionBuilder0 = OptionBuilder.hasArgs(704);
      assertNotNull(optionBuilder0);
  }
}
