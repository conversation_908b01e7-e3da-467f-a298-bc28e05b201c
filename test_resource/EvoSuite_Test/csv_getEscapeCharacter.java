/*
 * This file was automatically generated by EvoSuite
 * Wed Jun 18 08:10:12 GMT 2025
 */

package org.apache.commons.csv;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.shaded.org.mockito.Mockito.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.io.BufferedWriter;
import java.io.CharArrayWriter;
import java.io.File;
import java.io.IOException;
import java.io.PipedReader;
import java.io.PipedWriter;
import java.io.Reader;
import java.io.StringWriter;
import java.net.URI;
import java.nio.charset.Charset;
import java.nio.file.Path;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import javax.sql.rowset.RowSetMetaDataImpl;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVPrinter;
import org.apache.commons.csv.DuplicateHeaderMode;
import org.apache.commons.csv.QuoteMode;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.evosuite.runtime.ViolatedAssumptionAnswer;
import org.evosuite.runtime.mock.java.io.MockFile;
import org.evosuite.runtime.mock.java.io.MockFileWriter;
import org.evosuite.runtime.mock.java.io.MockPrintStream;
import org.evosuite.runtime.mock.java.net.MockURI;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true) 
public class CSVFormat_ESTest extends CSVFormat_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test000()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreSurroundingSpaces(true);
      CSVFormat cSVFormat2 = cSVFormat1.withTrim(false);
      assertTrue(cSVFormat2.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat2.getTrim());
  }

  @Test(timeout = 4000)
  public void test001()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.withAutoFlush(true);
      CSVFormat cSVFormat2 = cSVFormat1.withTrim(false);
      assertTrue(cSVFormat2.getAutoFlush());
      assertFalse(cSVFormat2.getTrim());
  }

  @Test(timeout = 4000)
  public void test002()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat cSVFormat1 = cSVFormat0.withTrim();
      assertTrue(cSVFormat1.getTrim());
  }

  @Test(timeout = 4000)
  public void test003()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      CSVFormat cSVFormat1 = cSVFormat0.withSkipHeaderRecord(true);
      CSVFormat cSVFormat2 = cSVFormat1.withTrim();
      assertTrue(cSVFormat2.getSkipHeaderRecord());
      assertTrue(cSVFormat2.getTrim());
  }

  @Test(timeout = 4000)
  public void test004()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('u');
      CSVFormat cSVFormat1 = cSVFormat0.INFORMIX_UNLOAD_CSV.withTrailingDelimiter(false);
      assertFalse(cSVFormat0.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat0.getIgnoreEmptyLines());
      assertFalse(cSVFormat0.getTrim());
      assertFalse(cSVFormat0.getAutoFlush());
      assertFalse(cSVFormat0.getIgnoreHeaderCase());
      assertFalse(cSVFormat1.getTrailingDelimiter());
      assertEquals("u", cSVFormat0.getDelimiterString());
      assertFalse(cSVFormat0.getSkipHeaderRecord());
      assertFalse(cSVFormat0.getAllowMissingColumnNames());
      assertFalse(cSVFormat0.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test005()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat cSVFormat1 = cSVFormat0.POSTGRESQL_TEXT.withTrailingDelimiter(true);
      assertTrue(cSVFormat1.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test006()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withSkipHeaderRecord();
      CSVFormat cSVFormat2 = cSVFormat1.withTrailingDelimiter();
      assertTrue(cSVFormat2.getTrailingDelimiter());
      assertTrue(cSVFormat2.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test007()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      RowSetMetaDataImpl rowSetMetaDataImpl0 = new RowSetMetaDataImpl();
      CSVFormat cSVFormat1 = cSVFormat0.MONGODB_TSV.withHeader((ResultSetMetaData) rowSetMetaDataImpl0);
      CSVFormat cSVFormat2 = cSVFormat1.withTrailingDelimiter();
      assertTrue(cSVFormat2.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test008()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.POSTGRESQL_CSV.withSystemRecordSeparator();
      assertFalse(cSVFormat1.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test009()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setIgnoreHeaderCase(true);
      CSVFormat cSVFormat1 = cSVFormat_Builder1.build();
      CSVFormat cSVFormat2 = cSVFormat1.withSkipHeaderRecord(true);
      assertTrue(cSVFormat2.getIgnoreHeaderCase());
      assertTrue(cSVFormat2.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test010()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.TDF.withSkipHeaderRecord();
      assertTrue(cSVFormat1.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test011()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MYSQL;
      CSVFormat cSVFormat1 = cSVFormat0.withRecordSeparator('l');
      assertEquals("l", cSVFormat1.getRecordSeparator());
  }

  @Test(timeout = 4000)
  public void test012()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      Character character0 = new Character('h');
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setEscape(character0);
      CSVFormat cSVFormat0 = cSVFormat_Builder1.build();
      CSVFormat cSVFormat1 = cSVFormat0.withRecordSeparator('h');
      assertEquals("h", cSVFormat1.getRecordSeparator());
  }

  @Test(timeout = 4000)
  public void test013()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      CSVFormat cSVFormat1 = cSVFormat0.withTrim();
      QuoteMode quoteMode0 = QuoteMode.ALL_NON_NULL;
      CSVFormat cSVFormat2 = cSVFormat1.withQuoteMode(quoteMode0);
      assertTrue(cSVFormat2.getTrim());
  }

  @Test(timeout = 4000)
  public void test014()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      QuoteMode quoteMode0 = QuoteMode.MINIMAL;
      CSVFormat cSVFormat1 = cSVFormat0.POSTGRESQL_CSV.withQuoteMode(quoteMode0);
      assertEquals("\n", cSVFormat1.getRecordSeparator());
  }

  @Test(timeout = 4000)
  public void test015()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      Character character0 = Character.valueOf(']');
      CSVFormat cSVFormat1 = cSVFormat0.POSTGRESQL_CSV.withQuote(character0);
      assertNull(cSVFormat1.getEscapeCharacter());
  }

  @Test(timeout = 4000)
  public void test016()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.withQuote('E');
      assertEquals('E', (char)cSVFormat1.getQuoteCharacter());
  }

  @Test(timeout = 4000)
  public void test017()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      CSVFormat cSVFormat1 = cSVFormat0.withSkipHeaderRecord(true);
      CSVFormat cSVFormat2 = cSVFormat1.withNullString("");
      assertTrue(cSVFormat2.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test018()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      CSVFormat cSVFormat1 = cSVFormat0.withNullString("TDF");
      assertEquals(",", cSVFormat1.getDelimiterString());
  }

  @Test(timeout = 4000)
  public void test019()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVFormat cSVFormat1 = cSVFormat0.POSTGRESQL_TEXT.withIgnoreSurroundingSpaces(false);
      assertFalse(cSVFormat1.getIgnoreSurroundingSpaces());
  }

  @Test(timeout = 4000)
  public void test020()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MYSQL;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreHeaderCase();
      CSVFormat cSVFormat2 = cSVFormat1.withIgnoreSurroundingSpaces();
      assertTrue(cSVFormat2.getIgnoreSurroundingSpaces());
      assertTrue(cSVFormat2.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test021()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.EXCEL.withIgnoreSurroundingSpaces();
      assertTrue(cSVFormat1.getIgnoreSurroundingSpaces());
  }

  @Test(timeout = 4000)
  public void test022()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      CSVFormat cSVFormat1 = cSVFormat0.withTrim();
      CSVFormat cSVFormat2 = cSVFormat1.withIgnoreHeaderCase(false);
      assertFalse(cSVFormat2.getIgnoreHeaderCase());
      assertTrue(cSVFormat2.getTrim());
  }

  @Test(timeout = 4000)
  public void test023()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      CSVFormat cSVFormat1 = cSVFormat0.withTrailingDelimiter();
      CSVFormat cSVFormat2 = cSVFormat1.withIgnoreHeaderCase(true);
      assertTrue(cSVFormat2.getIgnoreHeaderCase());
      assertTrue(cSVFormat2.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test024()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.POSTGRESQL_TEXT.withIgnoreHeaderCase(true);
      assertTrue(cSVFormat1.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test025()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVFormat cSVFormat1 = cSVFormat0.withSkipHeaderRecord(true);
      CSVFormat cSVFormat2 = cSVFormat1.withIgnoreHeaderCase();
      assertTrue(cSVFormat2.getIgnoreHeaderCase());
      assertTrue(cSVFormat2.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test026()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      CSVFormat cSVFormat1 = cSVFormat0.TDF.withIgnoreHeaderCase();
      assertTrue(cSVFormat1.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test027()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreEmptyLines(false);
      assertFalse(cSVFormat1.getIgnoreEmptyLines());
  }

  @Test(timeout = 4000)
  public void test028()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.MONGODB_CSV.withIgnoreEmptyLines();
      assertTrue(cSVFormat1.getIgnoreEmptyLines());
  }

  @Test(timeout = 4000)
  public void test029()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreEmptyLines();
      assertTrue(cSVFormat1.getIgnoreEmptyLines());
  }

  @Test(timeout = 4000)
  public void test030()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      CSVFormat cSVFormat1 = cSVFormat0.POSTGRESQL_TEXT.withHeader((String[]) null);
      assertFalse(cSVFormat1.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test031()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      CSVFormat cSVFormat1 = cSVFormat0.withSkipHeaderRecord();
      CSVFormat cSVFormat2 = cSVFormat1.withIgnoreHeaderCase(false);
      String[] stringArray0 = new String[1];
      CSVFormat cSVFormat3 = cSVFormat2.withHeader(stringArray0);
      assertTrue(cSVFormat3.getSkipHeaderRecord());
      assertFalse(cSVFormat3.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test032()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreSurroundingSpaces();
      String[] stringArray0 = new String[9];
      CSVFormat cSVFormat2 = cSVFormat1.withHeader(stringArray0);
      assertTrue(cSVFormat2.getIgnoreSurroundingSpaces());
  }

  @Test(timeout = 4000)
  public void test033()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      String[] stringArray0 = new String[9];
      CSVFormat cSVFormat1 = cSVFormat0.withHeader(stringArray0);
      assertFalse(cSVFormat1.isCommentMarkerSet());
  }

  @Test(timeout = 4000)
  public void test034()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVFormat cSVFormat1 = cSVFormat0.withSkipHeaderRecord(true);
      RowSetMetaDataImpl rowSetMetaDataImpl0 = new RowSetMetaDataImpl();
      CSVFormat cSVFormat2 = cSVFormat1.withHeader((ResultSetMetaData) rowSetMetaDataImpl0);
      assertTrue(cSVFormat2.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test035()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      cSVFormat_Builder0.setIgnoreHeaderCase(true);
      CSVFormat cSVFormat1 = cSVFormat_Builder0.build();
      RowSetMetaDataImpl rowSetMetaDataImpl0 = new RowSetMetaDataImpl();
      CSVFormat cSVFormat2 = cSVFormat1.withHeader((ResultSetMetaData) rowSetMetaDataImpl0);
      assertTrue(cSVFormat2.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test036()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('u');
      RowSetMetaDataImpl rowSetMetaDataImpl0 = new RowSetMetaDataImpl();
      cSVFormat0.EXCEL.withHeader((ResultSetMetaData) rowSetMetaDataImpl0);
      assertFalse(cSVFormat0.getTrim());
      assertFalse(cSVFormat0.getIgnoreHeaderCase());
      assertFalse(cSVFormat0.getIgnoreEmptyLines());
      assertFalse(cSVFormat0.getAllowMissingColumnNames());
      assertFalse(cSVFormat0.getAutoFlush());
      assertFalse(cSVFormat0.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat0.getTrailingDelimiter());
      assertEquals("u", cSVFormat0.getDelimiterString());
      assertFalse(cSVFormat0.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test037()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      ResultSet resultSet0 = mock(ResultSet.class, new ViolatedAssumptionAnswer());
      doReturn((ResultSetMetaData) null).when(resultSet0).getMetaData();
      CSVFormat cSVFormat1 = cSVFormat0.MYSQL.withHeader(resultSet0);
      assertNull(cSVFormat1.getCommentMarker());
  }

  @Test(timeout = 4000)
  public void test038()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('u');
      ResultSet resultSet0 = mock(ResultSet.class, new ViolatedAssumptionAnswer());
      doReturn((ResultSetMetaData) null).when(resultSet0).getMetaData();
      CSVFormat cSVFormat1 = cSVFormat0.withHeader(resultSet0);
      assertFalse(cSVFormat1.getSkipHeaderRecord());
      assertFalse(cSVFormat1.getAutoFlush());
      assertFalse(cSVFormat1.getTrim());
      assertFalse(cSVFormat1.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat1.getIgnoreHeaderCase());
      assertFalse(cSVFormat1.getIgnoreEmptyLines());
      assertEquals("u", cSVFormat1.getDelimiterString());
      assertFalse(cSVFormat1.getTrailingDelimiter());
      assertFalse(cSVFormat1.getAllowMissingColumnNames());
  }

  @Test(timeout = 4000)
  public void test039()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      Class<DuplicateHeaderMode> class0 = DuplicateHeaderMode.class;
      CSVFormat cSVFormat1 = cSVFormat0.POSTGRESQL_TEXT.withHeader(class0);
      assertFalse(cSVFormat1.equals((Object)cSVFormat0));
  }

  @Test(timeout = 4000)
  public void test040()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('C');
      Class<DuplicateHeaderMode> class0 = DuplicateHeaderMode.class;
      CSVFormat cSVFormat1 = cSVFormat0.withHeader(class0);
      assertFalse(cSVFormat1.getAutoFlush());
      assertFalse(cSVFormat1.getIgnoreSurroundingSpaces());
      assertEquals('C', cSVFormat1.getDelimiter());
      assertFalse(cSVFormat1.getTrim());
      assertFalse(cSVFormat1.getIgnoreHeaderCase());
      assertFalse(cSVFormat1.equals((Object)cSVFormat0));
      assertFalse(cSVFormat1.getIgnoreEmptyLines());
      assertFalse(cSVFormat1.getSkipHeaderRecord());
      assertFalse(cSVFormat1.getAllowMissingColumnNames());
  }

  @Test(timeout = 4000)
  public void test041()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('C');
      CSVFormat cSVFormat1 = cSVFormat0.withFirstRecordAsHeader();
      CSVFormat cSVFormat2 = cSVFormat1.withRecordSeparator('v');
      assertEquals("v", cSVFormat2.getRecordSeparator());
      assertEquals('C', cSVFormat2.getDelimiter());
      assertFalse(cSVFormat2.getTrailingDelimiter());
      assertFalse(cSVFormat2.getTrim());
      assertTrue(cSVFormat2.getSkipHeaderRecord());
      assertFalse(cSVFormat2.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat2.getAutoFlush());
      assertFalse(cSVFormat2.isNullStringSet());
      assertFalse(cSVFormat0.getSkipHeaderRecord());
      assertFalse(cSVFormat2.getAllowMissingColumnNames());
      assertFalse(cSVFormat2.getIgnoreEmptyLines());
  }

  @Test(timeout = 4000)
  public void test042()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      Character character0 = Character.valueOf('?');
      CSVFormat cSVFormat1 = cSVFormat0.POSTGRESQL_TEXT.withEscape(character0);
      assertFalse(cSVFormat1.equals((Object)cSVFormat0));
  }

  @Test(timeout = 4000)
  public void test043()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      Character character0 = new Character('n');
      CSVFormat cSVFormat1 = cSVFormat0.EXCEL.withEscape(character0);
      assertNull(cSVFormat1.getNullString());
  }

  @Test(timeout = 4000)
  public void test044()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      CSVFormat cSVFormat1 = cSVFormat0.MYSQL.withEscape('f');
      assertEquals('f', (char)cSVFormat1.getEscapeCharacter());
  }

  @Test(timeout = 4000)
  public void test045()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      CSVFormat cSVFormat1 = cSVFormat0.MYSQL.withDelimiter('(');
      assertEquals("(", cSVFormat1.getDelimiterString());
  }

  @Test(timeout = 4000)
  public void test046()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.withDelimiter('L');
      assertEquals("L", cSVFormat1.getDelimiterString());
  }

  @Test(timeout = 4000)
  public void test047()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreSurroundingSpaces(true);
      CSVFormat cSVFormat2 = cSVFormat1.withDelimiter('b');
      assertTrue(cSVFormat2.getIgnoreSurroundingSpaces());
      assertEquals('b', cSVFormat2.getDelimiter());
  }

  @Test(timeout = 4000)
  public void test048()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.MONGODB_CSV.withDelimiter('Z');
      assertEquals("Z", cSVFormat1.getDelimiterString());
  }

  @Test(timeout = 4000)
  public void test049()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreSurroundingSpaces();
      CSVFormat cSVFormat2 = cSVFormat1.withSkipHeaderRecord(true);
      Character character0 = new Character('d');
      CSVFormat cSVFormat3 = cSVFormat2.withCommentMarker(character0);
      assertTrue(cSVFormat3.getIgnoreSurroundingSpaces());
      assertTrue(cSVFormat3.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test050()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      Character character0 = new Character('j');
      CSVFormat cSVFormat1 = cSVFormat0.EXCEL.withCommentMarker(character0);
      assertFalse(cSVFormat1.getAutoFlush());
  }

  @Test(timeout = 4000)
  public void test051()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreSurroundingSpaces(true);
      CSVFormat cSVFormat2 = cSVFormat1.withCommentMarker('b');
      assertTrue(cSVFormat2.getIgnoreSurroundingSpaces());
      assertEquals('b', (char)cSVFormat2.getCommentMarker());
  }

  @Test(timeout = 4000)
  public void test052()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.MONGODB_TSV.withCommentMarker('V');
      assertEquals('V', (char)cSVFormat1.getCommentMarker());
  }

  @Test(timeout = 4000)
  public void test053()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      CSVFormat cSVFormat1 = cSVFormat0.withCommentMarker('f');
      assertEquals('f', (char)cSVFormat1.getCommentMarker());
  }

  @Test(timeout = 4000)
  public void test054()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreSurroundingSpaces(true);
      CSVFormat cSVFormat2 = cSVFormat1.withAutoFlush(true);
      assertTrue(cSVFormat2.getIgnoreSurroundingSpaces());
      assertTrue(cSVFormat2.getAutoFlush());
  }

  @Test(timeout = 4000)
  public void test055()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('Z');
      CSVFormat cSVFormat1 = cSVFormat0.withAllowMissingColumnNames(false);
      assertFalse(cSVFormat1.getIgnoreHeaderCase());
      assertFalse(cSVFormat1.getIgnoreEmptyLines());
      assertFalse(cSVFormat1.getTrailingDelimiter());
      assertFalse(cSVFormat1.getSkipHeaderRecord());
      assertEquals("Z", cSVFormat1.getDelimiterString());
      assertFalse(cSVFormat1.getAutoFlush());
      assertTrue(cSVFormat1.equals((Object)cSVFormat0));
      assertFalse(cSVFormat1.getTrim());
      assertFalse(cSVFormat1.isNullStringSet());
  }

  @Test(timeout = 4000)
  public void test056()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withFirstRecordAsHeader();
      CSVFormat cSVFormat2 = cSVFormat1.withAllowMissingColumnNames(false);
      assertTrue(cSVFormat2.getSkipHeaderRecord());
      assertFalse(cSVFormat2.getAllowMissingColumnNames());
  }

  @Test(timeout = 4000)
  public void test057()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.withAllowMissingColumnNames(false);
      assertFalse(cSVFormat1.getAllowMissingColumnNames());
  }

  @Test(timeout = 4000)
  public void test058()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.RFC4180.withAllowMissingColumnNames();
      assertTrue(cSVFormat1.getAllowMissingColumnNames());
  }

  @Test(timeout = 4000)
  public void test059()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreSurroundingSpaces(true);
      CSVFormat cSVFormat2 = cSVFormat1.withAllowMissingColumnNames();
      assertTrue(cSVFormat2.getAllowMissingColumnNames());
      assertTrue(cSVFormat2.getIgnoreSurroundingSpaces());
  }

  @Test(timeout = 4000)
  public void test060()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withAutoFlush(true);
      CSVFormat cSVFormat2 = cSVFormat1.withAllowMissingColumnNames();
      assertTrue(cSVFormat2.getAllowMissingColumnNames());
      assertTrue(cSVFormat2.getAutoFlush());
  }

  @Test(timeout = 4000)
  public void test061()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('C');
      CSVFormat cSVFormat1 = cSVFormat0.withAllowDuplicateHeaderNames(false);
      assertFalse(cSVFormat1.getIgnoreHeaderCase());
      assertFalse(cSVFormat1.getIgnoreEmptyLines());
      assertEquals("C", cSVFormat1.getDelimiterString());
      assertFalse(cSVFormat1.getIgnoreSurroundingSpaces());
      assertEquals(DuplicateHeaderMode.ALLOW_EMPTY, cSVFormat1.getDuplicateHeaderMode());
      assertFalse(cSVFormat1.getTrim());
      assertFalse(cSVFormat1.getAllowMissingColumnNames());
      assertFalse(cSVFormat1.getSkipHeaderRecord());
      assertFalse(cSVFormat1.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test062()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreSurroundingSpaces();
      CSVFormat cSVFormat2 = cSVFormat1.withAllowDuplicateHeaderNames(true);
      assertTrue(cSVFormat2.getAllowDuplicateHeaderNames());
      assertTrue(cSVFormat2.getIgnoreSurroundingSpaces());
  }

  @Test(timeout = 4000)
  public void test063()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      cSVFormat_Builder0.setIgnoreHeaderCase(true);
      CSVFormat cSVFormat1 = cSVFormat_Builder0.build();
      CSVFormat cSVFormat2 = cSVFormat1.withAllowDuplicateHeaderNames(true);
      assertTrue(cSVFormat2.getAllowDuplicateHeaderNames());
      assertTrue(cSVFormat2.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test064()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      CSVFormat cSVFormat1 = cSVFormat0.withTrim();
      CSVFormat cSVFormat2 = cSVFormat1.withAllowDuplicateHeaderNames();
      assertTrue(cSVFormat2.getTrim());
  }

  @Test(timeout = 4000)
  public void test065()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat cSVFormat1 = cSVFormat0.EXCEL.withAllowDuplicateHeaderNames();
      assertFalse(cSVFormat1.isCommentMarkerSet());
  }

  @Test(timeout = 4000)
  public void test066()  throws Throwable  {
      String[] stringArray0 = CSVFormat.toStringArray((Object[]) null);
      assertNull(stringArray0);
  }

  @Test(timeout = 4000)
  public void test067()  throws Throwable  {
      String[] stringArray0 = new String[5];
      String[] stringArray1 = CSVFormat.toStringArray(stringArray0);
      assertFalse(stringArray1.equals((Object)stringArray0));
  }

  @Test(timeout = 4000)
  public void test068()  throws Throwable  {
      Object[] objectArray0 = new Object[0];
      String[] stringArray0 = CSVFormat.toStringArray(objectArray0);
      assertEquals(0, stringArray0.length);
  }

  @Test(timeout = 4000)
  public void test069()  throws Throwable  {
      boolean boolean0 = CSVFormat.isBlank("Sc");
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test070()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreSurroundingSpaces(true);
      boolean boolean0 = cSVFormat1.getIgnoreSurroundingSpaces();
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test071()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat cSVFormat1 = cSVFormat0.withAutoFlush(true);
      boolean boolean0 = cSVFormat1.getAutoFlush();
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test072()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      Object[] objectArray0 = new Object[0];
      String string0 = cSVFormat0.ORACLE.format(objectArray0);
      assertEquals("", string0);
  }

  @Test(timeout = 4000)
  public void test073()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.copy();
      assertTrue(cSVFormat1.isNullStringSet());
  }

  @Test(timeout = 4000)
  public void test074()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.copy();
      assertEquals("\n", cSVFormat1.getRecordSeparator());
  }

  @Test(timeout = 4000)
  public void test075()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      // Undeclared exception!
      try { 
        cSVFormat0.MONGODB_CSV.parse((Reader) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // reader
         //
         verifyException("java.util.Objects", e);
      }
  }

  @Test(timeout = 4000)
  public void test076()  throws Throwable  {
      CSVFormat.Predefined cSVFormat_Predefined0 = CSVFormat.Predefined.MongoDBTsv;
      CSVFormat cSVFormat0 = cSVFormat_Predefined0.getFormat();
      PipedWriter pipedWriter0 = new PipedWriter();
      BufferedWriter bufferedWriter0 = new BufferedWriter(pipedWriter0, 92);
      cSVFormat0.ORACLE.println(bufferedWriter0);
      assertNull(cSVFormat0.getNullString());
  }

  @Test(timeout = 4000)
  public void test077()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      StringWriter stringWriter0 = new StringWriter();
      cSVFormat0.INFORMIX_UNLOAD.print((Object) cSVFormat0, (Appendable) stringWriter0, true);
      assertTrue(cSVFormat0.isEscapeCharacterSet());
      assertTrue(cSVFormat0.isQuoteCharacterSet());
  }

  @Test(timeout = 4000)
  public void test078()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      boolean boolean0 = cSVFormat0.isQuoteCharacterSet();
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test079()  throws Throwable  {
      CSVFormat.Predefined cSVFormat_Predefined0 = CSVFormat.Predefined.MongoDBTsv;
      CSVFormat cSVFormat0 = cSVFormat_Predefined0.getFormat();
      boolean boolean0 = cSVFormat0.isEscapeCharacterSet();
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test080()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      boolean boolean0 = cSVFormat0.isEscapeCharacterSet();
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test081()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      boolean boolean0 = cSVFormat0.isCommentMarkerSet();
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test082()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('u');
      cSVFormat0.getHeaderComments();
      assertFalse(cSVFormat0.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat0.getTrim());
      assertFalse(cSVFormat0.getIgnoreEmptyLines());
      assertFalse(cSVFormat0.getAllowMissingColumnNames());
      assertFalse(cSVFormat0.getIgnoreHeaderCase());
      assertFalse(cSVFormat0.getSkipHeaderRecord());
      assertEquals("u", cSVFormat0.getDelimiterString());
      assertFalse(cSVFormat0.getAutoFlush());
      assertFalse(cSVFormat0.getTrailingDelimiter());
      assertFalse(cSVFormat0.isNullStringSet());
  }

  @Test(timeout = 4000)
  public void test083()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_CSV;
      String[] stringArray0 = cSVFormat0.getHeader();
      assertNull(stringArray0);
  }

  @Test(timeout = 4000)
  public void test084()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      Character character0 = Character.valueOf('_');
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setQuote(character0);
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test085()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('u');
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      ResultSet resultSet0 = mock(ResultSet.class, new ViolatedAssumptionAnswer());
      doReturn((ResultSetMetaData) null).when(resultSet0).getMetaData();
      cSVFormat_Builder0.setHeader(resultSet0);
      assertFalse(cSVFormat0.getTrailingDelimiter());
      assertFalse(cSVFormat0.getSkipHeaderRecord());
      assertFalse(cSVFormat0.getIgnoreEmptyLines());
      assertFalse(cSVFormat0.getIgnoreHeaderCase());
      assertFalse(cSVFormat0.getTrim());
      assertFalse(cSVFormat0.getAllowMissingColumnNames());
      assertEquals('u', cSVFormat0.getDelimiter());
      assertFalse(cSVFormat0.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat0.getAutoFlush());
  }

  @Test(timeout = 4000)
  public void test086()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      Class<DuplicateHeaderMode> class0 = DuplicateHeaderMode.class;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setHeader(class0);
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test087()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setDelimiter(".w");
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test088()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setNullString("");
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test089()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat.Builder cSVFormat_Builder0 = cSVFormat0.builder();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setSkipHeaderRecord(false);
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test090()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat.Builder cSVFormat_Builder0 = cSVFormat0.EXCEL.builder();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setTrim(true);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test091()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      String[] stringArray0 = new String[7];
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setHeader(stringArray0);
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test092()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      String string0 = cSVFormat0.getDelimiterString();
      assertEquals(",", string0);
  }

  @Test(timeout = 4000)
  public void test093()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setAllowMissingColumnNames(false);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test094()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setAutoFlush(false);
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test095()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setEscape('4');
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test096()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setDelimiter('*');
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test097()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setIgnoreEmptyLines(true);
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test098()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      boolean boolean0 = cSVFormat0.getIgnoreHeaderCase();
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test099()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setIgnoreSurroundingSpaces(false);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test100()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setTrailingDelimiter(false);
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test101()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      boolean boolean0 = cSVFormat0.getTrailingDelimiter();
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test102()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      boolean boolean0 = cSVFormat0.getAutoFlush();
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test103()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setRecordSeparator("11B\"d");
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test104()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      boolean boolean0 = cSVFormat0.getIgnoreSurroundingSpaces();
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test105()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      boolean boolean0 = cSVFormat0.getIgnoreEmptyLines();
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test106()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      Character character0 = cSVFormat0.getEscapeCharacter();
      assertNull(character0);
  }

  @Test(timeout = 4000)
  public void test107()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      CSVFormat cSVFormat1 = cSVFormat0.withAllowDuplicateHeaderNames(true);
      assertEquals(DuplicateHeaderMode.ALLOW_ALL, cSVFormat1.getDuplicateHeaderMode());
  }

  @Test(timeout = 4000)
  public void test108()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      RowSetMetaDataImpl rowSetMetaDataImpl0 = new RowSetMetaDataImpl();
      cSVFormat_Builder0.setHeader((ResultSetMetaData) rowSetMetaDataImpl0);
      DuplicateHeaderMode duplicateHeaderMode0 = DuplicateHeaderMode.DISALLOW;
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setDuplicateHeaderMode(duplicateHeaderMode0);
      CSVFormat cSVFormat1 = cSVFormat_Builder1.build();
      assertTrue(cSVFormat1.isNullStringSet());
  }

  @Test(timeout = 4000)
  public void test109()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      QuoteMode quoteMode0 = QuoteMode.NONE;
      // Undeclared exception!
      try { 
        cSVFormat0.EXCEL.withQuoteMode(quoteMode0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // No quotes mode set but no escape character is set
         //
         verifyException("org.apache.commons.csv.CSVFormat", e);
      }
  }

  @Test(timeout = 4000)
  public void test110()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      Character character0 = new Character('}');
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setEscape(character0);
      CSVFormat cSVFormat1 = cSVFormat_Builder1.build();
      // Undeclared exception!
      try { 
        cSVFormat1.withCommentMarker('}');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The comment start and the escape character cannot be the same ('}')
         //
         verifyException("org.apache.commons.csv.CSVFormat", e);
      }
  }

  @Test(timeout = 4000)
  public void test111()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      Character character0 = Character.valueOf('k');
      CSVFormat cSVFormat1 = cSVFormat0.withCommentMarker(character0);
      // Undeclared exception!
      try { 
        cSVFormat1.withQuote(character0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The comment start character and the quoteChar cannot be the same ('k')
         //
         verifyException("org.apache.commons.csv.CSVFormat", e);
      }
  }

  @Test(timeout = 4000)
  public void test112()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      String string0 = cSVFormat0.trim("");
      assertEquals("", string0);
  }

  @Test(timeout = 4000)
  public void test113()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVFormat cSVFormat1 = cSVFormat0.INFORMIX_UNLOAD.withFirstRecordAsHeader();
      assertTrue(cSVFormat1.getSkipHeaderRecord());
      
      Object[] objectArray0 = new Object[9];
      objectArray0[3] = (Object) cSVFormat1;
      CSVFormat cSVFormat2 = cSVFormat0.withHeaderComments(objectArray0);
      assertFalse(cSVFormat2.isCommentMarkerSet());
      assertTrue(cSVFormat2.isEscapeCharacterSet());
      assertTrue(cSVFormat2.isQuoteCharacterSet());
      assertFalse(cSVFormat2.isNullStringSet());
  }

  @Test(timeout = 4000)
  public void test114()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreSurroundingSpaces();
      String string0 = cSVFormat1.toString();
      assertEquals("Delimiter=<,> QuoteChar=<\"> RecordSeparator=<\n> EmptyLines:ignored SurroundingSpaces:ignored SkipHeaderRecord:false", string0);
  }

  @Test(timeout = 4000)
  public void test115()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      Object[] objectArray0 = new Object[9];
      objectArray0[1] = (Object) cSVFormat0;
      CSVFormat cSVFormat1 = cSVFormat0.withHeaderComments(objectArray0);
      assertTrue(cSVFormat1.isQuoteCharacterSet());
      assertFalse(cSVFormat1.isCommentMarkerSet());
      assertTrue(cSVFormat1.isEscapeCharacterSet());
      assertFalse(cSVFormat1.isNullStringSet());
  }

  @Test(timeout = 4000)
  public void test116()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      String string0 = cSVFormat0.toString();
      assertEquals("Delimiter=<,> QuoteChar=<\"> RecordSeparator=<\n> EmptyLines:ignored SkipHeaderRecord:false", string0);
  }

  @Test(timeout = 4000)
  public void test117()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      String[] stringArray0 = new String[9];
      stringArray0[2] = "";
      MockFileWriter mockFileWriter0 = new MockFileWriter("h7YfB:f", true);
      cSVFormat0.printRecord(mockFileWriter0, stringArray0);
      assertTrue(cSVFormat0.isQuoteCharacterSet());
      assertTrue(cSVFormat0.isEscapeCharacterSet());
  }

  @Test(timeout = 4000)
  public void test118()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      CharArrayWriter charArrayWriter0 = new CharArrayWriter(124);
      Object[] objectArray0 = new Object[7];
      objectArray0[0] = (Object) charArrayWriter0;
      cSVFormat0.MYSQL.printRecord(charArrayWriter0, objectArray0);
      assertEquals(53, charArrayWriter0.size());
  }

  @Test(timeout = 4000)
  public void test119()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('Z');
      assertFalse(cSVFormat0.isQuoteCharacterSet());
      
      String[] stringArray0 = new String[4];
      stringArray0[0] = "The quoteChar cannot be a line break";
      String string0 = cSVFormat0.format(stringArray0);
      assertFalse(cSVFormat0.getIgnoreSurroundingSpaces());
      assertEquals("The quoteChar cannot be a line breakZZZ", string0);
      assertFalse(cSVFormat0.getTrim());
      assertFalse(cSVFormat0.getAutoFlush());
      assertFalse(cSVFormat0.getIgnoreHeaderCase());
      assertFalse(cSVFormat0.getAllowMissingColumnNames());
      assertFalse(cSVFormat0.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test120()  throws Throwable  {
      CSVFormat.Predefined cSVFormat_Predefined0 = CSVFormat.Predefined.PostgreSQLCsv;
      CSVFormat cSVFormat0 = cSVFormat_Predefined0.getFormat();
      assertFalse(cSVFormat0.isEscapeCharacterSet());
      
      Object[] objectArray0 = new Object[4];
      objectArray0[0] = (Object) cSVFormat_Predefined0;
      cSVFormat0.format(objectArray0);
      assertTrue(cSVFormat0.isQuoteCharacterSet());
  }

  @Test(timeout = 4000)
  public void test121()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      String[] stringArray0 = new String[9];
      stringArray0[3] = ",";
      MockFileWriter mockFileWriter0 = new MockFileWriter("h7YfB:f", true);
      cSVFormat0.printRecord(mockFileWriter0, stringArray0);
      assertTrue(cSVFormat0.isEscapeCharacterSet());
      assertTrue(cSVFormat0.isQuoteCharacterSet());
  }

  @Test(timeout = 4000)
  public void test122()  throws Throwable  {
      CSVFormat.Predefined cSVFormat_Predefined0 = CSVFormat.Predefined.MongoDBTsv;
      CSVFormat cSVFormat0 = cSVFormat_Predefined0.getFormat();
      String string0 = cSVFormat0.INFORMIX_UNLOAD.toString();
      assertEquals("Delimiter=<|> Escape=<\\> QuoteChar=<\"> RecordSeparator=<\n> EmptyLines:ignored SkipHeaderRecord:false", string0);
  }

  @Test(timeout = 4000)
  public void test123()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      boolean boolean0 = cSVFormat0.getAllowDuplicateHeaderNames();
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test124()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      Character character0 = new Character('h');
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setEscape(character0);
      CSVFormat cSVFormat0 = cSVFormat_Builder1.build();
      CSVFormat cSVFormat1 = cSVFormat0.withEscape('h');
      boolean boolean0 = cSVFormat0.equals(cSVFormat1);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test125()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat cSVFormat0 = cSVFormat_Builder0.build();
      CSVFormat cSVFormat1 = cSVFormat0.withRecordSeparator('h');
      boolean boolean0 = cSVFormat1.equals(cSVFormat0);
      assertEquals("h", cSVFormat1.getRecordSeparator());
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test126()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat cSVFormat1 = cSVFormat0.withAllowMissingColumnNames();
      CSVFormat cSVFormat2 = cSVFormat1.withNullString("PostgreSQLText");
      CSVFormat cSVFormat3 = cSVFormat2.MYSQL.withAllowDuplicateHeaderNames();
      boolean boolean0 = cSVFormat2.equals(cSVFormat3);
      assertFalse(cSVFormat3.equals((Object)cSVFormat1));
      assertTrue(cSVFormat3.equals((Object)cSVFormat0));
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test127()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      boolean boolean0 = cSVFormat0.POSTGRESQL_CSV.equals(cSVFormat0);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test128()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setCommentMarker((Character) null);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test129()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_CSV;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setHeader((ResultSet) null);
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test130()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat cSVFormat1 = cSVFormat0.INFORMIX_UNLOAD_CSV.withSystemRecordSeparator();
      assertEquals("\n", cSVFormat1.getRecordSeparator());
  }

  @Test(timeout = 4000)
  public void test131()  throws Throwable  {
      CSVFormat.Predefined cSVFormat_Predefined0 = CSVFormat.Predefined.MongoDBCsv;
      CSVFormat cSVFormat0 = cSVFormat_Predefined0.getFormat();
      RowSetMetaDataImpl rowSetMetaDataImpl0 = new RowSetMetaDataImpl();
      CSVFormat cSVFormat1 = cSVFormat0.withHeader((ResultSetMetaData) rowSetMetaDataImpl0);
      CSVFormat cSVFormat2 = cSVFormat1.withAllowMissingColumnNames();
      PipedReader pipedReader0 = new PipedReader();
      try { 
        cSVFormat2.parse(pipedReader0);
        fail("Expecting exception: IOException");
      
      } catch(IOException e) {
         //
         // Pipe not connected
         //
         verifyException("java.io.PipedReader", e);
      }
  }

  @Test(timeout = 4000)
  public void test132()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      URI uRI0 = MockURI.aFileURI;
      MockFile mockFile0 = new MockFile(uRI0);
      Charset charset0 = Charset.defaultCharset();
      CSVPrinter cSVPrinter0 = cSVFormat0.print((File) mockFile0, charset0);
      assertNotNull(cSVPrinter0);
  }

  @Test(timeout = 4000)
  public void test133()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat cSVFormat0 = cSVFormat_Builder0.build();
      CSVFormat cSVFormat1 = cSVFormat0.TDF.withIgnoreEmptyLines(true);
      assertTrue(cSVFormat1.getIgnoreEmptyLines());
  }

  @Test(timeout = 4000)
  public void test134()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat cSVFormat0 = cSVFormat_Builder0.build();
      DuplicateHeaderMode duplicateHeaderMode0 = cSVFormat0.getDuplicateHeaderMode();
      assertEquals(DuplicateHeaderMode.ALLOW_ALL, duplicateHeaderMode0);
  }

  @Test(timeout = 4000)
  public void test135()  throws Throwable  {
      // Undeclared exception!
      try { 
        CSVFormat.valueOf("The delimiter cannot be empty");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // No enum constant org.apache.commons.csv.CSVFormat.Predefined.The delimiter cannot be empty
         //
         verifyException("java.lang.Enum", e);
      }
  }

  @Test(timeout = 4000)
  public void test136()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      CSVFormat cSVFormat1 = cSVFormat0.withSkipHeaderRecord(true);
      CSVFormat cSVFormat2 = cSVFormat1.withAllowDuplicateHeaderNames(true);
      assertTrue(cSVFormat2.getAllowDuplicateHeaderNames());
      assertTrue(cSVFormat2.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test137()  throws Throwable  {
    Future<?> future = executor.submit(new Runnable(){ 
            @Override public void run() { 
        try {
          CSVFormat cSVFormat0 = CSVFormat.EXCEL;
          MockFile mockFile0 = new MockFile("");
          Path path0 = mockFile0.toPath();
          Charset charset0 = Charset.defaultCharset();
          // Undeclared exception!
          try { 
            cSVFormat0.TDF.print(path0, charset0);
            fail("Expecting exception: SecurityException");
          
          } catch(SecurityException e) {
             //
             // Security manager blocks (\"java.io.FilePermission\" \"\" \"write\")
             // java.lang.Thread.getStackTrace(Thread.java:1559)
             // org.evosuite.runtime.sandbox.MSecurityManager.checkPermission(MSecurityManager.java:424)
             // java.lang.SecurityManager.checkWrite(SecurityManager.java:979)
             // sun.nio.fs.UnixChannelFactory.open(UnixChannelFactory.java:247)
             // sun.nio.fs.UnixChannelFactory.newFileChannel(UnixChannelFactory.java:136)
             // sun.nio.fs.UnixChannelFactory.newFileChannel(UnixChannelFactory.java:148)
             // sun.nio.fs.UnixFileSystemProvider.newByteChannel(UnixFileSystemProvider.java:212)
             // java.nio.file.spi.FileSystemProvider.newOutputStream(FileSystemProvider.java:434)
             // java.nio.file.Files.newOutputStream(Files.java:216)
             // java.nio.file.Files.newBufferedWriter(Files.java:2860)
             // org.apache.commons.csv.CSVFormat.print(CSVFormat.java:1886)
             // sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
             // sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
             // sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
             // java.lang.reflect.Method.invoke(Method.java:498)
             // org.evosuite.testcase.statements.MethodStatement$1.execute(MethodStatement.java:256)
             // org.evosuite.testcase.statements.AbstractStatement.exceptionHandler(AbstractStatement.java:165)
             // org.evosuite.testcase.statements.MethodStatement.execute(MethodStatement.java:219)
             // org.evosuite.testcase.execution.TestRunnable.executeStatements(TestRunnable.java:286)
             // org.evosuite.testcase.execution.TestRunnable.call(TestRunnable.java:192)
             // org.evosuite.testcase.execution.TestRunnable.call(TestRunnable.java:49)
             // java.util.concurrent.FutureTask.run(FutureTask.java:266)
             // java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
             // java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
             // java.lang.Thread.run(Thread.java:748)
             //
             verifyException("org.evosuite.runtime.sandbox.MSecurityManager", e);
          }
        } catch(Throwable t) {
            // Need to catch declared exceptions
        }
      } 
    });
    future.get(4000, TimeUnit.MILLISECONDS);
  }

  @Test(timeout = 4000)
  public void test138()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('C');
      MockFile mockFile0 = new MockFile("Sc", "The quoteChar cannot be a line break");
      MockPrintStream mockPrintStream0 = new MockPrintStream(mockFile0);
      cSVFormat0.MONGODB_TSV.print((Appendable) mockPrintStream0);
      assertFalse(cSVFormat0.getSkipHeaderRecord());
      assertFalse(cSVFormat0.getIgnoreHeaderCase());
      assertFalse(cSVFormat0.getAllowMissingColumnNames());
      assertFalse(cSVFormat0.getIgnoreEmptyLines());
      assertFalse(cSVFormat0.getTrim());
      assertFalse(cSVFormat0.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat0.getAutoFlush());
      assertFalse(cSVFormat0.getTrailingDelimiter());
      assertEquals("C", cSVFormat0.getDelimiterString());
  }

  @Test(timeout = 4000)
  public void test139()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.EXCEL.withTrailingDelimiter(true);
      assertTrue(cSVFormat1.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test140()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_CSV;
      cSVFormat0.MONGODB_CSV.hashCode();
  }

  @Test(timeout = 4000)
  public void test141()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      boolean boolean0 = cSVFormat0.getAllowMissingColumnNames();
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test142()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      CSVFormat cSVFormat1 = cSVFormat0.withTrim();
      CSVFormat cSVFormat2 = cSVFormat1.withNullString("");
      assertTrue(cSVFormat2.getTrim());
  }

  @Test(timeout = 4000)
  public void test143()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      CSVFormat cSVFormat1 = cSVFormat0.withQuote('h');
      assertEquals('h', (char)cSVFormat1.getQuoteCharacter());
  }

  @Test(timeout = 4000)
  public void test144()  throws Throwable  {
      CSVFormat.Predefined cSVFormat_Predefined0 = CSVFormat.Predefined.MongoDBCsv;
      CSVFormat cSVFormat0 = cSVFormat_Predefined0.getFormat();
      PipedReader pipedReader0 = new PipedReader();
      CSVParser cSVParser0 = cSVFormat0.parse(pipedReader0);
      assertEquals(0L, cSVParser0.getRecordNumber());
  }

  @Test(timeout = 4000)
  public void test145()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      Character character0 = new Character('h');
      CSVFormat cSVFormat0 = cSVFormat_Builder0.build();
      CSVFormat cSVFormat1 = cSVFormat0.withEscape(character0);
      assertTrue(cSVFormat1.getIgnoreEmptyLines());
  }

  @Test(timeout = 4000)
  public void test146()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      Character character0 = Character.valueOf('D');
      CSVFormat cSVFormat1 = cSVFormat0.INFORMIX_UNLOAD.withQuote(character0);
      boolean boolean0 = cSVFormat1.getIgnoreEmptyLines();
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test147()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      RowSetMetaDataImpl rowSetMetaDataImpl0 = new RowSetMetaDataImpl();
      ResultSet resultSet0 = mock(ResultSet.class, new ViolatedAssumptionAnswer());
      doReturn(rowSetMetaDataImpl0).when(resultSet0).getMetaData();
      CSVFormat cSVFormat1 = cSVFormat0.DEFAULT.withHeader(resultSet0);
      assertFalse(cSVFormat1.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test148()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat cSVFormat1 = cSVFormat0.MYSQL.withCommentMarker('I');
      CSVFormat cSVFormat2 = cSVFormat1.withTrim(true);
      assertTrue(cSVFormat2.getTrim());
      assertEquals('I', (char)cSVFormat2.getCommentMarker());
  }

  @Test(timeout = 4000)
  public void test149()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      String string0 = cSVFormat0.getRecordSeparator();
      assertEquals("\n", string0);
  }

  @Test(timeout = 4000)
  public void test150()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      Class<DuplicateHeaderMode> class0 = DuplicateHeaderMode.class;
      CSVFormat cSVFormat1 = cSVFormat0.withHeader(class0);
      CSVFormat cSVFormat2 = cSVFormat1.withAllowDuplicateHeaderNames(false);
      assertFalse(cSVFormat1.equals((Object)cSVFormat0));
      assertEquals(DuplicateHeaderMode.ALLOW_EMPTY, cSVFormat2.getDuplicateHeaderMode());
  }

  @Test(timeout = 4000)
  public void test151()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVPrinter cSVPrinter0 = cSVFormat0.POSTGRESQL_CSV.printer();
      assertNotNull(cSVPrinter0);
  }

  @Test(timeout = 4000)
  public void test152()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('C');
      CSVFormat cSVFormat1 = cSVFormat0.copy();
      assertFalse(cSVFormat1.getTrailingDelimiter());
      assertFalse(cSVFormat1.getAutoFlush());
      assertFalse(cSVFormat1.getAllowMissingColumnNames());
      assertFalse(cSVFormat1.getSkipHeaderRecord());
      assertFalse(cSVFormat1.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat1.getTrim());
      assertFalse(cSVFormat1.getIgnoreEmptyLines());
      assertEquals('C', cSVFormat1.getDelimiter());
      assertFalse(cSVFormat1.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test153()  throws Throwable  {
      CSVFormat.Predefined cSVFormat_Predefined0 = CSVFormat.Predefined.PostgreSQLCsv;
      CSVFormat cSVFormat0 = cSVFormat_Predefined0.getFormat();
      boolean boolean0 = cSVFormat0.getSkipHeaderRecord();
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test154()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      char char0 = cSVFormat0.getDelimiter();
      assertEquals(',', char0);
  }

  @Test(timeout = 4000)
  public void test155()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      CSVFormat cSVFormat1 = cSVFormat0.withAllowDuplicateHeaderNames(false);
      CSVFormat cSVFormat2 = cSVFormat1.withAutoFlush(false);
      assertFalse(cSVFormat2.getAllowDuplicateHeaderNames());
      assertFalse(cSVFormat2.getAutoFlush());
  }

  @Test(timeout = 4000)
  public void test156()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withSkipHeaderRecord();
      CSVFormat cSVFormat2 = cSVFormat1.withEscape('#');
      CSVFormat cSVFormat3 = cSVFormat2.withTrim(true);
      assertEquals('#', (char)cSVFormat3.getEscapeCharacter());
      assertTrue(cSVFormat3.getTrim());
      assertTrue(cSVFormat3.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test157()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.EXCEL.withAutoFlush(false);
      assertFalse(cSVFormat1.getAutoFlush());
  }

  @Test(timeout = 4000)
  public void test158()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVFormat.Builder cSVFormat_Builder0 = cSVFormat0.builder();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setCommentMarker('M');
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test159()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_CSV;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setQuote('y');
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test160()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_CSV;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      String[] stringArray0 = new String[9];
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setHeaderComments(stringArray0);
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test161()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreSurroundingSpaces(true);
      CSVFormat cSVFormat2 = cSVFormat1.withRecordSeparator(" IgnoreHeaderCase:ignored");
      CSVFormat cSVFormat3 = cSVFormat2.copy();
      assertTrue(cSVFormat3.getIgnoreSurroundingSpaces());
  }

  @Test(timeout = 4000)
  public void test162()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.withRecordSeparator("Sc");
      assertTrue(cSVFormat1.isQuoteCharacterSet());
  }

  @Test(timeout = 4000)
  public void test163()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setRecordSeparator('X');
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test164()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('u');
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      cSVFormat_Builder0.setHeaderComments((Object[]) null);
      assertFalse(cSVFormat0.getTrailingDelimiter());
      assertFalse(cSVFormat0.getAutoFlush());
      assertFalse(cSVFormat0.getIgnoreEmptyLines());
      assertFalse(cSVFormat0.getTrim());
      assertFalse(cSVFormat0.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat0.getIgnoreHeaderCase());
      assertFalse(cSVFormat0.getSkipHeaderRecord());
      assertFalse(cSVFormat0.getAllowMissingColumnNames());
      assertEquals("u", cSVFormat0.getDelimiterString());
  }

  @Test(timeout = 4000)
  public void test165()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      QuoteMode quoteMode0 = QuoteMode.NONE;
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setQuoteMode(quoteMode0);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }
}
