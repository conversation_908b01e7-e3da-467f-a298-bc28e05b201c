/*
 * This file was automatically generated by EvoSuite
 * Wed Jun 18 07:42:18 GMT 2025
 */

package org.apache.commons.cli;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.io.ByteArrayOutputStream;
import java.io.DataOutputStream;
import java.io.File;
import java.io.FileDescriptor;
import java.io.FileNotFoundException;
import java.io.ObjectOutputStream;
import java.io.OutputStream;
import java.io.PrintStream;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.io.Writer;
import java.util.Comparator;
import java.util.Locale;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import org.apache.commons.cli.HelpFormatter;
import org.apache.commons.cli.Option;
import org.apache.commons.cli.OptionGroup;
import org.apache.commons.cli.Options;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.evosuite.runtime.mock.java.io.MockFile;
import org.evosuite.runtime.mock.java.io.MockFileOutputStream;
import org.evosuite.runtime.mock.java.io.MockPrintStream;
import org.evosuite.runtime.mock.java.io.MockPrintWriter;
import org.evosuite.runtime.testdata.EvoSuiteFile;
import org.evosuite.runtime.testdata.FileSystemHandling;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true) 
public class HelpFormatter_ESTest extends HelpFormatter_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals("\n", helpFormatter0.getNewLine());
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(74, helpFormatter0.defaultWidth);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertNotNull(helpFormatter0);
      
      StringBuffer stringBuffer0 = new StringBuffer(74);
      assertEquals("", stringBuffer0.toString());
      assertEquals(0, stringBuffer0.length());
      assertNotNull(stringBuffer0);
      
      Options options0 = new Options();
      assertNotNull(options0);
      
      Options options1 = options0.addRequiredOption("", "F>^xc`e\"x/6VK#sTd", true, "2I42H(2F]]`KuV>$");
      assertSame(options0, options1);
      assertSame(options1, options0);
      assertNotNull(options1);
      
      Options options2 = options1.addOption("", "", true, "");
      assertSame(options0, options2);
      assertSame(options0, options1);
      assertSame(options1, options0);
      assertSame(options1, options2);
      assertSame(options2, options0);
      assertSame(options2, options1);
      assertNotNull(options2);
      
      OptionGroup optionGroup0 = new OptionGroup();
      assertNull(optionGroup0.getSelected());
      assertFalse(optionGroup0.isRequired());
      assertNotNull(optionGroup0);
      
      Options options3 = options2.addOptionGroup(optionGroup0);
      assertNull(optionGroup0.getSelected());
      assertFalse(optionGroup0.isRequired());
      assertSame(options0, options3);
      assertSame(options0, options2);
      assertSame(options0, options1);
      assertSame(options1, options3);
      assertSame(options1, options0);
      assertSame(options1, options2);
      assertSame(options2, options3);
      assertSame(options2, options0);
      assertSame(options2, options1);
      assertSame(options3, options1);
      assertSame(options3, options2);
      assertSame(options3, options0);
      assertNotNull(options3);
      
      // Undeclared exception!
      try { 
        helpFormatter0.renderOptions(stringBuffer0, 34, options3, 34, (-9));
        fail("Expecting exception: NegativeArraySizeException");
      
      } catch(NegativeArraySizeException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.cli.HelpFormatter", e);
      }
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals("\n", helpFormatter0.getNewLine());
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(74, helpFormatter0.defaultWidth);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertNotNull(helpFormatter0);
      
      helpFormatter0.setNewLine((String) null);
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertNull(helpFormatter0.getNewLine());
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(74, helpFormatter0.defaultWidth);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      
      int int0 = 1995;
      int int1 = helpFormatter0.findWrapPos("H|zk!;a", 1995, (-957));
      assertEquals((-1), int1);
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertNull(helpFormatter0.getNewLine());
      assertFalse(int1 == int0);
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(74, helpFormatter0.defaultWidth);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      
      String string0 = helpFormatter0.getLongOptPrefix();
      assertEquals("--", string0);
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertNull(helpFormatter0.getNewLine());
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(74, helpFormatter0.defaultWidth);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertNotNull(string0);
      
      boolean boolean0 = FileSystemHandling.appendStringToFile((EvoSuiteFile) null, "arg");
      assertFalse(boolean0);
      
      String string1 = helpFormatter0.rtrim("");
      assertEquals("", string1);
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertNull(helpFormatter0.getNewLine());
      assertFalse(string1.equals((Object)string0));
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(74, helpFormatter0.defaultWidth);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertNotNull(string1);
      
      String string2 = helpFormatter0.getLongOptSeparator();
      assertEquals(" ", string2);
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertNull(helpFormatter0.getNewLine());
      assertFalse(string2.equals((Object)string0));
      assertFalse(string2.equals((Object)string1));
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(74, helpFormatter0.defaultWidth);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertNotNull(string2);
      
      String string3 = helpFormatter0.getOptPrefix();
      assertEquals("-", string3);
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertNull(helpFormatter0.getNewLine());
      assertFalse(string3.equals((Object)string0));
      assertFalse(string3.equals((Object)string2));
      assertFalse(string3.equals((Object)string1));
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(74, helpFormatter0.defaultWidth);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertNotNull(string3);
      
      String string4 = helpFormatter0.getSyntaxPrefix();
      assertEquals("usage: ", string4);
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertNull(helpFormatter0.getNewLine());
      assertFalse(string4.equals((Object)string0));
      assertFalse(string4.equals((Object)string1));
      assertFalse(string4.equals((Object)string2));
      assertFalse(string4.equals((Object)string3));
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(74, helpFormatter0.defaultWidth);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertNotNull(string4);
      
      StringBuffer stringBuffer0 = new StringBuffer((CharSequence) "usage: ");
      assertEquals(7, stringBuffer0.length());
      assertEquals("usage: ", stringBuffer0.toString());
      assertNotNull(stringBuffer0);
      
      int int2 = 1185;
      StringBuffer stringBuffer1 = stringBuffer0.append("");
      assertEquals(7, stringBuffer0.length());
      assertEquals("usage: ", stringBuffer0.toString());
      assertEquals("usage: ", stringBuffer1.toString());
      assertEquals(7, stringBuffer1.length());
      assertSame(stringBuffer0, stringBuffer1);
      assertSame(stringBuffer1, stringBuffer0);
      assertNotNull(stringBuffer1);
      
      int int3 = (-1300);
      char[] charArray0 = new char[5];
      charArray0[0] = '6';
      charArray0[1] = 'h';
      charArray0[2] = '%';
      charArray0[3] = '1';
      charArray0[4] = 'S';
      // Undeclared exception!
      try { 
        stringBuffer0.insert(3, charArray0, 1185, 1);
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
         //
         // offset 1185, len 1, str.length 5
         //
         verifyException("java.lang.AbstractStringBuilder", e);
      }
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertEquals("\n", helpFormatter0.getNewLine());
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(74, helpFormatter0.defaultWidth);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertNotNull(helpFormatter0);
      
      Options options0 = new Options();
      assertNotNull(options0);
      
      Option option0 = options0.getOption("-");
      assertNull(option0);
      
      HelpFormatter helpFormatter1 = new HelpFormatter();
      assertEquals("usage: ", helpFormatter1.getSyntaxPrefix());
      assertEquals("arg", helpFormatter1.getArgName());
      assertEquals(3, helpFormatter1.getDescPadding());
      assertEquals("\n", helpFormatter1.getNewLine());
      assertEquals(" ", helpFormatter1.getLongOptSeparator());
      assertEquals(1, helpFormatter1.getLeftPadding());
      assertEquals("-", helpFormatter1.getOptPrefix());
      assertEquals(74, helpFormatter1.getWidth());
      assertEquals("--", helpFormatter1.getLongOptPrefix());
      assertFalse(helpFormatter1.equals((Object)helpFormatter0));
      assertEquals(3, helpFormatter1.defaultDescPad);
      assertEquals(1, helpFormatter1.defaultLeftPad);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals(74, helpFormatter1.defaultWidth);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertNotNull(helpFormatter1);
      
      String string0 = "";
      // Undeclared exception!
      try { 
        helpFormatter0.printHelp(26, "", "", options0, "[");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // cmdLineSyntax not provided
         //
         verifyException("org.apache.commons.cli.HelpFormatter", e);
      }
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
    Future<?> future = executor.submit(new Runnable(){ 
            @Override public void run() { 
        try {
          HelpFormatter helpFormatter0 = new HelpFormatter();
          assertEquals("-", helpFormatter0.getOptPrefix());
          assertEquals(74, helpFormatter0.getWidth());
          assertEquals("--", helpFormatter0.getLongOptPrefix());
          assertEquals(3, helpFormatter0.getDescPadding());
          assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
          assertEquals(" ", helpFormatter0.getLongOptSeparator());
          assertEquals("arg", helpFormatter0.getArgName());
          assertEquals(1, helpFormatter0.getLeftPadding());
          assertEquals("\n", helpFormatter0.getNewLine());
          assertEquals(1, helpFormatter0.defaultLeftPad);
          assertEquals(74, helpFormatter0.defaultWidth);
          assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
          assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
          assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
          assertEquals(3, helpFormatter0.defaultDescPad);
          assertNotNull(helpFormatter0);
          
          String string0 = "[*<=[";
          ByteArrayOutputStream byteArrayOutputStream0 = new ByteArrayOutputStream(0);
          assertEquals("", byteArrayOutputStream0.toString());
          assertEquals(0, byteArrayOutputStream0.size());
          assertNotNull(byteArrayOutputStream0);
          
          MockPrintWriter mockPrintWriter0 = new MockPrintWriter(byteArrayOutputStream0);
          assertNotNull(mockPrintWriter0);
          
          Locale locale0 = Locale.FRENCH;
          assertEquals("fr", locale0.toString());
          assertEquals("fra", locale0.getISO3Language());
          assertEquals("", locale0.getCountry());
          assertEquals("", locale0.getISO3Country());
          assertEquals("", locale0.getVariant());
          assertEquals("fr", locale0.getLanguage());
          assertNotNull(locale0);
          
          PrintWriter printWriter0 = mockPrintWriter0.format(locale0, "usage: ", (Object[]) null);
          assertEquals("", byteArrayOutputStream0.toString());
          assertEquals(0, byteArrayOutputStream0.size());
          assertEquals("fr", locale0.toString());
          assertEquals("fra", locale0.getISO3Language());
          assertEquals("", locale0.getCountry());
          assertEquals("", locale0.getISO3Country());
          assertEquals("", locale0.getVariant());
          assertEquals("fr", locale0.getLanguage());
          assertSame(mockPrintWriter0, printWriter0);
          assertSame(printWriter0, mockPrintWriter0);
          assertNotNull(printWriter0);
          
          boolean boolean0 = FileSystemHandling.shouldThrowIOException((EvoSuiteFile) null);
          assertFalse(boolean0);
          
          helpFormatter0.printWrapped(printWriter0, 0, "");
          assertEquals("-", helpFormatter0.getOptPrefix());
          assertEquals(74, helpFormatter0.getWidth());
          assertEquals("--", helpFormatter0.getLongOptPrefix());
          assertEquals(3, helpFormatter0.getDescPadding());
          assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
          assertEquals(" ", helpFormatter0.getLongOptSeparator());
          assertEquals("arg", helpFormatter0.getArgName());
          assertEquals(1, helpFormatter0.getLeftPadding());
          assertEquals("\n", helpFormatter0.getNewLine());
          assertEquals("", byteArrayOutputStream0.toString());
          assertEquals(0, byteArrayOutputStream0.size());
          assertEquals("fr", locale0.toString());
          assertEquals("fra", locale0.getISO3Language());
          assertEquals("", locale0.getCountry());
          assertEquals("", locale0.getISO3Country());
          assertEquals("", locale0.getVariant());
          assertEquals("fr", locale0.getLanguage());
          assertSame(mockPrintWriter0, printWriter0);
          assertSame(printWriter0, mockPrintWriter0);
          assertEquals(1, helpFormatter0.defaultLeftPad);
          assertEquals(74, helpFormatter0.defaultWidth);
          assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
          assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
          assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
          assertEquals(3, helpFormatter0.defaultDescPad);
          
          String string1 = helpFormatter0.createPadding(0);
          assertEquals("", string1);
          assertEquals("-", helpFormatter0.getOptPrefix());
          assertEquals(74, helpFormatter0.getWidth());
          assertEquals("--", helpFormatter0.getLongOptPrefix());
          assertEquals(3, helpFormatter0.getDescPadding());
          assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
          assertEquals(" ", helpFormatter0.getLongOptSeparator());
          assertEquals("arg", helpFormatter0.getArgName());
          assertEquals(1, helpFormatter0.getLeftPadding());
          assertEquals("\n", helpFormatter0.getNewLine());
          assertFalse(string1.equals((Object)string0));
          assertEquals(1, helpFormatter0.defaultLeftPad);
          assertEquals(74, helpFormatter0.defaultWidth);
          assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
          assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
          assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
          assertEquals(3, helpFormatter0.defaultDescPad);
          assertNotNull(string1);
          
          Options options0 = new Options();
          assertNotNull(options0);
          
          String string2 = "<Sr3sm]{'";
          Option option0 = new Option("", "<Sr3sm]{'", false, "<Sr3sm]{'");
          assertEquals("", option0.getOpt());
          assertNull(option0.getValue());
          assertEquals("<Sr3sm]{'", option0.getLongOpt());
          assertFalse(option0.isRequired());
          assertFalse(option0.hasValueSeparator());
          assertFalse(option0.hasArgs());
          assertEquals((-1), option0.getArgs());
          assertFalse(option0.hasArgName());
          assertFalse(option0.hasArg());
          assertNull(option0.getArgName());
          assertEquals('\u0000', option0.getValueSeparator());
          assertTrue(option0.hasLongOpt());
          assertFalse(option0.hasOptionalArg());
          assertEquals("<Sr3sm]{'", option0.getDescription());
          assertEquals((-1), Option.UNINITIALIZED);
          assertEquals((-2), Option.UNLIMITED_VALUES);
          assertNotNull(option0);
          
          Options options1 = options0.addOption(option0);
          assertEquals("", option0.getOpt());
          assertNull(option0.getValue());
          assertEquals("<Sr3sm]{'", option0.getLongOpt());
          assertFalse(option0.isRequired());
          assertFalse(option0.hasValueSeparator());
          assertFalse(option0.hasArgs());
          assertEquals((-1), option0.getArgs());
          assertFalse(option0.hasArgName());
          assertFalse(option0.hasArg());
          assertNull(option0.getArgName());
          assertEquals('\u0000', option0.getValueSeparator());
          assertTrue(option0.hasLongOpt());
          assertFalse(option0.hasOptionalArg());
          assertEquals("<Sr3sm]{'", option0.getDescription());
          assertSame(options0, options1);
          assertSame(options1, options0);
          assertEquals((-1), Option.UNINITIALIZED);
          assertEquals((-2), Option.UNLIMITED_VALUES);
          assertNotNull(options1);
          
          helpFormatter0.printHelp("[*<=[", "[*<=[", options1, "<Sr3sm]{'", false);
          assertEquals("-", helpFormatter0.getOptPrefix());
          assertEquals(74, helpFormatter0.getWidth());
          assertEquals("--", helpFormatter0.getLongOptPrefix());
          assertEquals(3, helpFormatter0.getDescPadding());
          assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
          assertEquals(" ", helpFormatter0.getLongOptSeparator());
          assertEquals("arg", helpFormatter0.getArgName());
          assertEquals(1, helpFormatter0.getLeftPadding());
          assertEquals("\n", helpFormatter0.getNewLine());
          assertEquals("", option0.getOpt());
          assertNull(option0.getValue());
          assertEquals("<Sr3sm]{'", option0.getLongOpt());
          assertFalse(option0.isRequired());
          assertFalse(option0.hasValueSeparator());
          assertFalse(option0.hasArgs());
          assertEquals((-1), option0.getArgs());
          assertFalse(option0.hasArgName());
          assertFalse(option0.hasArg());
          assertNull(option0.getArgName());
          assertEquals('\u0000', option0.getValueSeparator());
          assertTrue(option0.hasLongOpt());
          assertFalse(option0.hasOptionalArg());
          assertEquals("<Sr3sm]{'", option0.getDescription());
          assertSame(options0, options1);
          assertSame(options1, options0);
          assertEquals(1, helpFormatter0.defaultLeftPad);
          assertEquals(74, helpFormatter0.defaultWidth);
          assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
          assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
          assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
          assertEquals(3, helpFormatter0.defaultDescPad);
          assertEquals((-1), Option.UNINITIALIZED);
          assertEquals((-2), Option.UNLIMITED_VALUES);
          
          helpFormatter0.setLeftPadding(1783);
          assertEquals("-", helpFormatter0.getOptPrefix());
          assertEquals(74, helpFormatter0.getWidth());
          assertEquals("--", helpFormatter0.getLongOptPrefix());
          assertEquals(3, helpFormatter0.getDescPadding());
          assertEquals(1783, helpFormatter0.getLeftPadding());
          assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
          assertEquals(" ", helpFormatter0.getLongOptSeparator());
          assertEquals("arg", helpFormatter0.getArgName());
          assertEquals("\n", helpFormatter0.getNewLine());
          assertEquals(1783, helpFormatter0.defaultLeftPad);
          assertEquals(74, helpFormatter0.defaultWidth);
          assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
          assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
          assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
          assertEquals(3, helpFormatter0.defaultDescPad);
          
          FileDescriptor fileDescriptor0 = new FileDescriptor();
          assertFalse(fileDescriptor0.valid());
          assertNotNull(fileDescriptor0);
          
          FileDescriptor fileDescriptor1 = new FileDescriptor();
          assertFalse(fileDescriptor1.valid());
          assertFalse(fileDescriptor1.equals((Object)fileDescriptor0));
          assertNotNull(fileDescriptor1);
          
          MockFileOutputStream mockFileOutputStream0 = null;
          try {
            mockFileOutputStream0 = new MockFileOutputStream(fileDescriptor1);
            fail("Expecting exception: SecurityException");
          
          } catch(SecurityException e) {
             //
             // Security manager blocks (\"java.lang.RuntimePermission\" \"writeFileDescriptor\")
             // java.lang.Thread.getStackTrace(Thread.java:1559)
             // org.evosuite.runtime.sandbox.MSecurityManager.checkPermission(MSecurityManager.java:424)
             // java.lang.SecurityManager.checkWrite(SecurityManager.java:954)
             // java.io.FileOutputStream.<init>(FileOutputStream.java:245)
             // org.evosuite.runtime.mock.java.io.MockFileOutputStream.<init>(MockFileOutputStream.java:114)
             // sun.reflect.GeneratedConstructorAccessor41.newInstance(Unknown Source)
             // sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
             // java.lang.reflect.Constructor.newInstance(Constructor.java:423)
             // org.evosuite.testcase.statements.ConstructorStatement$1.execute(ConstructorStatement.java:218)
             // org.evosuite.testcase.statements.AbstractStatement.exceptionHandler(AbstractStatement.java:165)
             // org.evosuite.testcase.statements.ConstructorStatement.execute(ConstructorStatement.java:173)
             // org.evosuite.testcase.execution.TestRunnable.executeStatements(TestRunnable.java:286)
             // org.evosuite.testcase.execution.TestRunnable.call(TestRunnable.java:192)
             // org.evosuite.testcase.execution.TestRunnable.call(TestRunnable.java:49)
             // java.util.concurrent.FutureTask.run(FutureTask.java:266)
             // java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
             // java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
             // java.lang.Thread.run(Thread.java:748)
             //
             verifyException("org.evosuite.runtime.sandbox.MSecurityManager", e);
          }
        } catch(Throwable t) {
            // Need to catch declared exceptions
        }
      } 
    });
    future.get(4000, TimeUnit.MILLISECONDS);
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
    Future<?> future = executor.submit(new Runnable(){ 
            @Override public void run() { 
        try {
          HelpFormatter helpFormatter0 = new HelpFormatter();
          assertEquals("-", helpFormatter0.getOptPrefix());
          assertEquals(74, helpFormatter0.getWidth());
          assertEquals("--", helpFormatter0.getLongOptPrefix());
          assertEquals(3, helpFormatter0.getDescPadding());
          assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
          assertEquals(" ", helpFormatter0.getLongOptSeparator());
          assertEquals("arg", helpFormatter0.getArgName());
          assertEquals(1, helpFormatter0.getLeftPadding());
          assertEquals("\n", helpFormatter0.getNewLine());
          assertEquals(1, helpFormatter0.defaultLeftPad);
          assertEquals(74, helpFormatter0.defaultWidth);
          assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
          assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
          assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
          assertEquals(3, helpFormatter0.defaultDescPad);
          assertNotNull(helpFormatter0);
          
          String string0 = "[*<=[";
          String string1 = helpFormatter0.createPadding(0);
          assertEquals("", string1);
          assertEquals("-", helpFormatter0.getOptPrefix());
          assertEquals(74, helpFormatter0.getWidth());
          assertEquals("--", helpFormatter0.getLongOptPrefix());
          assertEquals(3, helpFormatter0.getDescPadding());
          assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
          assertEquals(" ", helpFormatter0.getLongOptSeparator());
          assertEquals("arg", helpFormatter0.getArgName());
          assertEquals(1, helpFormatter0.getLeftPadding());
          assertEquals("\n", helpFormatter0.getNewLine());
          assertFalse(string1.equals((Object)string0));
          assertEquals(1, helpFormatter0.defaultLeftPad);
          assertEquals(74, helpFormatter0.defaultWidth);
          assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
          assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
          assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
          assertEquals(3, helpFormatter0.defaultDescPad);
          assertNotNull(string1);
          
          Options options0 = new Options();
          assertNotNull(options0);
          
          String string2 = "<Sr3sm]{'";
          Option option0 = new Option("", "<Sr3sm]{'", false, "<Sr3sm]{'");
          assertEquals("<Sr3sm]{'", option0.getDescription());
          assertTrue(option0.hasLongOpt());
          assertFalse(option0.hasArg());
          assertEquals("<Sr3sm]{'", option0.getLongOpt());
          assertNull(option0.getValue());
          assertFalse(option0.isRequired());
          assertEquals("", option0.getOpt());
          assertFalse(option0.hasArgs());
          assertFalse(option0.hasValueSeparator());
          assertEquals((-1), option0.getArgs());
          assertNull(option0.getArgName());
          assertFalse(option0.hasOptionalArg());
          assertEquals('\u0000', option0.getValueSeparator());
          assertFalse(option0.hasArgName());
          assertEquals((-2), Option.UNLIMITED_VALUES);
          assertEquals((-1), Option.UNINITIALIZED);
          assertNotNull(option0);
          
          Options options1 = options0.addOption(option0);
          assertEquals("<Sr3sm]{'", option0.getDescription());
          assertTrue(option0.hasLongOpt());
          assertFalse(option0.hasArg());
          assertEquals("<Sr3sm]{'", option0.getLongOpt());
          assertNull(option0.getValue());
          assertFalse(option0.isRequired());
          assertEquals("", option0.getOpt());
          assertFalse(option0.hasArgs());
          assertFalse(option0.hasValueSeparator());
          assertEquals((-1), option0.getArgs());
          assertNull(option0.getArgName());
          assertFalse(option0.hasOptionalArg());
          assertEquals('\u0000', option0.getValueSeparator());
          assertFalse(option0.hasArgName());
          assertSame(options0, options1);
          assertSame(options1, options0);
          assertEquals((-2), Option.UNLIMITED_VALUES);
          assertEquals((-1), Option.UNINITIALIZED);
          assertNotNull(options1);
          
          helpFormatter0.printHelp("[*<=[", "[*<=[", options1, "<Sr3sm]{'", false);
          assertEquals("-", helpFormatter0.getOptPrefix());
          assertEquals(74, helpFormatter0.getWidth());
          assertEquals("--", helpFormatter0.getLongOptPrefix());
          assertEquals(3, helpFormatter0.getDescPadding());
          assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
          assertEquals(" ", helpFormatter0.getLongOptSeparator());
          assertEquals("arg", helpFormatter0.getArgName());
          assertEquals(1, helpFormatter0.getLeftPadding());
          assertEquals("\n", helpFormatter0.getNewLine());
          assertEquals("<Sr3sm]{'", option0.getDescription());
          assertTrue(option0.hasLongOpt());
          assertFalse(option0.hasArg());
          assertEquals("<Sr3sm]{'", option0.getLongOpt());
          assertNull(option0.getValue());
          assertFalse(option0.isRequired());
          assertEquals("", option0.getOpt());
          assertFalse(option0.hasArgs());
          assertFalse(option0.hasValueSeparator());
          assertEquals((-1), option0.getArgs());
          assertNull(option0.getArgName());
          assertFalse(option0.hasOptionalArg());
          assertEquals('\u0000', option0.getValueSeparator());
          assertFalse(option0.hasArgName());
          assertSame(options0, options1);
          assertSame(options1, options0);
          assertEquals(1, helpFormatter0.defaultLeftPad);
          assertEquals(74, helpFormatter0.defaultWidth);
          assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
          assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
          assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
          assertEquals(3, helpFormatter0.defaultDescPad);
          assertEquals((-2), Option.UNLIMITED_VALUES);
          assertEquals((-1), Option.UNINITIALIZED);
          
          helpFormatter0.setLeftPadding(1783);
          assertEquals("-", helpFormatter0.getOptPrefix());
          assertEquals(74, helpFormatter0.getWidth());
          assertEquals("--", helpFormatter0.getLongOptPrefix());
          assertEquals(3, helpFormatter0.getDescPadding());
          assertEquals(1783, helpFormatter0.getLeftPadding());
          assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
          assertEquals(" ", helpFormatter0.getLongOptSeparator());
          assertEquals("arg", helpFormatter0.getArgName());
          assertEquals("\n", helpFormatter0.getNewLine());
          assertEquals(1783, helpFormatter0.defaultLeftPad);
          assertEquals(74, helpFormatter0.defaultWidth);
          assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
          assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
          assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
          assertEquals(3, helpFormatter0.defaultDescPad);
          
          FileDescriptor fileDescriptor0 = new FileDescriptor();
          assertFalse(fileDescriptor0.valid());
          assertNotNull(fileDescriptor0);
          
          FileDescriptor fileDescriptor1 = new FileDescriptor();
          assertFalse(fileDescriptor1.valid());
          assertFalse(fileDescriptor1.equals((Object)fileDescriptor0));
          assertNotNull(fileDescriptor1);
          
          MockFileOutputStream mockFileOutputStream0 = null;
          try {
            mockFileOutputStream0 = new MockFileOutputStream(fileDescriptor1);
            fail("Expecting exception: SecurityException");
          
          } catch(SecurityException e) {
             //
             // Security manager blocks (\"java.lang.RuntimePermission\" \"writeFileDescriptor\")
             // java.lang.Thread.getStackTrace(Thread.java:1559)
             // org.evosuite.runtime.sandbox.MSecurityManager.checkPermission(MSecurityManager.java:424)
             // java.lang.SecurityManager.checkWrite(SecurityManager.java:954)
             // java.io.FileOutputStream.<init>(FileOutputStream.java:245)
             // org.evosuite.runtime.mock.java.io.MockFileOutputStream.<init>(MockFileOutputStream.java:114)
             // sun.reflect.GeneratedConstructorAccessor41.newInstance(Unknown Source)
             // sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
             // java.lang.reflect.Constructor.newInstance(Constructor.java:423)
             // org.evosuite.testcase.statements.ConstructorStatement$1.execute(ConstructorStatement.java:218)
             // org.evosuite.testcase.statements.AbstractStatement.exceptionHandler(AbstractStatement.java:165)
             // org.evosuite.testcase.statements.ConstructorStatement.execute(ConstructorStatement.java:173)
             // org.evosuite.testcase.execution.TestRunnable.executeStatements(TestRunnable.java:286)
             // org.evosuite.testcase.execution.TestRunnable.call(TestRunnable.java:192)
             // org.evosuite.testcase.execution.TestRunnable.call(TestRunnable.java:49)
             // java.util.concurrent.FutureTask.run(FutureTask.java:266)
             // java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
             // java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
             // java.lang.Thread.run(Thread.java:748)
             //
             verifyException("org.evosuite.runtime.sandbox.MSecurityManager", e);
          }
        } catch(Throwable t) {
            // Need to catch declared exceptions
        }
      } 
    });
    future.get(4000, TimeUnit.MILLISECONDS);
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals("\n", helpFormatter0.getNewLine());
      assertEquals(74, helpFormatter0.defaultWidth);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertNotNull(helpFormatter0);
      
      File file0 = MockFile.createTempFile("arg", "");
      assertEquals(0L, file0.getUsableSpace());
      assertEquals("arg0", file0.getName());
      assertEquals(1392409281320L, file0.lastModified());
      assertEquals("/var/folders/sl/3mfrgmdj2n1cl2km06yxm9jh0000gn/T/arg0", file0.toString());
      assertTrue(file0.canRead());
      assertTrue(file0.exists());
      assertTrue(file0.isFile());
      assertEquals("/var/folders/sl/3mfrgmdj2n1cl2km06yxm9jh0000gn/T", file0.getParent());
      assertEquals(0L, file0.getTotalSpace());
      assertTrue(file0.canExecute());
      assertTrue(file0.canWrite());
      assertEquals(0L, file0.length());
      assertFalse(file0.isHidden());
      assertTrue(file0.isAbsolute());
      assertEquals(0L, file0.getFreeSpace());
      assertFalse(file0.isDirectory());
      assertNotNull(file0);
      
      MockPrintWriter mockPrintWriter0 = new MockPrintWriter(file0);
      assertNotNull(mockPrintWriter0);
      
      Object[] objectArray0 = new Object[5];
      objectArray0[0] = (Object) "C";
      objectArray0[1] = (Object) mockPrintWriter0;
      objectArray0[2] = (Object) helpFormatter0;
      objectArray0[3] = (Object) "C";
      objectArray0[4] = (Object) "";
      PrintWriter printWriter0 = mockPrintWriter0.printf("C", objectArray0);
      assertEquals(0L, file0.getUsableSpace());
      assertEquals("arg0", file0.getName());
      assertEquals(1392409281320L, file0.lastModified());
      assertEquals("/var/folders/sl/3mfrgmdj2n1cl2km06yxm9jh0000gn/T/arg0", file0.toString());
      assertTrue(file0.canRead());
      assertTrue(file0.exists());
      assertTrue(file0.isFile());
      assertEquals("/var/folders/sl/3mfrgmdj2n1cl2km06yxm9jh0000gn/T", file0.getParent());
      assertEquals(0L, file0.getTotalSpace());
      assertTrue(file0.canExecute());
      assertTrue(file0.canWrite());
      assertEquals(0L, file0.length());
      assertFalse(file0.isHidden());
      assertTrue(file0.isAbsolute());
      assertEquals(0L, file0.getFreeSpace());
      assertFalse(file0.isDirectory());
      assertSame(mockPrintWriter0, printWriter0);
      assertSame(printWriter0, mockPrintWriter0);
      assertEquals(5, objectArray0.length);
      assertNotNull(printWriter0);
      
      // Undeclared exception!
      try { 
        helpFormatter0.printWrapped(printWriter0, (-40), (-9), "05FOFo2__4HgP2J?93C");
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
      }
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertEquals("\n", helpFormatter0.getNewLine());
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals(74, helpFormatter0.defaultWidth);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertNotNull(helpFormatter0);
      
      helpFormatter0.setNewLine("Cc]e");
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals("Cc]e", helpFormatter0.getNewLine());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals(74, helpFormatter0.defaultWidth);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      
      helpFormatter0.setWidth((-1130));
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals("Cc]e", helpFormatter0.getNewLine());
      assertEquals((-1130), helpFormatter0.getWidth());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertEquals((-1130), helpFormatter0.defaultWidth);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      
      String string0 = helpFormatter0.getLongOptPrefix();
      assertEquals("--", string0);
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals("Cc]e", helpFormatter0.getNewLine());
      assertEquals((-1130), helpFormatter0.getWidth());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertEquals((-1130), helpFormatter0.defaultWidth);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertNotNull(string0);
      
      HelpFormatter helpFormatter1 = new HelpFormatter();
      assertEquals("arg", helpFormatter1.getArgName());
      assertEquals(74, helpFormatter1.getWidth());
      assertEquals(3, helpFormatter1.getDescPadding());
      assertEquals("--", helpFormatter1.getLongOptPrefix());
      assertEquals("usage: ", helpFormatter1.getSyntaxPrefix());
      assertEquals(1, helpFormatter1.getLeftPadding());
      assertEquals(" ", helpFormatter1.getLongOptSeparator());
      assertEquals("-", helpFormatter1.getOptPrefix());
      assertEquals("\n", helpFormatter1.getNewLine());
      assertFalse(helpFormatter1.equals((Object)helpFormatter0));
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(74, helpFormatter1.defaultWidth);
      assertEquals(1, helpFormatter1.defaultLeftPad);
      assertEquals(3, helpFormatter1.defaultDescPad);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertNotNull(helpFormatter1);
      
      Comparator<Option> comparator0 = helpFormatter0.getOptionComparator();
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals("Cc]e", helpFormatter0.getNewLine());
      assertEquals((-1130), helpFormatter0.getWidth());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertFalse(helpFormatter0.equals((Object)helpFormatter1));
      assertNotSame(helpFormatter0, helpFormatter1);
      assertEquals((-1130), helpFormatter0.defaultWidth);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertNotNull(comparator0);
      
      StringBuffer stringBuffer0 = null;
      ByteArrayOutputStream byteArrayOutputStream0 = new ByteArrayOutputStream();
      assertEquals("", byteArrayOutputStream0.toString());
      assertEquals(0, byteArrayOutputStream0.size());
      assertNotNull(byteArrayOutputStream0);
      
      MockPrintStream mockPrintStream0 = new MockPrintStream(byteArrayOutputStream0);
      assertNotNull(mockPrintStream0);
      
      ObjectOutputStream objectOutputStream0 = new ObjectOutputStream(mockPrintStream0);
      assertEquals("\uFFFD\uFFFD\u0000\u0005", byteArrayOutputStream0.toString());
      assertEquals(4, byteArrayOutputStream0.size());
      assertNotNull(objectOutputStream0);
      
      MockPrintWriter mockPrintWriter0 = new MockPrintWriter(objectOutputStream0, true);
      assertNotNull(mockPrintWriter0);
      
      Options options0 = new Options();
      assertNotNull(options0);
      
      Options options1 = options0.addRequiredOption("arg", "\b$y+)']mrM<Xq$+b", true, "Cc]e");
      assertSame(options0, options1);
      assertSame(options1, options0);
      assertNotNull(options1);
      
      Option option0 = new Option("3MR", "arg", false, "");
      assertFalse(option0.hasArg());
      assertEquals(51, option0.getId());
      assertNull(option0.getValue());
      assertFalse(option0.isRequired());
      assertEquals("3MR", option0.getOpt());
      assertEquals("arg", option0.getLongOpt());
      assertFalse(option0.hasValueSeparator());
      assertFalse(option0.hasArgs());
      assertNull(option0.getArgName());
      assertEquals((-1), option0.getArgs());
      assertEquals('\u0000', option0.getValueSeparator());
      assertEquals("", option0.getDescription());
      assertFalse(option0.hasArgName());
      assertFalse(option0.hasOptionalArg());
      assertTrue(option0.hasLongOpt());
      assertEquals((-2), Option.UNLIMITED_VALUES);
      assertEquals((-1), Option.UNINITIALIZED);
      assertNotNull(option0);
      
      Options options2 = options1.addOption(option0);
      assertFalse(option0.hasArg());
      assertEquals(51, option0.getId());
      assertNull(option0.getValue());
      assertFalse(option0.isRequired());
      assertEquals("3MR", option0.getOpt());
      assertEquals("arg", option0.getLongOpt());
      assertFalse(option0.hasValueSeparator());
      assertFalse(option0.hasArgs());
      assertNull(option0.getArgName());
      assertEquals((-1), option0.getArgs());
      assertEquals('\u0000', option0.getValueSeparator());
      assertEquals("", option0.getDescription());
      assertFalse(option0.hasArgName());
      assertFalse(option0.hasOptionalArg());
      assertTrue(option0.hasLongOpt());
      assertSame(options0, options1);
      assertSame(options0, options2);
      assertSame(options1, options2);
      assertSame(options1, options0);
      assertSame(options2, options1);
      assertSame(options2, options0);
      assertEquals((-2), Option.UNLIMITED_VALUES);
      assertEquals((-1), Option.UNINITIALIZED);
      assertNotNull(options2);
      
      OptionGroup optionGroup0 = new OptionGroup();
      assertNull(optionGroup0.getSelected());
      assertFalse(optionGroup0.isRequired());
      assertNotNull(optionGroup0);
      
      OptionGroup optionGroup1 = optionGroup0.addOption(option0);
      assertNull(optionGroup1.getSelected());
      assertFalse(optionGroup1.isRequired());
      assertFalse(option0.hasArg());
      assertEquals(51, option0.getId());
      assertNull(option0.getValue());
      assertFalse(option0.isRequired());
      assertEquals("3MR", option0.getOpt());
      assertEquals("arg", option0.getLongOpt());
      assertFalse(option0.hasValueSeparator());
      assertFalse(option0.hasArgs());
      assertNull(option0.getArgName());
      assertEquals((-1), option0.getArgs());
      assertEquals('\u0000', option0.getValueSeparator());
      assertEquals("", option0.getDescription());
      assertFalse(option0.hasArgName());
      assertFalse(option0.hasOptionalArg());
      assertTrue(option0.hasLongOpt());
      assertNull(optionGroup0.getSelected());
      assertFalse(optionGroup0.isRequired());
      assertSame(optionGroup1, optionGroup0);
      assertSame(optionGroup0, optionGroup1);
      assertEquals((-2), Option.UNLIMITED_VALUES);
      assertEquals((-1), Option.UNINITIALIZED);
      assertNotNull(optionGroup1);
      
      Options options3 = options2.addOptionGroup(optionGroup1);
      assertNull(optionGroup1.getSelected());
      assertFalse(optionGroup1.isRequired());
      assertFalse(option0.hasArg());
      assertEquals(51, option0.getId());
      assertNull(option0.getValue());
      assertFalse(option0.isRequired());
      assertEquals("3MR", option0.getOpt());
      assertEquals("arg", option0.getLongOpt());
      assertFalse(option0.hasValueSeparator());
      assertFalse(option0.hasArgs());
      assertNull(option0.getArgName());
      assertEquals((-1), option0.getArgs());
      assertEquals('\u0000', option0.getValueSeparator());
      assertEquals("", option0.getDescription());
      assertFalse(option0.hasArgName());
      assertFalse(option0.hasOptionalArg());
      assertTrue(option0.hasLongOpt());
      assertNull(optionGroup0.getSelected());
      assertFalse(optionGroup0.isRequired());
      assertSame(optionGroup1, optionGroup0);
      assertSame(options3, options2);
      assertSame(options3, options0);
      assertSame(options3, options1);
      assertSame(options0, options1);
      assertSame(options0, options2);
      assertSame(options0, options3);
      assertSame(options1, options3);
      assertSame(options1, options2);
      assertSame(options1, options0);
      assertSame(options2, options3);
      assertSame(options2, options1);
      assertSame(options2, options0);
      assertSame(optionGroup0, optionGroup1);
      assertEquals((-2), Option.UNLIMITED_VALUES);
      assertEquals((-1), Option.UNINITIALIZED);
      assertNotNull(options3);
      
      // Undeclared exception!
      try { 
        helpFormatter1.printUsage((PrintWriter) mockPrintWriter0, (-334), "", options3);
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
      }
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals("\n", helpFormatter0.getNewLine());
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertEquals(74, helpFormatter0.defaultWidth);
      assertNotNull(helpFormatter0);
      
      String string0 = "[*<=[";
      String string1 = helpFormatter0.getSyntaxPrefix();
      assertEquals("usage: ", string1);
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals("\n", helpFormatter0.getNewLine());
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertFalse(string1.equals((Object)string0));
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertEquals(74, helpFormatter0.defaultWidth);
      assertNotNull(string1);
      
      int int0 = 1322;
      String string2 = helpFormatter0.createPadding(18);
      assertEquals("                  ", string2);
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals("\n", helpFormatter0.getNewLine());
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertFalse(string2.equals((Object)string1));
      assertFalse(string2.equals((Object)string0));
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertEquals(74, helpFormatter0.defaultWidth);
      assertNotNull(string2);
      
      // Undeclared exception!
      try { 
        helpFormatter0.printHelp(1322, ">,gCQm'?p~@Zq`n", ">,gCQm'?p~@Zq`n", (Options) null, "[*<=[");
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.cli.HelpFormatter", e);
      }
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals("\n", helpFormatter0.getNewLine());
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(74, helpFormatter0.defaultWidth);
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertNotNull(helpFormatter0);
      
      StringWriter stringWriter0 = new StringWriter(3);
      assertEquals("", stringWriter0.toString());
      assertNotNull(stringWriter0);
      
      StringWriter stringWriter1 = stringWriter0.append((CharSequence) " ");
      assertEquals(" ", stringWriter0.toString());
      assertEquals(" ", stringWriter1.toString());
      assertSame(stringWriter0, stringWriter1);
      assertSame(stringWriter1, stringWriter0);
      assertNotNull(stringWriter1);
      
      StringBuffer stringBuffer0 = stringWriter1.getBuffer();
      assertEquals(" ", stringWriter0.toString());
      assertEquals(" ", stringWriter1.toString());
      assertEquals(1, stringBuffer0.length());
      assertEquals(" ", stringBuffer0.toString());
      assertSame(stringWriter0, stringWriter1);
      assertSame(stringWriter1, stringWriter0);
      assertNotNull(stringBuffer0);
      
      StringBuffer stringBuffer1 = stringBuffer0.append((CharSequence) "usage: ");
      assertEquals(" usage: ", stringWriter0.toString());
      assertEquals(" usage: ", stringWriter1.toString());
      assertEquals(" usage: ", stringBuffer0.toString());
      assertEquals(8, stringBuffer0.length());
      assertEquals(" usage: ", stringBuffer1.toString());
      assertEquals(8, stringBuffer1.length());
      assertSame(stringWriter0, stringWriter1);
      assertSame(stringWriter1, stringWriter0);
      assertSame(stringBuffer0, stringBuffer1);
      assertSame(stringBuffer1, stringBuffer0);
      assertNotNull(stringBuffer1);
      
      StringBuffer stringBuffer2 = helpFormatter0.renderWrappedText(stringBuffer0, 1652, 1, " ");
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals("\n", helpFormatter0.getNewLine());
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals(" usage: ", stringWriter0.toString());
      assertEquals(" usage: ", stringWriter1.toString());
      assertEquals(" usage: ", stringBuffer0.toString());
      assertEquals(8, stringBuffer0.length());
      assertEquals(" usage: ", stringBuffer2.toString());
      assertEquals(8, stringBuffer2.length());
      assertSame(stringWriter0, stringWriter1);
      assertSame(stringWriter1, stringWriter0);
      assertSame(stringBuffer0, stringBuffer1);
      assertSame(stringBuffer0, stringBuffer2);
      assertSame(stringBuffer2, stringBuffer1);
      assertSame(stringBuffer2, stringBuffer0);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(74, helpFormatter0.defaultWidth);
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertNotNull(stringBuffer2);
      
      HelpFormatter helpFormatter1 = new HelpFormatter();
      assertEquals(74, helpFormatter1.getWidth());
      assertEquals("arg", helpFormatter1.getArgName());
      assertEquals(3, helpFormatter1.getDescPadding());
      assertEquals("--", helpFormatter1.getLongOptPrefix());
      assertEquals("usage: ", helpFormatter1.getSyntaxPrefix());
      assertEquals(1, helpFormatter1.getLeftPadding());
      assertEquals(" ", helpFormatter1.getLongOptSeparator());
      assertEquals("-", helpFormatter1.getOptPrefix());
      assertEquals("\n", helpFormatter1.getNewLine());
      assertFalse(helpFormatter1.equals((Object)helpFormatter0));
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals(74, helpFormatter1.defaultWidth);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(1, helpFormatter1.defaultLeftPad);
      assertEquals(3, helpFormatter1.defaultDescPad);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertNotNull(helpFormatter1);
      
      String string0 = helpFormatter1.rtrim("usage: ");
      assertEquals("usage:", string0);
      assertEquals(74, helpFormatter1.getWidth());
      assertEquals("arg", helpFormatter1.getArgName());
      assertEquals(3, helpFormatter1.getDescPadding());
      assertEquals("--", helpFormatter1.getLongOptPrefix());
      assertEquals("usage: ", helpFormatter1.getSyntaxPrefix());
      assertEquals(1, helpFormatter1.getLeftPadding());
      assertEquals(" ", helpFormatter1.getLongOptSeparator());
      assertEquals("-", helpFormatter1.getOptPrefix());
      assertEquals("\n", helpFormatter1.getNewLine());
      assertFalse(helpFormatter1.equals((Object)helpFormatter0));
      assertNotSame(helpFormatter1, helpFormatter0);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals(74, helpFormatter1.defaultWidth);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(1, helpFormatter1.defaultLeftPad);
      assertEquals(3, helpFormatter1.defaultDescPad);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertNotNull(string0);
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals("\n", helpFormatter0.getNewLine());
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(74, helpFormatter0.defaultWidth);
      assertNotNull(helpFormatter0);
      
      helpFormatter0.setNewLine("Cc]e");
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals("Cc]e", helpFormatter0.getNewLine());
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(74, helpFormatter0.defaultWidth);
      
      helpFormatter0.setWidth((-1130));
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals((-1130), helpFormatter0.getWidth());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals("Cc]e", helpFormatter0.getNewLine());
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals((-1130), helpFormatter0.defaultWidth);
      
      String string0 = helpFormatter0.getLongOptPrefix();
      assertEquals("--", string0);
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals((-1130), helpFormatter0.getWidth());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals("Cc]e", helpFormatter0.getNewLine());
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals((-1130), helpFormatter0.defaultWidth);
      assertNotNull(string0);
      
      HelpFormatter helpFormatter1 = new HelpFormatter();
      assertEquals("usage: ", helpFormatter1.getSyntaxPrefix());
      assertEquals(" ", helpFormatter1.getLongOptSeparator());
      assertEquals("arg", helpFormatter1.getArgName());
      assertEquals(1, helpFormatter1.getLeftPadding());
      assertEquals("\n", helpFormatter1.getNewLine());
      assertEquals("-", helpFormatter1.getOptPrefix());
      assertEquals(74, helpFormatter1.getWidth());
      assertEquals("--", helpFormatter1.getLongOptPrefix());
      assertEquals(3, helpFormatter1.getDescPadding());
      assertFalse(helpFormatter1.equals((Object)helpFormatter0));
      assertEquals(1, helpFormatter1.defaultLeftPad);
      assertEquals(74, helpFormatter1.defaultWidth);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertEquals(3, helpFormatter1.defaultDescPad);
      assertNotNull(helpFormatter1);
      
      MockPrintStream mockPrintStream0 = new MockPrintStream("arg");
      assertNotNull(mockPrintStream0);
      
      PrintStream printStream0 = mockPrintStream0.printf((Locale) null, "usage: ", (Object[]) null);
      assertSame(mockPrintStream0, printStream0);
      assertSame(printStream0, mockPrintStream0);
      assertNotNull(printStream0);
      
      DataOutputStream dataOutputStream0 = new DataOutputStream(printStream0);
      assertNotNull(dataOutputStream0);
      
      MockPrintWriter mockPrintWriter0 = new MockPrintWriter(dataOutputStream0, false);
      assertNotNull(mockPrintWriter0);
      
      Object object0 = new Object();
      assertNotNull(object0);
      
      mockPrintWriter0.println((Object) mockPrintStream0);
      assertSame(mockPrintStream0, printStream0);
      assertSame(printStream0, mockPrintStream0);
      
      PrintWriter printWriter0 = mockPrintWriter0.printf("-", (Object[]) null);
      assertSame(mockPrintStream0, printStream0);
      assertSame(printStream0, mockPrintStream0);
      assertSame(mockPrintWriter0, printWriter0);
      assertSame(printWriter0, mockPrintWriter0);
      assertNotNull(printWriter0);
      
      // Undeclared exception!
      try { 
        helpFormatter0.printWrapped((PrintWriter) mockPrintWriter0, 1, (-14), "--");
        fail("Expecting exception: NegativeArraySizeException");
      
      } catch(NegativeArraySizeException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.cli.HelpFormatter", e);
      }
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      assertEquals("\n", helpFormatter0.getNewLine());
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertEquals(74, helpFormatter0.defaultWidth);
      assertNotNull(helpFormatter0);
      
      String string0 = helpFormatter0.createPadding(64);
      assertEquals("                                                                ", string0);
      assertEquals("\n", helpFormatter0.getNewLine());
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertEquals(74, helpFormatter0.defaultWidth);
      assertNotNull(string0);
      
      String string1 = helpFormatter0.createPadding(1322);
      assertEquals("\n", helpFormatter0.getNewLine());
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertFalse(string1.equals((Object)string0));
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertEquals(74, helpFormatter0.defaultWidth);
      assertNotNull(string1);
      
      HelpFormatter helpFormatter1 = new HelpFormatter();
      assertEquals("\n", helpFormatter1.getNewLine());
      assertEquals("arg", helpFormatter1.getArgName());
      assertEquals(74, helpFormatter1.getWidth());
      assertEquals(3, helpFormatter1.getDescPadding());
      assertEquals(" ", helpFormatter1.getLongOptSeparator());
      assertEquals(1, helpFormatter1.getLeftPadding());
      assertEquals("-", helpFormatter1.getOptPrefix());
      assertEquals("--", helpFormatter1.getLongOptPrefix());
      assertEquals("usage: ", helpFormatter1.getSyntaxPrefix());
      assertFalse(helpFormatter1.equals((Object)helpFormatter0));
      assertEquals(3, helpFormatter1.defaultDescPad);
      assertEquals(1, helpFormatter1.defaultLeftPad);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(74, helpFormatter1.defaultWidth);
      assertNotNull(helpFormatter1);
      
      // Undeclared exception!
      helpFormatter1.printHelp(0, "LM:U2", "", (Options) null, "                                                                ");
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals("\n", helpFormatter0.getNewLine());
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals(74, helpFormatter0.defaultWidth);
      assertNotNull(helpFormatter0);
      
      StringWriter stringWriter0 = new StringWriter(3);
      assertEquals("", stringWriter0.toString());
      assertNotNull(stringWriter0);
      
      boolean boolean0 = FileSystemHandling.setPermissions((EvoSuiteFile) null, true, false, false);
      assertFalse(boolean0);
      
      StringWriter stringWriter1 = stringWriter0.append((CharSequence) " ");
      assertEquals(" ", stringWriter0.toString());
      assertEquals(" ", stringWriter1.toString());
      assertSame(stringWriter0, stringWriter1);
      assertSame(stringWriter1, stringWriter0);
      assertNotNull(stringWriter1);
      
      StringBuffer stringBuffer0 = stringWriter1.getBuffer();
      assertEquals(" ", stringWriter0.toString());
      assertEquals(" ", stringWriter1.toString());
      assertEquals(1, stringBuffer0.length());
      assertEquals(" ", stringBuffer0.toString());
      assertSame(stringWriter0, stringWriter1);
      assertSame(stringWriter1, stringWriter0);
      assertNotNull(stringBuffer0);
      
      StringBuffer stringBuffer1 = stringBuffer0.append((CharSequence) "usage: ");
      assertEquals(" usage: ", stringWriter0.toString());
      assertEquals(" usage: ", stringWriter1.toString());
      assertEquals(" usage: ", stringBuffer0.toString());
      assertEquals(8, stringBuffer0.length());
      assertEquals(" usage: ", stringBuffer1.toString());
      assertEquals(8, stringBuffer1.length());
      assertSame(stringWriter0, stringWriter1);
      assertSame(stringWriter1, stringWriter0);
      assertSame(stringBuffer0, stringBuffer1);
      assertSame(stringBuffer1, stringBuffer0);
      assertNotNull(stringBuffer1);
      
      HelpFormatter helpFormatter1 = new HelpFormatter();
      assertEquals("arg", helpFormatter1.getArgName());
      assertEquals(3, helpFormatter1.getDescPadding());
      assertEquals(74, helpFormatter1.getWidth());
      assertEquals("\n", helpFormatter1.getNewLine());
      assertEquals("usage: ", helpFormatter1.getSyntaxPrefix());
      assertEquals("--", helpFormatter1.getLongOptPrefix());
      assertEquals(1, helpFormatter1.getLeftPadding());
      assertEquals("-", helpFormatter1.getOptPrefix());
      assertEquals(" ", helpFormatter1.getLongOptSeparator());
      assertFalse(helpFormatter1.equals((Object)helpFormatter0));
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals(3, helpFormatter1.defaultDescPad);
      assertEquals(1, helpFormatter1.defaultLeftPad);
      assertEquals(74, helpFormatter1.defaultWidth);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertNotNull(helpFormatter1);
      
      StringBuffer stringBuffer2 = new StringBuffer(stringBuffer1);
      assertEquals(" usage: ", stringWriter0.toString());
      assertEquals(" usage: ", stringWriter1.toString());
      assertEquals(" usage: ", stringBuffer0.toString());
      assertEquals(8, stringBuffer0.length());
      assertEquals(" usage: ", stringBuffer1.toString());
      assertEquals(8, stringBuffer1.length());
      assertEquals(" usage: ", stringBuffer2.toString());
      assertEquals(8, stringBuffer2.length());
      assertFalse(stringBuffer2.equals((Object)stringBuffer1));
      assertFalse(stringBuffer2.equals((Object)stringBuffer0));
      assertNotNull(stringBuffer2);
      
      // Undeclared exception!
      helpFormatter1.renderWrappedText(stringBuffer2, 0, 1, "-");
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      boolean boolean0 = FileSystemHandling.shouldAllThrowIOExceptions();
      assertTrue(boolean0);
      
      boolean boolean1 = FileSystemHandling.createFolder((EvoSuiteFile) null);
      assertFalse(boolean1);
      assertFalse(boolean1 == boolean0);
      
      HelpFormatter helpFormatter0 = new HelpFormatter();
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals("\n", helpFormatter0.getNewLine());
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(74, helpFormatter0.defaultWidth);
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertNotNull(helpFormatter0);
      
      String string0 = helpFormatter0.getSyntaxPrefix();
      assertEquals("usage: ", string0);
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals("\n", helpFormatter0.getNewLine());
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(74, helpFormatter0.defaultWidth);
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertNotNull(string0);
      
      int int0 = helpFormatter0.getDescPadding();
      assertEquals(3, int0);
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals("\n", helpFormatter0.getNewLine());
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(74, helpFormatter0.defaultWidth);
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      
      String string1 = helpFormatter0.getArgName();
      assertEquals("arg", string1);
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals("\n", helpFormatter0.getNewLine());
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertFalse(string1.equals((Object)string0));
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(74, helpFormatter0.defaultWidth);
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertNotNull(string1);
      
      ByteArrayOutputStream byteArrayOutputStream0 = new ByteArrayOutputStream();
      assertEquals("", byteArrayOutputStream0.toString());
      assertEquals(0, byteArrayOutputStream0.size());
      assertNotNull(byteArrayOutputStream0);
      
      MockPrintWriter mockPrintWriter0 = new MockPrintWriter(byteArrayOutputStream0);
      assertNotNull(mockPrintWriter0);
      
      String string2 = null;
      Options options0 = new Options();
      assertNotNull(options0);
      
      // Undeclared exception!
      try { 
        helpFormatter0.printHelp((PrintWriter) mockPrintWriter0, 3, "arg", (String) null, options0, 74, (-3081), "usage: ");
        fail("Expecting exception: NegativeArraySizeException");
      
      } catch(NegativeArraySizeException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.cli.HelpFormatter", e);
      }
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      FileSystemHandling fileSystemHandling0 = new FileSystemHandling();
      assertNotNull(fileSystemHandling0);
      
      HelpFormatter helpFormatter0 = new HelpFormatter();
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals("\n", helpFormatter0.getNewLine());
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(74, helpFormatter0.defaultWidth);
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertNotNull(helpFormatter0);
      
      MockPrintWriter mockPrintWriter0 = new MockPrintWriter("-");
      assertNotNull(mockPrintWriter0);
      
      MockPrintWriter mockPrintWriter1 = new MockPrintWriter(mockPrintWriter0, false);
      assertFalse(mockPrintWriter1.equals((Object)mockPrintWriter0));
      assertNotNull(mockPrintWriter1);
      
      mockPrintWriter1.println((float) 1);
      assertFalse(mockPrintWriter0.equals((Object)mockPrintWriter1));
      assertFalse(mockPrintWriter1.equals((Object)mockPrintWriter0));
      assertNotSame(mockPrintWriter0, mockPrintWriter1);
      assertNotSame(mockPrintWriter1, mockPrintWriter0);
      
      Locale locale0 = Locale.SIMPLIFIED_CHINESE;
      assertEquals("", locale0.getVariant());
      assertEquals("CHN", locale0.getISO3Country());
      assertEquals("zho", locale0.getISO3Language());
      assertEquals("zh_CN", locale0.toString());
      assertEquals("CN", locale0.getCountry());
      assertEquals("zh", locale0.getLanguage());
      assertNotNull(locale0);
      
      PrintWriter printWriter0 = mockPrintWriter1.printf(locale0, " ", (Object[]) null);
      assertEquals("", locale0.getVariant());
      assertEquals("CHN", locale0.getISO3Country());
      assertEquals("zho", locale0.getISO3Language());
      assertEquals("zh_CN", locale0.toString());
      assertEquals("CN", locale0.getCountry());
      assertEquals("zh", locale0.getLanguage());
      assertFalse(mockPrintWriter0.equals((Object)mockPrintWriter1));
      assertFalse(mockPrintWriter1.equals((Object)mockPrintWriter0));
      assertFalse(printWriter0.equals((Object)mockPrintWriter0));
      assertNotSame(mockPrintWriter0, printWriter0);
      assertNotSame(mockPrintWriter0, mockPrintWriter1);
      assertSame(mockPrintWriter1, printWriter0);
      assertNotSame(mockPrintWriter1, mockPrintWriter0);
      assertSame(printWriter0, mockPrintWriter1);
      assertNotSame(printWriter0, mockPrintWriter0);
      assertNotNull(printWriter0);
      
      mockPrintWriter1.print(74);
      assertFalse(mockPrintWriter0.equals((Object)printWriter0));
      assertFalse(mockPrintWriter0.equals((Object)mockPrintWriter1));
      assertFalse(mockPrintWriter1.equals((Object)mockPrintWriter0));
      assertNotSame(mockPrintWriter0, printWriter0);
      assertNotSame(mockPrintWriter0, mockPrintWriter1);
      assertSame(mockPrintWriter1, printWriter0);
      assertNotSame(mockPrintWriter1, mockPrintWriter0);
      
      helpFormatter0.printUsage(printWriter0, 535, "yJAsG$]`s2t");
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals("\n", helpFormatter0.getNewLine());
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals("", locale0.getVariant());
      assertEquals("CHN", locale0.getISO3Country());
      assertEquals("zho", locale0.getISO3Language());
      assertEquals("zh_CN", locale0.toString());
      assertEquals("CN", locale0.getCountry());
      assertEquals("zh", locale0.getLanguage());
      assertFalse(mockPrintWriter0.equals((Object)printWriter0));
      assertFalse(mockPrintWriter0.equals((Object)mockPrintWriter1));
      assertFalse(mockPrintWriter1.equals((Object)mockPrintWriter0));
      assertFalse(printWriter0.equals((Object)mockPrintWriter0));
      assertNotSame(mockPrintWriter0, printWriter0);
      assertNotSame(mockPrintWriter0, mockPrintWriter1);
      assertSame(mockPrintWriter1, printWriter0);
      assertNotSame(mockPrintWriter1, mockPrintWriter0);
      assertSame(printWriter0, mockPrintWriter1);
      assertNotSame(printWriter0, mockPrintWriter0);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(74, helpFormatter0.defaultWidth);
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      
      // Undeclared exception!
      try { 
        helpFormatter0.findWrapPos((String) null, (-492), (-492));
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.cli.HelpFormatter", e);
      }
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      FileSystemHandling fileSystemHandling0 = new FileSystemHandling();
      assertNotNull(fileSystemHandling0);
      
      HelpFormatter helpFormatter0 = new HelpFormatter();
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals("\n", helpFormatter0.getNewLine());
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals(74, helpFormatter0.defaultWidth);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertNotNull(helpFormatter0);
      
      MockPrintWriter mockPrintWriter0 = new MockPrintWriter("-");
      assertNotNull(mockPrintWriter0);
      
      MockPrintWriter mockPrintWriter1 = new MockPrintWriter(mockPrintWriter0, false);
      assertFalse(mockPrintWriter1.equals((Object)mockPrintWriter0));
      assertNotNull(mockPrintWriter1);
      
      mockPrintWriter1.println((float) 1);
      assertFalse(mockPrintWriter0.equals((Object)mockPrintWriter1));
      assertFalse(mockPrintWriter1.equals((Object)mockPrintWriter0));
      assertNotSame(mockPrintWriter0, mockPrintWriter1);
      assertNotSame(mockPrintWriter1, mockPrintWriter0);
      
      mockPrintWriter0.println('k');
      assertFalse(mockPrintWriter0.equals((Object)mockPrintWriter1));
      assertNotSame(mockPrintWriter0, mockPrintWriter1);
      
      Locale locale0 = Locale.SIMPLIFIED_CHINESE;
      assertEquals("zh", locale0.getLanguage());
      assertEquals("CN", locale0.getCountry());
      assertEquals("zh_CN", locale0.toString());
      assertEquals("", locale0.getVariant());
      assertEquals("zho", locale0.getISO3Language());
      assertEquals("CHN", locale0.getISO3Country());
      assertNotNull(locale0);
      
      PrintWriter printWriter0 = mockPrintWriter1.printf(locale0, " ", (Object[]) null);
      assertEquals("zh", locale0.getLanguage());
      assertEquals("CN", locale0.getCountry());
      assertEquals("zh_CN", locale0.toString());
      assertEquals("", locale0.getVariant());
      assertEquals("zho", locale0.getISO3Language());
      assertEquals("CHN", locale0.getISO3Country());
      assertFalse(mockPrintWriter0.equals((Object)mockPrintWriter1));
      assertFalse(mockPrintWriter1.equals((Object)mockPrintWriter0));
      assertFalse(printWriter0.equals((Object)mockPrintWriter0));
      assertNotSame(mockPrintWriter0, printWriter0);
      assertNotSame(mockPrintWriter0, mockPrintWriter1);
      assertNotSame(mockPrintWriter1, mockPrintWriter0);
      assertSame(mockPrintWriter1, printWriter0);
      assertSame(printWriter0, mockPrintWriter1);
      assertNotSame(printWriter0, mockPrintWriter0);
      assertNotNull(printWriter0);
      
      mockPrintWriter1.print(74);
      assertFalse(mockPrintWriter0.equals((Object)printWriter0));
      assertFalse(mockPrintWriter0.equals((Object)mockPrintWriter1));
      assertFalse(mockPrintWriter1.equals((Object)mockPrintWriter0));
      assertNotSame(mockPrintWriter0, printWriter0);
      assertNotSame(mockPrintWriter0, mockPrintWriter1);
      assertNotSame(mockPrintWriter1, mockPrintWriter0);
      assertSame(mockPrintWriter1, printWriter0);
      
      helpFormatter0.printUsage(printWriter0, 535, "yJAsG$]`s2t");
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals("\n", helpFormatter0.getNewLine());
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals("zh", locale0.getLanguage());
      assertEquals("CN", locale0.getCountry());
      assertEquals("zh_CN", locale0.toString());
      assertEquals("", locale0.getVariant());
      assertEquals("zho", locale0.getISO3Language());
      assertEquals("CHN", locale0.getISO3Country());
      assertFalse(mockPrintWriter0.equals((Object)printWriter0));
      assertFalse(mockPrintWriter0.equals((Object)mockPrintWriter1));
      assertFalse(mockPrintWriter1.equals((Object)mockPrintWriter0));
      assertFalse(printWriter0.equals((Object)mockPrintWriter0));
      assertNotSame(mockPrintWriter0, printWriter0);
      assertNotSame(mockPrintWriter0, mockPrintWriter1);
      assertNotSame(mockPrintWriter1, mockPrintWriter0);
      assertSame(mockPrintWriter1, printWriter0);
      assertSame(printWriter0, mockPrintWriter1);
      assertNotSame(printWriter0, mockPrintWriter0);
      assertEquals(74, helpFormatter0.defaultWidth);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals(3, helpFormatter0.defaultDescPad);
      
      // Undeclared exception!
      try { 
        helpFormatter0.findWrapPos((String) null, (-492), (-492));
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.cli.HelpFormatter", e);
      }
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals("\n", helpFormatter0.getNewLine());
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(74, helpFormatter0.defaultWidth);
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertNotNull(helpFormatter0);
      
      Options options0 = new Options();
      assertNotNull(options0);
      
      helpFormatter0.setNewLine("4o,\"k6u3}uB2u0");
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertEquals("4o,\"k6u3}uB2u0", helpFormatter0.getNewLine());
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(74, helpFormatter0.defaultWidth);
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      
      helpFormatter0.setWidth(9);
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals(9, helpFormatter0.getWidth());
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertEquals("4o,\"k6u3}uB2u0", helpFormatter0.getNewLine());
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertEquals(9, helpFormatter0.defaultWidth);
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      
      String string0 = helpFormatter0.getLongOptPrefix();
      assertEquals("--", string0);
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals(9, helpFormatter0.getWidth());
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertEquals("4o,\"k6u3}uB2u0", helpFormatter0.getNewLine());
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertEquals(9, helpFormatter0.defaultWidth);
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertNotNull(string0);
      
      HelpFormatter helpFormatter1 = new HelpFormatter();
      assertEquals("usage: ", helpFormatter1.getSyntaxPrefix());
      assertEquals(3, helpFormatter1.getDescPadding());
      assertEquals(" ", helpFormatter1.getLongOptSeparator());
      assertEquals(74, helpFormatter1.getWidth());
      assertEquals("\n", helpFormatter1.getNewLine());
      assertEquals("arg", helpFormatter1.getArgName());
      assertEquals(1, helpFormatter1.getLeftPadding());
      assertEquals("-", helpFormatter1.getOptPrefix());
      assertEquals("--", helpFormatter1.getLongOptPrefix());
      assertFalse(helpFormatter1.equals((Object)helpFormatter0));
      assertEquals(1, helpFormatter1.defaultLeftPad);
      assertEquals(3, helpFormatter1.defaultDescPad);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertEquals(74, helpFormatter1.defaultWidth);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertNotNull(helpFormatter1);
      
      Comparator<Option> comparator0 = helpFormatter1.getOptionComparator();
      assertEquals("usage: ", helpFormatter1.getSyntaxPrefix());
      assertEquals(3, helpFormatter1.getDescPadding());
      assertEquals(" ", helpFormatter1.getLongOptSeparator());
      assertEquals(74, helpFormatter1.getWidth());
      assertEquals("\n", helpFormatter1.getNewLine());
      assertEquals("arg", helpFormatter1.getArgName());
      assertEquals(1, helpFormatter1.getLeftPadding());
      assertEquals("-", helpFormatter1.getOptPrefix());
      assertEquals("--", helpFormatter1.getLongOptPrefix());
      assertFalse(helpFormatter1.equals((Object)helpFormatter0));
      assertNotSame(helpFormatter1, helpFormatter0);
      assertEquals(1, helpFormatter1.defaultLeftPad);
      assertEquals(3, helpFormatter1.defaultDescPad);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertEquals(74, helpFormatter1.defaultWidth);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertNotNull(comparator0);
      
      // Undeclared exception!
      try { 
        helpFormatter0.renderWrappedText((StringBuffer) null, 2, (-466), "4o,\"k6u3}uB2u0");
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.cli.HelpFormatter", e);
      }
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals("\n", helpFormatter0.getNewLine());
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertEquals(74, helpFormatter0.defaultWidth);
      assertNotNull(helpFormatter0);
      
      Options options0 = new Options();
      assertNotNull(options0);
      
      Option option0 = options0.getOption("");
      assertNull(option0);
      
      helpFormatter0.printHelp(">", options0);
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals("\n", helpFormatter0.getNewLine());
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertEquals(74, helpFormatter0.defaultWidth);
      
      helpFormatter0.setDescPadding(3);
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals("\n", helpFormatter0.getNewLine());
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertEquals(74, helpFormatter0.defaultWidth);
      
      MockPrintWriter mockPrintWriter0 = null;
      try {
        mockPrintWriter0 = new MockPrintWriter((Writer) null, false);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("java.io.Writer", e);
      }
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
    Future<?> future = executor.submit(new Runnable(){ 
            @Override public void run() { 
        try {
          HelpFormatter helpFormatter0 = new HelpFormatter();
          assertEquals("--", helpFormatter0.getLongOptPrefix());
          assertEquals(74, helpFormatter0.getWidth());
          assertEquals("arg", helpFormatter0.getArgName());
          assertEquals(3, helpFormatter0.getDescPadding());
          assertEquals("-", helpFormatter0.getOptPrefix());
          assertEquals("\n", helpFormatter0.getNewLine());
          assertEquals(1, helpFormatter0.getLeftPadding());
          assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
          assertEquals(" ", helpFormatter0.getLongOptSeparator());
          assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
          assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
          assertEquals(74, helpFormatter0.defaultWidth);
          assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
          assertEquals(1, helpFormatter0.defaultLeftPad);
          assertEquals(3, helpFormatter0.defaultDescPad);
          assertNotNull(helpFormatter0);
          
          String string0 = helpFormatter0.createPadding(0);
          assertEquals("", string0);
          assertEquals("--", helpFormatter0.getLongOptPrefix());
          assertEquals(74, helpFormatter0.getWidth());
          assertEquals("arg", helpFormatter0.getArgName());
          assertEquals(3, helpFormatter0.getDescPadding());
          assertEquals("-", helpFormatter0.getOptPrefix());
          assertEquals("\n", helpFormatter0.getNewLine());
          assertEquals(1, helpFormatter0.getLeftPadding());
          assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
          assertEquals(" ", helpFormatter0.getLongOptSeparator());
          assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
          assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
          assertEquals(74, helpFormatter0.defaultWidth);
          assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
          assertEquals(1, helpFormatter0.defaultLeftPad);
          assertEquals(3, helpFormatter0.defaultDescPad);
          assertNotNull(string0);
          
          Options options0 = new Options();
          assertNotNull(options0);
          
          String string1 = "";
          Option option0 = new Option("", "<Sr3sm]{'", true, "<Sr3sm]{'");
          assertFalse(option0.hasArgs());
          assertNull(option0.getValue());
          assertEquals("", option0.getOpt());
          assertFalse(option0.isRequired());
          assertFalse(option0.hasValueSeparator());
          assertEquals(1, option0.getArgs());
          assertTrue(option0.hasLongOpt());
          assertFalse(option0.hasOptionalArg());
          assertEquals("<Sr3sm]{'", option0.getDescription());
          assertEquals("<Sr3sm]{'", option0.getLongOpt());
          assertFalse(option0.hasArgName());
          assertTrue(option0.hasArg());
          assertEquals('\u0000', option0.getValueSeparator());
          assertNull(option0.getArgName());
          assertEquals((-2), Option.UNLIMITED_VALUES);
          assertEquals((-1), Option.UNINITIALIZED);
          assertNotNull(option0);
          
          Options options1 = options0.addOption(option0);
          assertFalse(option0.hasArgs());
          assertNull(option0.getValue());
          assertEquals("", option0.getOpt());
          assertFalse(option0.isRequired());
          assertFalse(option0.hasValueSeparator());
          assertEquals(1, option0.getArgs());
          assertTrue(option0.hasLongOpt());
          assertFalse(option0.hasOptionalArg());
          assertEquals("<Sr3sm]{'", option0.getDescription());
          assertEquals("<Sr3sm]{'", option0.getLongOpt());
          assertFalse(option0.hasArgName());
          assertTrue(option0.hasArg());
          assertEquals('\u0000', option0.getValueSeparator());
          assertNull(option0.getArgName());
          assertSame(options0, options1);
          assertSame(options1, options0);
          assertEquals((-2), Option.UNLIMITED_VALUES);
          assertEquals((-1), Option.UNINITIALIZED);
          assertNotNull(options1);
          
          helpFormatter0.printHelp("[*<=[", "[*<=[", options1, "<Sr3sm]{'", false);
          assertEquals("--", helpFormatter0.getLongOptPrefix());
          assertEquals(74, helpFormatter0.getWidth());
          assertEquals("arg", helpFormatter0.getArgName());
          assertEquals(3, helpFormatter0.getDescPadding());
          assertEquals("-", helpFormatter0.getOptPrefix());
          assertEquals("\n", helpFormatter0.getNewLine());
          assertEquals(1, helpFormatter0.getLeftPadding());
          assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
          assertEquals(" ", helpFormatter0.getLongOptSeparator());
          assertFalse(option0.hasArgs());
          assertNull(option0.getValue());
          assertEquals("", option0.getOpt());
          assertFalse(option0.isRequired());
          assertFalse(option0.hasValueSeparator());
          assertEquals(1, option0.getArgs());
          assertTrue(option0.hasLongOpt());
          assertFalse(option0.hasOptionalArg());
          assertEquals("<Sr3sm]{'", option0.getDescription());
          assertEquals("<Sr3sm]{'", option0.getLongOpt());
          assertFalse(option0.hasArgName());
          assertTrue(option0.hasArg());
          assertEquals('\u0000', option0.getValueSeparator());
          assertNull(option0.getArgName());
          assertSame(options0, options1);
          assertSame(options1, options0);
          assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
          assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
          assertEquals(74, helpFormatter0.defaultWidth);
          assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
          assertEquals(1, helpFormatter0.defaultLeftPad);
          assertEquals(3, helpFormatter0.defaultDescPad);
          assertEquals((-2), Option.UNLIMITED_VALUES);
          assertEquals((-1), Option.UNINITIALIZED);
          
          helpFormatter0.setLeftPadding(1783);
          assertEquals("--", helpFormatter0.getLongOptPrefix());
          assertEquals(74, helpFormatter0.getWidth());
          assertEquals("arg", helpFormatter0.getArgName());
          assertEquals(3, helpFormatter0.getDescPadding());
          assertEquals("-", helpFormatter0.getOptPrefix());
          assertEquals("\n", helpFormatter0.getNewLine());
          assertEquals(1783, helpFormatter0.getLeftPadding());
          assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
          assertEquals(" ", helpFormatter0.getLongOptSeparator());
          assertEquals(1783, helpFormatter0.defaultLeftPad);
          assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
          assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
          assertEquals(74, helpFormatter0.defaultWidth);
          assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
          assertEquals(3, helpFormatter0.defaultDescPad);
          
          FileDescriptor fileDescriptor0 = new FileDescriptor();
          assertFalse(fileDescriptor0.valid());
          assertNotNull(fileDescriptor0);
          
          FileDescriptor fileDescriptor1 = new FileDescriptor();
          assertFalse(fileDescriptor1.valid());
          assertFalse(fileDescriptor1.equals((Object)fileDescriptor0));
          assertNotNull(fileDescriptor1);
          
          MockFileOutputStream mockFileOutputStream0 = null;
          try {
            mockFileOutputStream0 = new MockFileOutputStream(fileDescriptor1);
            fail("Expecting exception: SecurityException");
          
          } catch(SecurityException e) {
             //
             // Security manager blocks (\"java.lang.RuntimePermission\" \"writeFileDescriptor\")
             // java.lang.Thread.getStackTrace(Thread.java:1559)
             // org.evosuite.runtime.sandbox.MSecurityManager.checkPermission(MSecurityManager.java:424)
             // java.lang.SecurityManager.checkWrite(SecurityManager.java:954)
             // java.io.FileOutputStream.<init>(FileOutputStream.java:245)
             // org.evosuite.runtime.mock.java.io.MockFileOutputStream.<init>(MockFileOutputStream.java:114)
             // sun.reflect.GeneratedConstructorAccessor41.newInstance(Unknown Source)
             // sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
             // java.lang.reflect.Constructor.newInstance(Constructor.java:423)
             // org.evosuite.testcase.statements.ConstructorStatement$1.execute(ConstructorStatement.java:218)
             // org.evosuite.testcase.statements.AbstractStatement.exceptionHandler(AbstractStatement.java:165)
             // org.evosuite.testcase.statements.ConstructorStatement.execute(ConstructorStatement.java:173)
             // org.evosuite.testcase.execution.TestRunnable.executeStatements(TestRunnable.java:286)
             // org.evosuite.testcase.execution.TestRunnable.call(TestRunnable.java:192)
             // org.evosuite.testcase.execution.TestRunnable.call(TestRunnable.java:49)
             // java.util.concurrent.FutureTask.run(FutureTask.java:266)
             // java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
             // java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
             // java.lang.Thread.run(Thread.java:748)
             //
             verifyException("org.evosuite.runtime.sandbox.MSecurityManager", e);
          }
        } catch(Throwable t) {
            // Need to catch declared exceptions
        }
      } 
    });
    future.get(4000, TimeUnit.MILLISECONDS);
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals("\n", helpFormatter0.getNewLine());
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertEquals(74, helpFormatter0.defaultWidth);
      assertNotNull(helpFormatter0);
      
      Options options0 = new Options();
      assertNotNull(options0);
      
      helpFormatter0.printHelp("|/_)rPD{X=}Yn", ":t\"3B", options0, "yT,E'VaLe6W~7", false);
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals("\n", helpFormatter0.getNewLine());
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertEquals(74, helpFormatter0.defaultWidth);
      
      HelpFormatter helpFormatter1 = new HelpFormatter();
      assertEquals("\n", helpFormatter1.getNewLine());
      assertEquals("arg", helpFormatter1.getArgName());
      assertEquals(" ", helpFormatter1.getLongOptSeparator());
      assertEquals(3, helpFormatter1.getDescPadding());
      assertEquals(74, helpFormatter1.getWidth());
      assertEquals(1, helpFormatter1.getLeftPadding());
      assertEquals("-", helpFormatter1.getOptPrefix());
      assertEquals("--", helpFormatter1.getLongOptPrefix());
      assertEquals("usage: ", helpFormatter1.getSyntaxPrefix());
      assertFalse(helpFormatter1.equals((Object)helpFormatter0));
      assertEquals(1, helpFormatter1.defaultLeftPad);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(3, helpFormatter1.defaultDescPad);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertEquals(74, helpFormatter1.defaultWidth);
      assertNotNull(helpFormatter1);
      
      helpFormatter1.setLeftPadding(413);
      assertEquals("\n", helpFormatter1.getNewLine());
      assertEquals("arg", helpFormatter1.getArgName());
      assertEquals(" ", helpFormatter1.getLongOptSeparator());
      assertEquals(3, helpFormatter1.getDescPadding());
      assertEquals(74, helpFormatter1.getWidth());
      assertEquals(413, helpFormatter1.getLeftPadding());
      assertEquals("-", helpFormatter1.getOptPrefix());
      assertEquals("--", helpFormatter1.getLongOptPrefix());
      assertEquals("usage: ", helpFormatter1.getSyntaxPrefix());
      assertFalse(helpFormatter1.equals((Object)helpFormatter0));
      assertNotSame(helpFormatter1, helpFormatter0);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(413, helpFormatter1.defaultLeftPad);
      assertEquals(3, helpFormatter1.defaultDescPad);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertEquals(74, helpFormatter1.defaultWidth);
      
      FileDescriptor fileDescriptor0 = new FileDescriptor();
      assertFalse(fileDescriptor0.valid());
      assertNotNull(fileDescriptor0);
      
      String string0 = helpFormatter0.getLongOptPrefix();
      assertEquals("--", string0);
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals("\n", helpFormatter0.getNewLine());
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertFalse(helpFormatter0.equals((Object)helpFormatter1));
      assertNotSame(helpFormatter0, helpFormatter1);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertEquals(74, helpFormatter0.defaultWidth);
      assertNotNull(string0);
      
      HelpFormatter helpFormatter2 = new HelpFormatter();
      assertEquals("usage: ", helpFormatter2.getSyntaxPrefix());
      assertEquals(" ", helpFormatter2.getLongOptSeparator());
      assertEquals(3, helpFormatter2.getDescPadding());
      assertEquals(74, helpFormatter2.getWidth());
      assertEquals("arg", helpFormatter2.getArgName());
      assertEquals(1, helpFormatter2.getLeftPadding());
      assertEquals("-", helpFormatter2.getOptPrefix());
      assertEquals("\n", helpFormatter2.getNewLine());
      assertEquals("--", helpFormatter2.getLongOptPrefix());
      assertFalse(helpFormatter2.equals((Object)helpFormatter1));
      assertFalse(helpFormatter2.equals((Object)helpFormatter0));
      assertEquals(1, helpFormatter2.defaultLeftPad);
      assertEquals(3, helpFormatter2.defaultDescPad);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertEquals(74, helpFormatter2.defaultWidth);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertNotNull(helpFormatter2);
      
      Comparator<Option> comparator0 = helpFormatter0.getOptionComparator();
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals("\n", helpFormatter0.getNewLine());
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertFalse(helpFormatter0.equals((Object)helpFormatter2));
      assertFalse(helpFormatter0.equals((Object)helpFormatter1));
      assertNotSame(helpFormatter0, helpFormatter2);
      assertNotSame(helpFormatter0, helpFormatter1);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertEquals(74, helpFormatter0.defaultWidth);
      assertNotNull(comparator0);
      
      // Undeclared exception!
      try { 
        helpFormatter2.renderWrappedText((StringBuffer) null, 3, 3, (String) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.cli.HelpFormatter", e);
      }
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
    Future<?> future = executor.submit(new Runnable(){ 
            @Override public void run() { 
        try {
          HelpFormatter helpFormatter0 = new HelpFormatter();
          assertEquals(" ", helpFormatter0.getLongOptSeparator());
          assertEquals(3, helpFormatter0.getDescPadding());
          assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
          assertEquals("--", helpFormatter0.getLongOptPrefix());
          assertEquals(74, helpFormatter0.getWidth());
          assertEquals("\n", helpFormatter0.getNewLine());
          assertEquals("arg", helpFormatter0.getArgName());
          assertEquals(1, helpFormatter0.getLeftPadding());
          assertEquals("-", helpFormatter0.getOptPrefix());
          assertEquals(74, helpFormatter0.defaultWidth);
          assertEquals(3, helpFormatter0.defaultDescPad);
          assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
          assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
          assertEquals(1, helpFormatter0.defaultLeftPad);
          assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
          assertNotNull(helpFormatter0);
          
          String string0 = "[*<=[";
          String string1 = helpFormatter0.createPadding(0);
          assertEquals("", string1);
          assertEquals(" ", helpFormatter0.getLongOptSeparator());
          assertEquals(3, helpFormatter0.getDescPadding());
          assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
          assertEquals("--", helpFormatter0.getLongOptPrefix());
          assertEquals(74, helpFormatter0.getWidth());
          assertEquals("\n", helpFormatter0.getNewLine());
          assertEquals("arg", helpFormatter0.getArgName());
          assertEquals(1, helpFormatter0.getLeftPadding());
          assertEquals("-", helpFormatter0.getOptPrefix());
          assertFalse(string1.equals((Object)string0));
          assertEquals(74, helpFormatter0.defaultWidth);
          assertEquals(3, helpFormatter0.defaultDescPad);
          assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
          assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
          assertEquals(1, helpFormatter0.defaultLeftPad);
          assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
          assertNotNull(string1);
          
          Options options0 = new Options();
          assertNotNull(options0);
          
          String string2 = "<Sr3sm]{'";
          Option option0 = new Option("", "<Sr3sm]{'", true, "<Sr3sm]{'");
          assertFalse(option0.hasArgName());
          assertEquals(1, option0.getArgs());
          assertFalse(option0.hasArgs());
          assertEquals("<Sr3sm]{'", option0.getLongOpt());
          assertEquals("", option0.getOpt());
          assertNull(option0.getValue());
          assertFalse(option0.isRequired());
          assertFalse(option0.hasValueSeparator());
          assertNull(option0.getArgName());
          assertTrue(option0.hasArg());
          assertEquals('\u0000', option0.getValueSeparator());
          assertFalse(option0.hasOptionalArg());
          assertEquals("<Sr3sm]{'", option0.getDescription());
          assertTrue(option0.hasLongOpt());
          assertEquals((-1), Option.UNINITIALIZED);
          assertEquals((-2), Option.UNLIMITED_VALUES);
          assertNotNull(option0);
          
          Options options1 = options0.addOption(option0);
          assertFalse(option0.hasArgName());
          assertEquals(1, option0.getArgs());
          assertFalse(option0.hasArgs());
          assertEquals("<Sr3sm]{'", option0.getLongOpt());
          assertEquals("", option0.getOpt());
          assertNull(option0.getValue());
          assertFalse(option0.isRequired());
          assertFalse(option0.hasValueSeparator());
          assertNull(option0.getArgName());
          assertTrue(option0.hasArg());
          assertEquals('\u0000', option0.getValueSeparator());
          assertFalse(option0.hasOptionalArg());
          assertEquals("<Sr3sm]{'", option0.getDescription());
          assertTrue(option0.hasLongOpt());
          assertSame(options0, options1);
          assertSame(options1, options0);
          assertEquals((-1), Option.UNINITIALIZED);
          assertEquals((-2), Option.UNLIMITED_VALUES);
          assertNotNull(options1);
          
          helpFormatter0.printHelp("[*<=[", "[*<=[", options1, "<Sr3sm]{'", false);
          assertEquals(" ", helpFormatter0.getLongOptSeparator());
          assertEquals(3, helpFormatter0.getDescPadding());
          assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
          assertEquals("--", helpFormatter0.getLongOptPrefix());
          assertEquals(74, helpFormatter0.getWidth());
          assertEquals("\n", helpFormatter0.getNewLine());
          assertEquals("arg", helpFormatter0.getArgName());
          assertEquals(1, helpFormatter0.getLeftPadding());
          assertEquals("-", helpFormatter0.getOptPrefix());
          assertFalse(option0.hasArgName());
          assertEquals(1, option0.getArgs());
          assertFalse(option0.hasArgs());
          assertEquals("<Sr3sm]{'", option0.getLongOpt());
          assertEquals("", option0.getOpt());
          assertNull(option0.getValue());
          assertFalse(option0.isRequired());
          assertFalse(option0.hasValueSeparator());
          assertNull(option0.getArgName());
          assertTrue(option0.hasArg());
          assertEquals('\u0000', option0.getValueSeparator());
          assertFalse(option0.hasOptionalArg());
          assertEquals("<Sr3sm]{'", option0.getDescription());
          assertTrue(option0.hasLongOpt());
          assertSame(options0, options1);
          assertSame(options1, options0);
          assertEquals(74, helpFormatter0.defaultWidth);
          assertEquals(3, helpFormatter0.defaultDescPad);
          assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
          assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
          assertEquals(1, helpFormatter0.defaultLeftPad);
          assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
          assertEquals((-1), Option.UNINITIALIZED);
          assertEquals((-2), Option.UNLIMITED_VALUES);
          
          helpFormatter0.setLeftPadding(1783);
          assertEquals(" ", helpFormatter0.getLongOptSeparator());
          assertEquals(1783, helpFormatter0.getLeftPadding());
          assertEquals(3, helpFormatter0.getDescPadding());
          assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
          assertEquals("--", helpFormatter0.getLongOptPrefix());
          assertEquals(74, helpFormatter0.getWidth());
          assertEquals("\n", helpFormatter0.getNewLine());
          assertEquals("arg", helpFormatter0.getArgName());
          assertEquals("-", helpFormatter0.getOptPrefix());
          assertEquals(74, helpFormatter0.defaultWidth);
          assertEquals(1783, helpFormatter0.defaultLeftPad);
          assertEquals(3, helpFormatter0.defaultDescPad);
          assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
          assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
          assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
          
          FileDescriptor fileDescriptor0 = new FileDescriptor();
          assertFalse(fileDescriptor0.valid());
          assertNotNull(fileDescriptor0);
          
          FileDescriptor fileDescriptor1 = new FileDescriptor();
          assertFalse(fileDescriptor1.valid());
          assertFalse(fileDescriptor1.equals((Object)fileDescriptor0));
          assertNotNull(fileDescriptor1);
          
          MockFileOutputStream mockFileOutputStream0 = null;
          try {
            mockFileOutputStream0 = new MockFileOutputStream(fileDescriptor1);
            fail("Expecting exception: SecurityException");
          
          } catch(SecurityException e) {
             //
             // Security manager blocks (\"java.lang.RuntimePermission\" \"writeFileDescriptor\")
             // java.lang.Thread.getStackTrace(Thread.java:1559)
             // org.evosuite.runtime.sandbox.MSecurityManager.checkPermission(MSecurityManager.java:424)
             // java.lang.SecurityManager.checkWrite(SecurityManager.java:954)
             // java.io.FileOutputStream.<init>(FileOutputStream.java:245)
             // org.evosuite.runtime.mock.java.io.MockFileOutputStream.<init>(MockFileOutputStream.java:114)
             // sun.reflect.GeneratedConstructorAccessor41.newInstance(Unknown Source)
             // sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
             // java.lang.reflect.Constructor.newInstance(Constructor.java:423)
             // org.evosuite.testcase.statements.ConstructorStatement$1.execute(ConstructorStatement.java:218)
             // org.evosuite.testcase.statements.AbstractStatement.exceptionHandler(AbstractStatement.java:165)
             // org.evosuite.testcase.statements.ConstructorStatement.execute(ConstructorStatement.java:173)
             // org.evosuite.testcase.execution.TestRunnable.executeStatements(TestRunnable.java:286)
             // org.evosuite.testcase.execution.TestRunnable.call(TestRunnable.java:192)
             // org.evosuite.testcase.execution.TestRunnable.call(TestRunnable.java:49)
             // java.util.concurrent.FutureTask.run(FutureTask.java:266)
             // java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
             // java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
             // java.lang.Thread.run(Thread.java:748)
             //
             verifyException("org.evosuite.runtime.sandbox.MSecurityManager", e);
          }
        } catch(Throwable t) {
            // Need to catch declared exceptions
        }
      } 
    });
    future.get(4000, TimeUnit.MILLISECONDS);
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals("\n", helpFormatter0.getNewLine());
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals(74, helpFormatter0.defaultWidth);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertNotNull(helpFormatter0);
      
      helpFormatter0.setSyntaxPrefix("2ktQGwl9IDEu4xP-b");
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertEquals("2ktQGwl9IDEu4xP-b", helpFormatter0.getSyntaxPrefix());
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals("\n", helpFormatter0.getNewLine());
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals(74, helpFormatter0.defaultWidth);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      
      helpFormatter0.setLeftPadding((-674));
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertEquals("2ktQGwl9IDEu4xP-b", helpFormatter0.getSyntaxPrefix());
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals("\n", helpFormatter0.getNewLine());
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals((-674), helpFormatter0.getLeftPadding());
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals(74, helpFormatter0.defaultWidth);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertEquals((-674), helpFormatter0.defaultLeftPad);
      
      Comparator<Option> comparator0 = helpFormatter0.optionComparator;
      assertNotNull(comparator0);
      
      helpFormatter0.optionComparator = comparator0;
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertEquals("2ktQGwl9IDEu4xP-b", helpFormatter0.getSyntaxPrefix());
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals("\n", helpFormatter0.getNewLine());
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals((-674), helpFormatter0.getLeftPadding());
      
      String string0 = "g";
      helpFormatter0.setOptPrefix("g");
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertEquals("g", helpFormatter0.getOptPrefix());
      assertEquals("2ktQGwl9IDEu4xP-b", helpFormatter0.getSyntaxPrefix());
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals("\n", helpFormatter0.getNewLine());
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals((-674), helpFormatter0.getLeftPadding());
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals(74, helpFormatter0.defaultWidth);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertEquals((-674), helpFormatter0.defaultLeftPad);
      
      Comparator<Option> comparator1 = helpFormatter0.getOptionComparator();
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertEquals("g", helpFormatter0.getOptPrefix());
      assertEquals("2ktQGwl9IDEu4xP-b", helpFormatter0.getSyntaxPrefix());
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals("\n", helpFormatter0.getNewLine());
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals((-674), helpFormatter0.getLeftPadding());
      assertSame(comparator1, comparator0);
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals(74, helpFormatter0.defaultWidth);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertEquals((-674), helpFormatter0.defaultLeftPad);
      assertNotNull(comparator1);
      
      String string1 = helpFormatter0.getNewLine();
      assertEquals("\n", string1);
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertEquals("g", helpFormatter0.getOptPrefix());
      assertEquals("2ktQGwl9IDEu4xP-b", helpFormatter0.getSyntaxPrefix());
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals("\n", helpFormatter0.getNewLine());
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals((-674), helpFormatter0.getLeftPadding());
      assertFalse(string1.equals((Object)string0));
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals(74, helpFormatter0.defaultWidth);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertEquals((-674), helpFormatter0.defaultLeftPad);
      assertNotNull(string1);
      
      String string2 = helpFormatter0.getLongOptSeparator();
      assertEquals(" ", string2);
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertEquals("g", helpFormatter0.getOptPrefix());
      assertEquals("2ktQGwl9IDEu4xP-b", helpFormatter0.getSyntaxPrefix());
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals("\n", helpFormatter0.getNewLine());
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals((-674), helpFormatter0.getLeftPadding());
      assertFalse(string2.equals((Object)string0));
      assertFalse(string2.equals((Object)string1));
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals(74, helpFormatter0.defaultWidth);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertEquals((-674), helpFormatter0.defaultLeftPad);
      assertNotNull(string2);
      
      StringWriter stringWriter0 = new StringWriter(74);
      assertEquals("", stringWriter0.toString());
      assertNotNull(stringWriter0);
      
      StringWriter stringWriter1 = stringWriter0.append((CharSequence) "\n");
      assertEquals("\n", stringWriter0.toString());
      assertEquals("\n", stringWriter1.toString());
      assertSame(stringWriter0, stringWriter1);
      assertSame(stringWriter1, stringWriter0);
      assertNotNull(stringWriter1);
      
      // Undeclared exception!
      try { 
        stringWriter1.append((CharSequence) "g", (-674), 74);
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
      }
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals("\n", helpFormatter0.getNewLine());
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(74, helpFormatter0.defaultWidth);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertNotNull(helpFormatter0);
      
      helpFormatter0.setLeftPadding(1772);
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertEquals(1772, helpFormatter0.getLeftPadding());
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals("\n", helpFormatter0.getNewLine());
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(74, helpFormatter0.defaultWidth);
      assertEquals(1772, helpFormatter0.defaultLeftPad);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      
      String string0 = null;
      String string1 = helpFormatter0.rtrim((String) null);
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertEquals(1772, helpFormatter0.getLeftPadding());
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals("\n", helpFormatter0.getNewLine());
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(74, helpFormatter0.defaultWidth);
      assertEquals(1772, helpFormatter0.defaultLeftPad);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertNull(string1);
      
      MockPrintWriter mockPrintWriter0 = null;
      try {
        mockPrintWriter0 = new MockPrintWriter((OutputStream) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("java.io.Writer", e);
      }
  }

  @Test(timeout = 4000)
  public void test22()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      assertEquals("\n", helpFormatter0.getNewLine());
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(74, helpFormatter0.defaultWidth);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertNotNull(helpFormatter0);
      
      int int0 = 1772;
      helpFormatter0.setLeftPadding(1772);
      assertEquals("\n", helpFormatter0.getNewLine());
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals(1772, helpFormatter0.getLeftPadding());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals(74, helpFormatter0.defaultWidth);
      assertEquals(1772, helpFormatter0.defaultLeftPad);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      
      String string0 = null;
      String string1 = helpFormatter0.rtrim((String) null);
      assertEquals("\n", helpFormatter0.getNewLine());
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals(1772, helpFormatter0.getLeftPadding());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals(74, helpFormatter0.defaultWidth);
      assertEquals(1772, helpFormatter0.defaultLeftPad);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertNull(string1);
      
      MockPrintWriter mockPrintWriter0 = null;
      try {
        mockPrintWriter0 = new MockPrintWriter((OutputStream) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("java.io.Writer", e);
      }
  }

  @Test(timeout = 4000)
  public void test23()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals("\n", helpFormatter0.getNewLine());
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertEquals(74, helpFormatter0.defaultWidth);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertNotNull(helpFormatter0);
      
      helpFormatter0.setWidth((-987));
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals("\n", helpFormatter0.getNewLine());
      assertEquals((-987), helpFormatter0.getWidth());
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals((-987), helpFormatter0.defaultWidth);
      
      String string0 = "";
      helpFormatter0.setNewLine("");
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals((-987), helpFormatter0.getWidth());
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals("", helpFormatter0.getNewLine());
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals((-987), helpFormatter0.defaultWidth);
      
      int int0 = helpFormatter0.getDescPadding();
      assertEquals(3, int0);
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals((-987), helpFormatter0.getWidth());
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals("", helpFormatter0.getNewLine());
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals((-987), helpFormatter0.defaultWidth);
      
      int int1 = helpFormatter0.getLeftPadding();
      assertEquals(1, int1);
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals((-987), helpFormatter0.getWidth());
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals("", helpFormatter0.getNewLine());
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertFalse(int1 == int0);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals((-987), helpFormatter0.defaultWidth);
      
      MockPrintWriter mockPrintWriter0 = null;
      try {
        mockPrintWriter0 = new MockPrintWriter((OutputStream) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("java.io.Writer", e);
      }
  }

  @Test(timeout = 4000)
  public void test24()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals("\n", helpFormatter0.getNewLine());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals(74, helpFormatter0.defaultWidth);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertNotNull(helpFormatter0);
      
      String string0 = "";
      helpFormatter0.setArgName("");
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals("\n", helpFormatter0.getNewLine());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals("", helpFormatter0.getArgName());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals(74, helpFormatter0.defaultWidth);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      
      String string1 = "org.apache.commons.cli.Option$Builder";
      MockPrintStream mockPrintStream0 = null;
      try {
        mockPrintStream0 = new MockPrintStream((File) null, "org.apache.commons.cli.Option$Builder");
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("java.io.File", e);
      }
  }

  @Test(timeout = 4000)
  public void test25()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals("\n", helpFormatter0.getNewLine());
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertEquals(74, helpFormatter0.defaultWidth);
      assertNotNull(helpFormatter0);
      
      Options options0 = new Options();
      assertNotNull(options0);
      
      // Undeclared exception!
      try { 
        helpFormatter0.printHelp("usage: ", "--", (Options) null, "yT,E'VaLe6W~7", false);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.cli.HelpFormatter", e);
      }
  }

  @Test(timeout = 4000)
  public void test26()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals("\n", helpFormatter0.getNewLine());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(74, helpFormatter0.defaultWidth);
      assertNotNull(helpFormatter0);
      
      Options options0 = new Options();
      assertNotNull(options0);
      
      // Undeclared exception!
      try { 
        helpFormatter0.printHelp((String) null, "yT,E'VaLe6W~7", options0, ":t\"3B", false);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // cmdLineSyntax not provided
         //
         verifyException("org.apache.commons.cli.HelpFormatter", e);
      }
  }

  @Test(timeout = 4000)
  public void test27()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals("\n", helpFormatter0.getNewLine());
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(74, helpFormatter0.defaultWidth);
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertNotNull(helpFormatter0);
      
      Options options0 = new Options();
      assertNotNull(options0);
      
      String string0 = ":t\"3B";
      // Undeclared exception!
      try { 
        helpFormatter0.printHelp((String) null, "yT,E'VaLe6W~7", options0, ":t\"3B", false);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // cmdLineSyntax not provided
         //
         verifyException("org.apache.commons.cli.HelpFormatter", e);
      }
  }

  @Test(timeout = 4000)
  public void test28()  throws Throwable  {
      boolean boolean0 = FileSystemHandling.shouldAllThrowIOExceptions();
      assertTrue(boolean0);
      
      boolean boolean1 = FileSystemHandling.createFolder((EvoSuiteFile) null);
      assertFalse(boolean1);
      assertFalse(boolean1 == boolean0);
      
      MockPrintWriter mockPrintWriter0 = new MockPrintWriter("lUTo7p1");
      assertNotNull(mockPrintWriter0);
      
      HelpFormatter helpFormatter0 = new HelpFormatter();
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals("\n", helpFormatter0.getNewLine());
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals(74, helpFormatter0.defaultWidth);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertNotNull(helpFormatter0);
      
      helpFormatter0.setLeftPadding(13);
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals(13, helpFormatter0.getLeftPadding());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals("\n", helpFormatter0.getNewLine());
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals(74, helpFormatter0.defaultWidth);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertEquals(13, helpFormatter0.defaultLeftPad);
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      
      int int0 = helpFormatter0.getWidth();
      assertEquals(74, int0);
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals(13, helpFormatter0.getLeftPadding());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals("\n", helpFormatter0.getNewLine());
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals(74, helpFormatter0.defaultWidth);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertEquals(13, helpFormatter0.defaultLeftPad);
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      
      String string0 = helpFormatter0.getArgName();
      assertEquals("arg", string0);
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals(13, helpFormatter0.getLeftPadding());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals("\n", helpFormatter0.getNewLine());
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals(74, helpFormatter0.defaultWidth);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertEquals(13, helpFormatter0.defaultLeftPad);
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertNotNull(string0);
  }

  @Test(timeout = 4000)
  public void test29()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals("\n", helpFormatter0.getNewLine());
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(74, helpFormatter0.defaultWidth);
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertNotNull(helpFormatter0);
      
      helpFormatter0.setDescPadding(0);
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals(0, helpFormatter0.getDescPadding());
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals("\n", helpFormatter0.getNewLine());
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(74, helpFormatter0.defaultWidth);
      assertEquals(0, helpFormatter0.defaultDescPad);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      
      helpFormatter0.setDescPadding(0);
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals(0, helpFormatter0.getDescPadding());
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals("\n", helpFormatter0.getNewLine());
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(74, helpFormatter0.defaultWidth);
      assertEquals(0, helpFormatter0.defaultDescPad);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      
      String string0 = helpFormatter0.getArgName();
      assertEquals("arg", string0);
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals(0, helpFormatter0.getDescPadding());
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals("\n", helpFormatter0.getNewLine());
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(74, helpFormatter0.defaultWidth);
      assertEquals(0, helpFormatter0.defaultDescPad);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertNotNull(string0);
      
      boolean boolean0 = true;
      MockFileOutputStream mockFileOutputStream0 = new MockFileOutputStream("arg", true);
      assertNotNull(mockFileOutputStream0);
      
      MockPrintWriter mockPrintWriter0 = new MockPrintWriter(mockFileOutputStream0);
      assertNotNull(mockPrintWriter0);
      
      int int0 = (-482);
      String string1 = "";
      Options options0 = new Options();
      assertNotNull(options0);
      
      Option option0 = null;
      try {
        option0 = new Option("Lad4k\"'rv&", "|;IytJw;Qg]");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The option 'Lad4k\"'rv&' contains an illegal character : '\"'
         //
         verifyException("org.apache.commons.cli.OptionValidator", e);
      }
  }

  @Test(timeout = 4000)
  public void test30()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      assertEquals("\n", helpFormatter0.getNewLine());
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals(74, helpFormatter0.defaultWidth);
      assertNotNull(helpFormatter0);
      
      helpFormatter0.defaultLongOptPrefix = ", ";
      assertEquals("\n", helpFormatter0.getNewLine());
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals(", ", helpFormatter0.getLongOptPrefix());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals(3, helpFormatter0.getDescPadding());
      
      helpFormatter0.setWidth((-1130));
      assertEquals("\n", helpFormatter0.getNewLine());
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals((-1130), helpFormatter0.getWidth());
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals(", ", helpFormatter0.getLongOptPrefix());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals((-1130), helpFormatter0.defaultWidth);
      
      boolean boolean0 = FileSystemHandling.shouldAllThrowIOExceptions();
      assertTrue(boolean0);
      
      String string0 = helpFormatter0.getLongOptPrefix();
      assertEquals(", ", string0);
      assertEquals("\n", helpFormatter0.getNewLine());
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals((-1130), helpFormatter0.getWidth());
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals(", ", helpFormatter0.getLongOptPrefix());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals((-1130), helpFormatter0.defaultWidth);
      assertNotNull(string0);
      
      Comparator<Option> comparator0 = helpFormatter0.getOptionComparator();
      assertEquals("\n", helpFormatter0.getNewLine());
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals((-1130), helpFormatter0.getWidth());
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals(", ", helpFormatter0.getLongOptPrefix());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals((-1130), helpFormatter0.defaultWidth);
      assertNotNull(comparator0);
      
      // Undeclared exception!
      try { 
        helpFormatter0.renderWrappedText((StringBuffer) null, 3, (-30), "usage: ");
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.cli.HelpFormatter", e);
      }
  }

  @Test(timeout = 4000)
  public void test31()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      assertEquals("\n", helpFormatter0.getNewLine());
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals(74, helpFormatter0.defaultWidth);
      assertNotNull(helpFormatter0);
      
      helpFormatter0.defaultArgName = "wAEaUEPon=";
      assertEquals("\n", helpFormatter0.getNewLine());
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals("wAEaUEPon=", helpFormatter0.getArgName());
      assertEquals(3, helpFormatter0.getDescPadding());
      
      helpFormatter0.setDescPadding(0);
      assertEquals("\n", helpFormatter0.getNewLine());
      assertEquals(0, helpFormatter0.getDescPadding());
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals("wAEaUEPon=", helpFormatter0.getArgName());
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals(0, helpFormatter0.defaultDescPad);
      assertEquals(74, helpFormatter0.defaultWidth);
  }

  @Test(timeout = 4000)
  public void test32()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertEquals("\n", helpFormatter0.getNewLine());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals(74, helpFormatter0.defaultWidth);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertNotNull(helpFormatter0);
      
      String string0 = "";
      // Undeclared exception!
      try { 
        helpFormatter0.printUsage((PrintWriter) null, 61, "");
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.cli.HelpFormatter", e);
      }
  }

  @Test(timeout = 4000)
  public void test33()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals("\n", helpFormatter0.getNewLine());
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertEquals(74, helpFormatter0.defaultWidth);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertNotNull(helpFormatter0);
      
      String string0 = "";
      // Undeclared exception!
      try { 
        helpFormatter0.printWrapped((PrintWriter) null, 0, 0, "");
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.cli.HelpFormatter", e);
      }
  }

  @Test(timeout = 4000)
  public void test34()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals("\n", helpFormatter0.getNewLine());
      assertEquals(74, helpFormatter0.defaultWidth);
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      assertNotNull(helpFormatter0);
      
      int int0 = helpFormatter0.getWidth();
      assertEquals(74, int0);
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals("\n", helpFormatter0.getNewLine());
      assertEquals(74, helpFormatter0.defaultWidth);
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      
      String string0 = null;
      helpFormatter0.setLongOptPrefix((String) null);
      assertNull(helpFormatter0.getLongOptPrefix());
      assertEquals(3, helpFormatter0.getDescPadding());
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals(1, helpFormatter0.getLeftPadding());
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(74, helpFormatter0.getWidth());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals("\n", helpFormatter0.getNewLine());
      assertEquals(74, helpFormatter0.defaultWidth);
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(74, HelpFormatter.DEFAULT_WIDTH);
      assertEquals(3, HelpFormatter.DEFAULT_DESC_PAD);
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(1, HelpFormatter.DEFAULT_LEFT_PAD);
      
      StringWriter stringWriter0 = new StringWriter();
      assertEquals("", stringWriter0.toString());
      assertNotNull(stringWriter0);
      
      MockPrintWriter mockPrintWriter0 = new MockPrintWriter(stringWriter0);
      assertNotNull(mockPrintWriter0);
      
      Options options0 = new Options();
      assertNotNull(options0);
      
      Options options1 = options0.addRequiredOption((String) null, (String) null, true, "-");
      assertSame(options0, options1);
      assertSame(options1, options0);
      assertNotNull(options1);
      
      OptionGroup optionGroup0 = new OptionGroup();
      assertFalse(optionGroup0.isRequired());
      assertNull(optionGroup0.getSelected());
      assertNotNull(optionGroup0);
      
      Options options2 = options1.addOptionGroup(optionGroup0);
      assertFalse(optionGroup0.isRequired());
      assertNull(optionGroup0.getSelected());
      assertSame(options0, options2);
      assertSame(options0, options1);
      assertSame(options1, options2);
      assertSame(options1, options0);
      assertSame(options2, options1);
      assertSame(options2, options0);
      assertNotNull(options2);
      
      // Undeclared exception!
      try { 
        options2.addOption("usage: ", "Xr5K#1W>fwWt30");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The option 'usage: ' contains an illegal character : ':'
         //
         verifyException("org.apache.commons.cli.OptionValidator", e);
      }
  }

  @Test(timeout = 4000)
  public void test35()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      StringWriter stringWriter0 = new StringWriter(3);
      StringWriter stringWriter1 = stringWriter0.append((CharSequence) " ");
      StringBuffer stringBuffer0 = stringWriter1.getBuffer();
      stringBuffer0.append((CharSequence) "usage: ");
      helpFormatter0.renderWrappedText(stringBuffer0, 1652, 1652, "");
      assertEquals(8, stringBuffer0.length());
      
      HelpFormatter helpFormatter1 = new HelpFormatter();
      helpFormatter1.setLongOptPrefix("");
      String string0 = helpFormatter1.rtrim("\n");
      assertEquals("", string0);
  }

  @Test(timeout = 4000)
  public void test36()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      StringWriter stringWriter0 = new StringWriter(3);
      StringWriter stringWriter1 = stringWriter0.append((CharSequence) " ");
      StringBuffer stringBuffer0 = stringWriter1.getBuffer();
      int int0 = 2;
      helpFormatter0.renderWrappedText(stringBuffer0, 1652, 2, " ");
      MockFile mockFile0 = new MockFile(".Gf&*@abW?-)l[o'[b", "usage: ");
      MockPrintWriter mockPrintWriter0 = new MockPrintWriter(mockFile0);
      String string0 = "<3";
      String string1 = "org.apache.commons.cli.HelpFormatter$OptionComparator";
      Options options0 = new Options();
      // Undeclared exception!
      try { 
        options0.addOption(",E", "");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The option ',E' contains an illegal character : ','
         //
         verifyException("org.apache.commons.cli.OptionValidator", e);
      }
  }

  @Test(timeout = 4000)
  public void test37()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      helpFormatter0.defaultLongOptPrefix = "";
      helpFormatter0.setNewLine("");
      helpFormatter0.setWidth((-1130));
      helpFormatter0.getLongOptPrefix();
      HelpFormatter helpFormatter1 = new HelpFormatter();
      Comparator<Option> comparator0 = helpFormatter1.getOptionComparator();
      helpFormatter0.optionComparator = comparator0;
      // Undeclared exception!
      try { 
        helpFormatter0.renderWrappedText((StringBuffer) null, (-1130), (-1130), "");
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
      }
  }

  @Test(timeout = 4000)
  public void test38()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      helpFormatter0.getNewLine();
      helpFormatter0.setDescPadding((-699));
      assertEquals((-699), helpFormatter0.defaultDescPad);
  }

  @Test(timeout = 4000)
  public void test39()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      helpFormatter0.getNewLine();
      Options options0 = new Options();
      String string0 = "";
      Options options1 = options0.addRequiredOption("", "cmdLineSyntax not provided", false, "arg");
      Option option0 = null;
      // Undeclared exception!
      try { 
        options1.addOption((Option) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.cli.Options", e);
      }
  }

  @Test(timeout = 4000)
  public void test40()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      int int0 = (-2319);
      helpFormatter0.setWidth((-2319));
      String string0 = "";
      MockFile mockFile0 = new MockFile("");
      MockPrintStream mockPrintStream0 = null;
      try {
        mockPrintStream0 = new MockPrintStream(mockFile0);
        fail("Expecting exception: FileNotFoundException");
      
      } catch(Throwable e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.evosuite.runtime.mock.java.io.MockFileOutputStream", e);
      }
  }

  @Test(timeout = 4000)
  public void test41()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      helpFormatter0.setNewLine("Cc]e");
      helpFormatter0.setWidth((-1130));
      helpFormatter0.getLongOptPrefix();
      HelpFormatter helpFormatter1 = new HelpFormatter();
      helpFormatter0.getOptionComparator();
      helpFormatter0.getLongOptSeparator();
      // Undeclared exception!
      try { 
        helpFormatter0.createPadding((-1022));
        fail("Expecting exception: NegativeArraySizeException");
      
      } catch(NegativeArraySizeException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.cli.HelpFormatter", e);
      }
  }

  @Test(timeout = 4000)
  public void test42()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      String string0 = "[*<=[";
      helpFormatter0.createPadding(0);
      int int0 = 1322;
      helpFormatter0.createPadding(0);
      // Undeclared exception!
      try { 
        helpFormatter0.printHelp(1322, ">,gCQm'?p~@Zq`n", ">,gCQm'?p~@Zq`n", (Options) null, "[*<=[");
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.cli.HelpFormatter", e);
      }
  }

  @Test(timeout = 4000)
  public void test43()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      helpFormatter0.defaultArgName = "arg";
      helpFormatter0.getLongOptPrefix();
      helpFormatter0.setSyntaxPrefix("--");
      helpFormatter0.getOptionComparator();
      int int0 = (-2);
      MockPrintWriter mockPrintWriter0 = new MockPrintWriter("arg");
      String string0 = " :: ";
      // Undeclared exception!
      try { 
        helpFormatter0.printHelp((PrintWriter) mockPrintWriter0, (-901), "--", " :: ", (Options) null, 0, 727, "2vASk<u;/G=]", true);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.cli.HelpFormatter", e);
      }
  }

  @Test(timeout = 4000)
  public void test44()  throws Throwable  {
    Future<?> future = executor.submit(new Runnable(){ 
            @Override public void run() { 
        try {
          HelpFormatter helpFormatter0 = new HelpFormatter();
          int int0 = 0;
          helpFormatter0.createPadding(0);
          Options options0 = new Options();
          Option option0 = new Option("", "<Sr3sm]{'", true, "<Sr3sm]{'");
          Options options1 = options0.addOption(option0);
          helpFormatter0.printHelp("[*=Y", "[*=Y", options1, "<Sr3sm]{'", true);
          helpFormatter0.setLeftPadding(1783);
          FileDescriptor fileDescriptor0 = new FileDescriptor();
          FileDescriptor fileDescriptor1 = new FileDescriptor();
          MockFileOutputStream mockFileOutputStream0 = null;
          try {
            mockFileOutputStream0 = new MockFileOutputStream(fileDescriptor1);
            fail("Expecting exception: SecurityException");
          
          } catch(SecurityException e) {
             //
             // Security manager blocks (\"java.lang.RuntimePermission\" \"writeFileDescriptor\")
             // java.lang.Thread.getStackTrace(Thread.java:1559)
             // org.evosuite.runtime.sandbox.MSecurityManager.checkPermission(MSecurityManager.java:424)
             // java.lang.SecurityManager.checkWrite(SecurityManager.java:954)
             // java.io.FileOutputStream.<init>(FileOutputStream.java:245)
             // org.evosuite.runtime.mock.java.io.MockFileOutputStream.<init>(MockFileOutputStream.java:114)
             // sun.reflect.GeneratedConstructorAccessor41.newInstance(Unknown Source)
             // sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
             // java.lang.reflect.Constructor.newInstance(Constructor.java:423)
             // org.evosuite.testcase.statements.ConstructorStatement$1.execute(ConstructorStatement.java:218)
             // org.evosuite.testcase.statements.AbstractStatement.exceptionHandler(AbstractStatement.java:165)
             // org.evosuite.testcase.statements.ConstructorStatement.execute(ConstructorStatement.java:173)
             // org.evosuite.testcase.execution.TestRunnable.executeStatements(TestRunnable.java:286)
             // org.evosuite.testcase.execution.TestRunnable.call(TestRunnable.java:192)
             // org.evosuite.testcase.execution.TestRunnable.call(TestRunnable.java:49)
             // java.util.concurrent.FutureTask.run(FutureTask.java:266)
             // java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
             // java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
             // java.lang.Thread.run(Thread.java:748)
             //
             verifyException("org.evosuite.runtime.sandbox.MSecurityManager", e);
          }
        } catch(Throwable t) {
            // Need to catch declared exceptions
        }
      } 
    });
    future.get(4000, TimeUnit.MILLISECONDS);
  }

  @Test(timeout = 4000)
  public void test45()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      Options options0 = new Options();
      helpFormatter0.setNewLine("4o,\"k6u3}uB2u0");
      helpFormatter0.setWidth(9);
      helpFormatter0.getLongOptPrefix();
      HelpFormatter helpFormatter1 = new HelpFormatter();
      helpFormatter1.getOptionComparator();
      helpFormatter1.printHelp(" cJ*_cy_@A*\"|3<x7", options0);
      // Undeclared exception!
      try { 
        helpFormatter0.renderWrappedText((StringBuffer) null, 2, (-466), "4o,\"k6u3}uB2u0");
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.cli.HelpFormatter", e);
      }
  }

  @Test(timeout = 4000)
  public void test46()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      // Undeclared exception!
      try { 
        helpFormatter0.printUsage((PrintWriter) null, 61, "");
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.cli.HelpFormatter", e);
      }
  }

  @Test(timeout = 4000)
  public void test47()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      helpFormatter0.setOptPrefix((String) null);
      helpFormatter0.setLongOptSeparator("");
      helpFormatter0.getOptPrefix();
      String string0 = "org.apache.commons.cli.HelpFormatter$OptionComparator";
      Options options0 = new Options();
      // Undeclared exception!
      try { 
        options0.addRequiredOption("--", (String) null, false, "org.apache.commons.cli.Option$Builder");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The option '--' contains an illegal character : '-'
         //
         verifyException("org.apache.commons.cli.OptionValidator", e);
      }
  }

  @Test(timeout = 4000)
  public void test48()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      Options options0 = new Options();
      options0.getOption("-");
      // Undeclared exception!
      try { 
        helpFormatter0.printHelp("", options0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // cmdLineSyntax not provided
         //
         verifyException("org.apache.commons.cli.HelpFormatter", e);
      }
  }

  @Test(timeout = 4000)
  public void test49()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      helpFormatter0.defaultArgName = "arg";
      helpFormatter0.setOptPrefix("arg");
      helpFormatter0.getLongOptPrefix();
      helpFormatter0.setSyntaxPrefix("--");
      helpFormatter0.getOptionComparator();
      int int0 = (-2);
      MockPrintWriter mockPrintWriter0 = new MockPrintWriter("<");
      String string0 = " :: ";
      // Undeclared exception!
      try { 
        helpFormatter0.printHelp((PrintWriter) mockPrintWriter0, (-885), "--", " :: ", (Options) null, 0, 727, "", true);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.cli.HelpFormatter", e);
      }
  }

  @Test(timeout = 4000)
  public void test50()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      helpFormatter0.getLongOptPrefix();
      Options options0 = new Options();
      // Undeclared exception!
      try { 
        options0.addOption("-", false, "");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Illegal option name '-'
         //
         verifyException("org.apache.commons.cli.OptionValidator", e);
      }
  }

  @Test(timeout = 4000)
  public void test51()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      helpFormatter0.setNewLine("Yraxl5~9Q1");
      helpFormatter0.setSyntaxPrefix("Yraxl5~9Q1");
      helpFormatter0.setLeftPadding(0);
      helpFormatter0.getLongOptPrefix();
      assertEquals("Yraxl5~9Q1", helpFormatter0.getNewLine());
  }

  @Test(timeout = 4000)
  public void test52()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      Comparator<Option> comparator0 = helpFormatter0.optionComparator;
      helpFormatter0.setOptionComparator(comparator0);
      assertEquals(1, helpFormatter0.defaultLeftPad);
      
      helpFormatter0.setLeftPadding(0);
      assertEquals(0, helpFormatter0.defaultLeftPad);
  }

  @Test(timeout = 4000)
  public void test53()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      helpFormatter0.setDescPadding((-40));
      assertEquals((-40), helpFormatter0.defaultDescPad);
  }

  @Test(timeout = 4000)
  public void test54()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      String string0 = "IUh7QSfA?!4[G71O9";
      String string1 = "";
      Options options0 = new Options();
      // Undeclared exception!
      try { 
        options0.addRequiredOption("-", " ", false, "IUh7QSfA?!4[G71O9");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Illegal option name '-'
         //
         verifyException("org.apache.commons.cli.OptionValidator", e);
      }
  }
}
