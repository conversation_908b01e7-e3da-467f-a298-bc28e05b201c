/*
 * This file was automatically generated by EvoSuite
 * Wed Jun 18 09:01:27 GMT 2025
 */

package software.amazon.event.ruler.input;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.junit.runner.RunWith;
import software.amazon.event.ruler.input.MultiByte;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true) 
public class MultiByte_ESTest extends MultiByte_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      byte[] byteArray0 = new byte[1];
      byteArray0[0] = (byte)56;
      MultiByte multiByte0 = new MultiByte(byteArray0);
      boolean boolean0 = multiByte0.isNumeric();
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      byte[] byteArray0 = new byte[1];
      byteArray0[0] = (byte)56;
      MultiByte multiByte0 = new MultiByte(byteArray0);
      byte byte0 = multiByte0.singular();
      assertEquals((byte)56, byte0);
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      byte[] byteArray0 = new byte[1];
      byteArray0[0] = (byte) (-121);
      MultiByte multiByte0 = new MultiByte(byteArray0);
      byte byte0 = multiByte0.singular();
      assertEquals((byte) (-121), byte0);
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      byte[] byteArray0 = new byte[2];
      MultiByte multiByte0 = new MultiByte(byteArray0);
      byte[] byteArray1 = new byte[2];
      byteArray1[0] = (byte) (-128);
      MultiByte multiByte1 = new MultiByte(byteArray1);
      boolean boolean0 = multiByte0.isLessThan(multiByte1);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      byte[] byteArray0 = new byte[1];
      MultiByte multiByte0 = new MultiByte(byteArray0);
      boolean boolean0 = multiByte0.isLessThan(multiByte0);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      byte[] byteArray0 = new byte[1];
      MultiByte multiByte0 = new MultiByte(byteArray0);
      byte[] byteArray1 = new byte[6];
      boolean boolean0 = multiByte0.is(byteArray1);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      byte[] byteArray0 = new byte[7];
      MultiByte multiByte0 = new MultiByte(byteArray0);
      // Undeclared exception!
      try { 
        multiByte0.isLessThanOrEqualTo((MultiByte) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("software.amazon.event.ruler.input.MultiByte", e);
      }
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      byte[] byteArray0 = new byte[1];
      MultiByte multiByte0 = new MultiByte(byteArray0);
      // Undeclared exception!
      try { 
        multiByte0.isGreaterThan((MultiByte) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("software.amazon.event.ruler.input.MultiByte", e);
      }
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      MultiByte multiByte0 = null;
      try {
        multiByte0 = new MultiByte((byte[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("software.amazon.event.ruler.input.MultiByte", e);
      }
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      byte[] byteArray0 = new byte[1];
      MultiByte multiByte0 = new MultiByte(byteArray0);
      // Undeclared exception!
      try { 
        multiByte0.isLessThan((MultiByte) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("software.amazon.event.ruler.input.MultiByte", e);
      }
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      byte[] byteArray0 = new byte[1];
      MultiByte multiByte0 = new MultiByte(byteArray0);
      byte[] byteArray1 = multiByte0.getBytes();
      assertNotSame(byteArray0, byteArray1);
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      byte[] byteArray0 = new byte[1];
      MultiByte multiByte0 = new MultiByte(byteArray0);
      boolean boolean0 = multiByte0.equals(multiByte0);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      byte[] byteArray0 = new byte[2];
      MultiByte multiByte0 = new MultiByte(byteArray0);
      Object object0 = new Object();
      boolean boolean0 = multiByte0.equals(object0);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      byte[] byteArray0 = new byte[4];
      MultiByte multiByte0 = new MultiByte(byteArray0);
      boolean boolean0 = multiByte0.equals((Object) null);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      byte[] byteArray0 = new byte[6];
      MultiByte multiByte0 = new MultiByte(byteArray0);
      byte[] byteArray1 = new byte[1];
      byteArray1[0] = (byte) (-65);
      MultiByte multiByte1 = new MultiByte(byteArray1);
      boolean boolean0 = multiByte0.isLessThanOrEqualTo(multiByte1);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      byte[] byteArray0 = new byte[14];
      MultiByte multiByte0 = new MultiByte(byteArray0);
      byte[] byteArray1 = new byte[1];
      byteArray1[0] = (byte) (-65);
      MultiByte multiByte1 = new MultiByte(byteArray1);
      boolean boolean0 = multiByte1.isLessThanOrEqualTo(multiByte0);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      byte[] byteArray0 = new byte[1];
      MultiByte multiByte0 = new MultiByte(byteArray0);
      boolean boolean0 = multiByte0.isGreaterThanOrEqualTo(multiByte0);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      byte[] byteArray0 = new byte[1];
      byte[] byteArray1 = new byte[5];
      MultiByte multiByte0 = new MultiByte(byteArray0);
      MultiByte multiByte1 = new MultiByte(byteArray1);
      boolean boolean0 = multiByte0.isGreaterThanOrEqualTo(multiByte1);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      byte[] byteArray0 = new byte[11];
      MultiByte multiByte0 = new MultiByte(byteArray0);
      byte[] byteArray1 = new byte[5];
      MultiByte multiByte1 = new MultiByte(byteArray1);
      boolean boolean0 = multiByte0.isGreaterThan(multiByte1);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      byte[] byteArray0 = new byte[1];
      byteArray0[0] = (byte)57;
      MultiByte multiByte0 = new MultiByte(byteArray0);
      boolean boolean0 = multiByte0.isNumeric();
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      byte[] byteArray0 = new byte[1];
      byteArray0[0] = (byte)127;
      MultiByte multiByte0 = new MultiByte(byteArray0);
      boolean boolean0 = multiByte0.isNumeric();
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      byte[] byteArray0 = new byte[1];
      MultiByte multiByte0 = new MultiByte(byteArray0);
      boolean boolean0 = multiByte0.isNumeric();
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test22()  throws Throwable  {
      byte[] byteArray0 = new byte[8];
      MultiByte multiByte0 = new MultiByte(byteArray0);
      boolean boolean0 = multiByte0.isNumeric();
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test23()  throws Throwable  {
      byte[] byteArray0 = new byte[8];
      MultiByte multiByte0 = new MultiByte(byteArray0);
      // Undeclared exception!
      try { 
        multiByte0.singular();
        fail("Expecting exception: IllegalStateException");
      
      } catch(IllegalStateException e) {
         //
         // Must be a singular byte
         //
         verifyException("software.amazon.event.ruler.input.MultiByte", e);
      }
  }

  @Test(timeout = 4000)
  public void test24()  throws Throwable  {
      byte[] byteArray0 = new byte[1];
      MultiByte multiByte0 = new MultiByte(byteArray0);
      byte byte0 = multiByte0.singular();
      assertEquals((byte)0, byte0);
  }

  @Test(timeout = 4000)
  public void test25()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      MultiByte multiByte0 = null;
      try {
        multiByte0 = new MultiByte(byteArray0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Must provide at least one byte
         //
         verifyException("software.amazon.event.ruler.input.MultiByte", e);
      }
  }

  @Test(timeout = 4000)
  public void test26()  throws Throwable  {
      byte[] byteArray0 = new byte[1];
      MultiByte multiByte0 = new MultiByte(byteArray0);
      boolean boolean0 = multiByte0.is(byteArray0);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test27()  throws Throwable  {
      byte[] byteArray0 = new byte[17];
      MultiByte multiByte0 = new MultiByte(byteArray0);
      boolean boolean0 = multiByte0.isGreaterThan(multiByte0);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test28()  throws Throwable  {
      byte[] byteArray0 = new byte[1];
      MultiByte multiByte0 = new MultiByte(byteArray0);
      String string0 = multiByte0.toString();
      assertEquals("[0]", string0);
  }

  @Test(timeout = 4000)
  public void test29()  throws Throwable  {
      byte[] byteArray0 = new byte[1];
      MultiByte multiByte0 = new MultiByte(byteArray0);
      // Undeclared exception!
      try { 
        multiByte0.isGreaterThanOrEqualTo((MultiByte) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("software.amazon.event.ruler.input.MultiByte", e);
      }
  }

  @Test(timeout = 4000)
  public void test30()  throws Throwable  {
      byte[] byteArray0 = new byte[1];
      MultiByte multiByte0 = new MultiByte(byteArray0);
      multiByte0.hashCode();
  }
}
