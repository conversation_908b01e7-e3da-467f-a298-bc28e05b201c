/*
 * This file was automatically generated by EvoSuite
 * Wed Jun 18 07:47:45 GMT 2025
 */

package org.apache.commons.cli;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import org.apache.commons.cli.Option;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true) 
public class Option_ESTest extends Option_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      Option option0 = new Option("g", "g");
      option0.setArgs((-73));
      option0.toString();
      assertEquals((-73), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      Option option0 = new Option("", "");
      assertEquals((-1), option0.getArgs());
      
      option0.setArgs(0);
      option0.toString();
      assertEquals(0, option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder();
      Option.Builder option_Builder1 = option_Builder0.required(false);
      assertSame(option_Builder1, option_Builder0);
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder((String) null);
      Option.Builder option_Builder1 = option_Builder0.valueSeparator();
      Option.Builder option_Builder2 = option_Builder1.longOpt("IBoV$");
      option_Builder0.numberOfArgs(2304);
      Option option0 = option_Builder2.build();
      option0.addValueForProcessing("uPmIm=TVHw|wIU?");
      assertEquals(2304, option0.getArgs());
      assertEquals("uPmIm", option0.getValue());
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder((String) null);
      option_Builder0.hasArg(true);
      Option.Builder option_Builder1 = option_Builder0.longOpt("IBoV$");
      Option option0 = option_Builder1.build();
      boolean boolean0 = option0.requiresArg();
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder((String) null);
      Option.Builder option_Builder1 = option_Builder0.required();
      option_Builder1.longOpt("IBoV$");
      Option option0 = option_Builder1.build();
      boolean boolean0 = option0.isRequired();
      assertTrue(boolean0);
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      Option option0 = new Option("", "", true, "|");
      option0.setOptionalArg(true);
      boolean boolean0 = option0.hasOptionalArg();
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder((String) null);
      option_Builder0.hasArg(true);
      Option.Builder option_Builder1 = option_Builder0.longOpt("IBoV$");
      Option option0 = option_Builder1.build();
      option0.addValueForProcessing("/XozvJUCM|R?)1gq/2Y");
      option0.getValuesList();
      assertFalse(option0.hasValueSeparator());
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      Option option0 = new Option("", "", true, "|");
      option0.setValueSeparator('1');
      char char0 = option0.getValueSeparator();
      assertEquals('1', char0);
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      Option option0 = new Option("", "");
      option0.setValueSeparator('h');
      char char0 = option0.getValueSeparator();
      assertEquals('h', char0);
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder((String) null);
      option_Builder0.longOpt("IBoV$");
      Option option0 = option_Builder0.build();
      option0.getValue((String) null);
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      Option option0 = new Option((String) null, (String) null);
      option0.getOpt();
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      Option option0 = new Option("NO_ARGS_ALLOWED", "|@`!", true, "e&ZsXAriG'");
      option0.getOpt();
      assertEquals("|@`!", option0.getLongOpt());
      assertEquals("e&ZsXAriG'", option0.getDescription());
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      Option option0 = new Option("", false, "");
      String string0 = option0.getLongOpt();
      assertNull(string0);
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      Option option0 = new Option((String) null, "7fC)k8&$BN=7", true, "J>Es");
      String string0 = option0.getLongOpt();
      assertEquals("J>Es", option0.getDescription());
      assertEquals("7fC)k8&$BN=7", string0);
      assertNotNull(string0);
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      Option option0 = new Option((String) null, false, "uPmIm=TVHw|wIU?");
      String string0 = option0.getKey();
      assertEquals((-1), option0.getArgs());
      assertNull(string0);
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      Option option0 = new Option("", "", false, "|");
      option0.getKey();
      assertEquals("", option0.getLongOpt());
      assertEquals((-1), option0.getArgs());
      assertEquals("|", option0.getDescription());
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder((String) null);
      option_Builder0.longOpt("IBoV$");
      Option option0 = option_Builder0.build();
      option0.getDescription();
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      Option option0 = new Option("", false, "The addValue method is not intended for client use. Subclasses should use the addValueForProcessing method instead. ");
      String string0 = option0.getDescription();
      assertFalse(option0.hasLongOpt());
      assertNotNull(string0);
      assertEquals((-1), option0.getArgs());
      assertEquals("The addValue method is not intended for client use. Subclasses should use the addValueForProcessing method instead. ", string0);
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder((String) null);
      option_Builder0.hasArg(true);
      option_Builder0.longOpt("IBoV$");
      Option option0 = option_Builder0.build();
      int int0 = option0.getArgs();
      assertEquals(1, int0);
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      Option option0 = new Option("", "", true, "|");
      option0.setArgName("|");
      option0.getArgName();
      assertEquals("|", option0.getDescription());
      assertEquals("", option0.getLongOpt());
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      Option option0 = new Option((String) null, false, (String) null);
      option0.setArgName("");
      option0.getArgName();
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test22()  throws Throwable  {
      Option option0 = new Option("h", "h", true, "");
      option0.acceptsArg();
      assertEquals("h", option0.getLongOpt());
      assertEquals("", option0.getDescription());
  }

  @Test(timeout = 4000)
  public void test23()  throws Throwable  {
      Option option0 = new Option("", true, "");
      option0.addValueForProcessing("");
      try { 
        option0.getValue(1696);
        fail("Expecting exception: IndexOutOfBoundsException");
      
      } catch(IndexOutOfBoundsException e) {
         //
         // Index: 1696, Size: 1
         //
         verifyException("java.util.ArrayList", e);
      }
  }

  @Test(timeout = 4000)
  public void test24()  throws Throwable  {
      Option option0 = new Option("", "", true, "");
      option0.addValueForProcessing("");
      try { 
        option0.getValue((-5));
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test25()  throws Throwable  {
      Option option0 = new Option("", "");
      // Undeclared exception!
      try { 
        option0.getId();
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
      }
  }

  @Test(timeout = 4000)
  public void test26()  throws Throwable  {
      Option option0 = new Option((String) null, (String) null, true, "&3:To8@)N");
      // Undeclared exception!
      try { 
        option0.getId();
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.cli.Option", e);
      }
  }

  @Test(timeout = 4000)
  public void test27()  throws Throwable  {
      // Undeclared exception!
      try { 
        Option.builder("=odw\"VAZ4= ,u;F");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The option '=odw\"VAZ4= ,u;F' contains an illegal character : '='
         //
         verifyException("org.apache.commons.cli.OptionValidator", e);
      }
  }

  @Test(timeout = 4000)
  public void test28()  throws Throwable  {
      Option option0 = new Option("", "", true, "|");
      option0.setValueSeparator('1');
      // Undeclared exception!
      try { 
        option0.addValueForProcessing((String) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.cli.Option", e);
      }
  }

  @Test(timeout = 4000)
  public void test29()  throws Throwable  {
      Option option0 = null;
      try {
        option0 = new Option("8ID1Z?", false, "8ID1Z?");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The option '8ID1Z?' contains an illegal character : '?'
         //
         verifyException("org.apache.commons.cli.OptionValidator", e);
      }
  }

  @Test(timeout = 4000)
  public void test30()  throws Throwable  {
      Option option0 = null;
      try {
        option0 = new Option(" ]", " ]", true, "rY`.\"o<");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The option ' ]' contains an illegal character : ' '
         //
         verifyException("org.apache.commons.cli.OptionValidator", e);
      }
  }

  @Test(timeout = 4000)
  public void test31()  throws Throwable  {
      Option option0 = null;
      try {
        option0 = new Option("-[d8>,3sR[ba9\"Q", "-[d8>,3sR[ba9\"Q");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The option '-[d8>,3sR[ba9\"Q' contains an illegal character : '-'
         //
         verifyException("org.apache.commons.cli.OptionValidator", e);
      }
  }

  @Test(timeout = 4000)
  public void test32()  throws Throwable  {
      Option option0 = new Option("g", "g");
      boolean boolean0 = option0.hasValueSeparator();
      assertFalse(boolean0);
      assertFalse(option0.hasLongOpt());
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test33()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder((String) null);
      option_Builder0.valueSeparator('/');
      option_Builder0.longOpt("IBoV$");
      Option option0 = option_Builder0.build();
      boolean boolean0 = option0.hasValueSeparator();
      assertEquals('/', option0.getValueSeparator());
      assertEquals((-1), option0.getArgs());
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test34()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder((String) null);
      Option.Builder option_Builder1 = option_Builder0.longOpt("IBoV$");
      option_Builder0.numberOfArgs(2304);
      Option option0 = option_Builder1.build();
      boolean boolean0 = option0.hasArgs();
      assertTrue(boolean0);
      assertEquals(2304, option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test35()  throws Throwable  {
      Option option0 = new Option("", "", false, "|");
      boolean boolean0 = option0.hasArgs();
      assertFalse(boolean0);
      assertEquals((-1), option0.getArgs());
      assertEquals("|", option0.getDescription());
      assertEquals("", option0.getLongOpt());
  }

  @Test(timeout = 4000)
  public void test36()  throws Throwable  {
      Option option0 = new Option("", "");
      boolean boolean0 = option0.hasArg();
      assertEquals((-1), option0.getArgs());
      assertFalse(option0.hasLongOpt());
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test37()  throws Throwable  {
      Option option0 = new Option("", "", true, "|");
      option0.hasArg();
      assertEquals("|", option0.getDescription());
      assertEquals("", option0.getLongOpt());
  }

  @Test(timeout = 4000)
  public void test38()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder("");
      Option.Builder option_Builder1 = option_Builder0.hasArgs();
      Option option0 = option_Builder1.build();
      boolean boolean0 = option0.hasArg();
      assertEquals((-2), option0.getArgs());
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test39()  throws Throwable  {
      Option option0 = new Option("", "");
      option0.getValue();
      assertFalse(option0.hasLongOpt());
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test40()  throws Throwable  {
      Option option0 = new Option("", "");
      option0.getValueSeparator();
      assertFalse(option0.hasLongOpt());
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test41()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder((String) null);
      Option.Builder option_Builder1 = option_Builder0.longOpt("IBoV$");
      Option option0 = option_Builder1.build();
      option0.hasOptionalArg();
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test42()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder((String) null);
      Option.Builder option_Builder1 = option_Builder0.option("IBoV$");
      assertSame(option_Builder1, option_Builder0);
  }

  @Test(timeout = 4000)
  public void test43()  throws Throwable  {
      Option option0 = new Option("", "", true, "|");
      option0.setType((Object) null);
      String string0 = option0.toString();
      assertEquals("[ option:    [ARG] :: | ]", string0);
  }

  @Test(timeout = 4000)
  public void test44()  throws Throwable  {
      Option option0 = new Option("", "");
      assertFalse(option0.hasLongOpt());
      
      option0.setLongOpt("");
      String string0 = option0.toString();
      assertEquals("[ option:    ::  :: class java.lang.String ]", string0);
  }

  @Test(timeout = 4000)
  public void test45()  throws Throwable  {
      Option option0 = new Option("", false, "");
      option0.setOptionalArg(true);
      boolean boolean0 = option0.requiresArg();
      assertTrue(option0.hasOptionalArg());
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test46()  throws Throwable  {
      Option option0 = new Option("", false, "");
      boolean boolean0 = option0.requiresArg();
      assertFalse(boolean0);
      assertFalse(option0.hasLongOpt());
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test47()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder((String) null);
      Option.Builder option_Builder1 = option_Builder0.valueSeparator();
      option_Builder1.hasArg(true);
      Option.Builder option_Builder2 = option_Builder1.longOpt("IBoV$");
      Option option0 = option_Builder2.build();
      option0.addValueForProcessing("uPmIm=TVHw|wIU?");
      assertEquals('=', option0.getValueSeparator());
      assertTrue(option0.hasValueSeparator());
  }

  @Test(timeout = 4000)
  public void test48()  throws Throwable  {
      Option option0 = new Option("", "", false, "");
      boolean boolean0 = option0.hasLongOpt();
      assertEquals((-1), option0.getArgs());
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test49()  throws Throwable  {
      Option option0 = new Option("", "");
      boolean boolean0 = option0.hasLongOpt();
      assertFalse(boolean0);
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test50()  throws Throwable  {
      Option option0 = new Option("", "", true, "");
      option0.setArgName("");
      boolean boolean0 = option0.hasArgName();
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test51()  throws Throwable  {
      Option option0 = new Option("", "", true, "|");
      option0.setArgName("|");
      boolean boolean0 = option0.hasArgName();
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test52()  throws Throwable  {
      Option option0 = new Option("", "", true, "");
      boolean boolean0 = option0.hasArgName();
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test53()  throws Throwable  {
      Option option0 = new Option("", "", true, (String) null);
      String[] stringArray0 = option0.getValues();
      assertTrue(option0.hasLongOpt());
      assertNull(stringArray0);
  }

  @Test(timeout = 4000)
  public void test54()  throws Throwable  {
      Option option0 = new Option("", true, "");
      option0.addValueForProcessing("");
      String[] stringArray0 = option0.getValues();
      assertNotNull(stringArray0);
      assertFalse(option0.hasLongOpt());
      assertFalse(option0.hasValueSeparator());
  }

  @Test(timeout = 4000)
  public void test55()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder((String) null);
      option_Builder0.hasArg(true);
      Option.Builder option_Builder1 = option_Builder0.longOpt("IBoV$");
      Option option0 = option_Builder1.build();
      option0.addValueForProcessing("/XozvJUCM|R?)1gq/2Y");
      String string0 = option0.getValue(" ]");
      assertEquals("/XozvJUCM|R?)1gq/2Y", string0);
      assertFalse(option0.hasValueSeparator());
  }

  @Test(timeout = 4000)
  public void test56()  throws Throwable  {
      Option option0 = new Option("", "");
      option0.getValue((-2));
      assertFalse(option0.hasLongOpt());
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test57()  throws Throwable  {
      Option option0 = new Option("", "", true, "|");
      assertEquals(1, option0.getArgs());
      
      option0.addValueForProcessing("-j%sgwA.GM");
      String string0 = option0.getValue(0);
      assertFalse(option0.hasValueSeparator());
      assertNotNull(string0);
      assertEquals("|", option0.getDescription());
      assertEquals("", option0.getLongOpt());
  }

  @Test(timeout = 4000)
  public void test58()  throws Throwable  {
      Option option0 = new Option("", false, "");
      String string0 = option0.getValue("");
      assertFalse(option0.hasLongOpt());
      assertEquals((-1), option0.getArgs());
      assertNotNull(string0);
  }

  @Test(timeout = 4000)
  public void test59()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder((String) null);
      option_Builder0.hasArg(true);
      Option.Builder option_Builder1 = option_Builder0.longOpt("IBoV$");
      Option option0 = option_Builder1.build();
      option0.addValueForProcessing("/XozvJUCM|R?)1gq/2Y");
      String string0 = option0.getValue();
      assertNotNull(string0);
      assertFalse(option0.hasValueSeparator());
  }

  @Test(timeout = 4000)
  public void test60()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder((String) null);
      Option.Builder option_Builder1 = option_Builder0.longOpt("IBoV$");
      Option option0 = option_Builder1.build();
      option0.getKey();
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test61()  throws Throwable  {
      Option option0 = new Option("J", "");
      Option option1 = new Option("", "");
      boolean boolean0 = option0.equals(option1);
      assertEquals("", option0.getDescription());
      assertFalse(boolean0);
      assertEquals((-1), option1.getArgs());
      assertFalse(option1.hasLongOpt());
  }

  @Test(timeout = 4000)
  public void test62()  throws Throwable  {
      Option option0 = new Option("", "", true, (String) null);
      Option option1 = new Option("", (String) null);
      boolean boolean0 = option1.equals(option0);
      assertEquals((-1), option1.getArgs());
      assertTrue(option0.hasArg());
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test63()  throws Throwable  {
      Option option0 = new Option("", "", true, (String) null);
      Option option1 = (Option)option0.clone();
      boolean boolean0 = option0.equals(option1);
      assertTrue(option1.hasArg());
      assertTrue(option1.hasLongOpt());
      assertTrue(boolean0);
      assertNotSame(option1, option0);
  }

  @Test(timeout = 4000)
  public void test64()  throws Throwable  {
      Option option0 = new Option("", "");
      boolean boolean0 = option0.equals(option0);
      assertEquals((-1), option0.getArgs());
      assertFalse(option0.hasLongOpt());
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test65()  throws Throwable  {
      Option option0 = new Option("", false, "");
      // Undeclared exception!
      try { 
        option0.addValueForProcessing("");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // NO_ARGS_ALLOWED
         //
         verifyException("org.apache.commons.cli.Option", e);
      }
  }

  @Test(timeout = 4000)
  public void test66()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder((String) null);
      option_Builder0.hasArg(true);
      Option.Builder option_Builder1 = option_Builder0.longOpt("IBoV$");
      Option option0 = option_Builder1.build();
      option0.addValueForProcessing("?##W");
      // Undeclared exception!
      try { 
        option0.addValueForProcessing("uPmIm=TVHw|wIU?");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot add value, list full.
         //
         verifyException("org.apache.commons.cli.Option", e);
      }
  }

  @Test(timeout = 4000)
  public void test67()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder();
      option_Builder0.hasArgs();
      option_Builder0.longOpt("IBoV$");
      Option option0 = option_Builder0.build();
      option0.addValueForProcessing("");
      assertTrue(option0.hasArg());
      assertFalse(option0.hasValueSeparator());
      assertTrue(option0.hasArgs());
  }

  @Test(timeout = 4000)
  public void test68()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder();
      Option.Builder option_Builder1 = option_Builder0.optionalArg(true);
      assertSame(option_Builder1, option_Builder0);
  }

  @Test(timeout = 4000)
  public void test69()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder();
      Option.Builder option_Builder1 = option_Builder0.optionalArg(false);
      assertSame(option_Builder0, option_Builder1);
  }

  @Test(timeout = 4000)
  public void test70()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder((String) null);
      Option.Builder option_Builder1 = option_Builder0.hasArg(false);
      assertSame(option_Builder0, option_Builder1);
  }

  @Test(timeout = 4000)
  public void test71()  throws Throwable  {
      Option option0 = new Option("", "", false, "|");
      option0.getOpt();
      assertEquals((-1), option0.getArgs());
      assertEquals("", option0.getLongOpt());
      assertEquals("|", option0.getDescription());
  }

  @Test(timeout = 4000)
  public void test72()  throws Throwable  {
      Option option0 = new Option((String) null, (String) null);
      option0.getType();
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test73()  throws Throwable  {
      Option option0 = new Option("", "", true, "");
      // Undeclared exception!
      try { 
        option0.addValue("");
        fail("Expecting exception: UnsupportedOperationException");
      
      } catch(UnsupportedOperationException e) {
         //
         // The addValue method is not intended for client use. Subclasses should use the addValueForProcessing method instead. 
         //
         verifyException("org.apache.commons.cli.Option", e);
      }
  }

  @Test(timeout = 4000)
  public void test74()  throws Throwable  {
      Option option0 = new Option("", "", false, "|");
      String string0 = option0.getLongOpt();
      assertEquals("", string0);
      assertEquals((-1), option0.getArgs());
      assertEquals("|", option0.getDescription());
  }

  @Test(timeout = 4000)
  public void test75()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder();
      // Undeclared exception!
      try { 
        option_Builder0.build();
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Either opt or longOpt must be specified
         //
         verifyException("org.apache.commons.cli.Option$Builder", e);
      }
  }

  @Test(timeout = 4000)
  public void test76()  throws Throwable  {
      Option option0 = new Option("", false, "");
      option0.hashCode();
      assertFalse(option0.hasLongOpt());
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test77()  throws Throwable  {
      Option option0 = new Option("", false, "");
      option0.isRequired();
      assertFalse(option0.hasLongOpt());
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test78()  throws Throwable  {
      Option option0 = new Option("", false, "");
      String string0 = option0.getDescription();
      assertNotNull(string0);
      assertEquals((-1), option0.getArgs());
      assertFalse(option0.hasLongOpt());
  }

  @Test(timeout = 4000)
  public void test79()  throws Throwable  {
      Option option0 = new Option("", false, "");
      int int0 = option0.getArgs();
      assertFalse(option0.hasLongOpt());
      assertEquals((-1), int0);
  }

  @Test(timeout = 4000)
  public void test80()  throws Throwable  {
      Option option0 = new Option("", "", true, (String) null);
      option0.clearValues();
      assertTrue(option0.hasLongOpt());
      assertEquals(1, option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test81()  throws Throwable  {
      Option option0 = new Option("j0asC", "j0asC");
      boolean boolean0 = option0.acceptsArg();
      assertFalse(boolean0);
      assertFalse(option0.hasLongOpt());
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test82()  throws Throwable  {
      Option option0 = new Option("", "");
      option0.setDescription("");
      assertFalse(option0.hasLongOpt());
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test83()  throws Throwable  {
      Option option0 = new Option("I", "I");
      option0.getId();
      assertFalse(option0.hasLongOpt());
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test84()  throws Throwable  {
      Option option0 = new Option("", "", true, "|");
      option0.getValuesList();
      assertEquals("", option0.getLongOpt());
      assertEquals("|", option0.getDescription());
      assertEquals(1, option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test85()  throws Throwable  {
      Option option0 = new Option("", "");
      Class<Object> class0 = Object.class;
      option0.setType(class0);
      assertEquals((-1), option0.getArgs());
      assertFalse(option0.hasLongOpt());
  }

  @Test(timeout = 4000)
  public void test86()  throws Throwable  {
      Option option0 = new Option("", false, "");
      option0.setArgs((-1467));
      boolean boolean0 = option0.requiresArg();
      assertEquals((-1467), option0.getArgs());
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test87()  throws Throwable  {
      Option option0 = new Option("j0asC", "j0asC");
      option0.setRequired(false);
      assertFalse(option0.hasLongOpt());
      assertEquals((-1), option0.getArgs());
      assertFalse(option0.isRequired());
  }

  @Test(timeout = 4000)
  public void test88()  throws Throwable  {
      Option option0 = new Option("", "", true, "|");
      option0.getArgName();
      assertEquals("|", option0.getDescription());
      assertTrue(option0.hasArg());
      assertEquals("", option0.getLongOpt());
  }

  @Test(timeout = 4000)
  public void test89()  throws Throwable  {
      Option option0 = new Option("", "");
      // Undeclared exception!
      try { 
        option0.setType((Object) "");
        fail("Expecting exception: ClassCastException");
      
      } catch(ClassCastException e) {
         //
         // java.lang.String cannot be cast to java.lang.Class
         //
         verifyException("org.apache.commons.cli.Option", e);
      }
  }

  @Test(timeout = 4000)
  public void test90()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder("");
      Class<Object> class0 = Object.class;
      Option.Builder option_Builder1 = option_Builder0.type(class0);
      assertSame(option_Builder0, option_Builder1);
  }

  @Test(timeout = 4000)
  public void test91()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder("");
      Option.Builder option_Builder1 = option_Builder0.numberOfArgs(1614);
      Option option0 = option_Builder1.build();
      String string0 = option0.toString();
      assertEquals("[ option:  [ARG...] :: null :: class java.lang.String ]", string0);
      assertEquals(1614, option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test92()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder("");
      option_Builder0.hasArgs();
      Option option0 = option_Builder0.build();
      String string0 = option0.toString();
      assertEquals("[ option:  [ARG...] :: null :: class java.lang.String ]", string0);
  }

  @Test(timeout = 4000)
  public void test93()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder("");
      Option.Builder option_Builder1 = option_Builder0.hasArg();
      Option option0 = option_Builder1.build();
      assertTrue(option0.hasArg());
      
      option0.addValueForProcessing("");
      String string0 = option0.getValue();
      assertFalse(option0.hasValueSeparator());
      assertNotNull(string0);
  }

  @Test(timeout = 4000)
  public void test94()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder("");
      Option.Builder option_Builder1 = option_Builder0.desc((String) null);
      assertSame(option_Builder0, option_Builder1);
  }

  @Test(timeout = 4000)
  public void test95()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder((String) null);
      Option.Builder option_Builder1 = option_Builder0.argName((String) null);
      assertSame(option_Builder0, option_Builder1);
  }

  @Test(timeout = 4000)
  public void test96()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder((String) null);
      Option.Builder option_Builder1 = option_Builder0.longOpt("IBoV$");
      Option option0 = option_Builder1.build();
      boolean boolean0 = option0.equals(option_Builder0);
      assertFalse(boolean0);
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test97()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder((String) null);
      Option.Builder option_Builder1 = option_Builder0.valueSeparator();
      option_Builder1.hasArg(true);
      Option.Builder option_Builder2 = option_Builder1.longOpt("IBoV$");
      Option option0 = option_Builder2.build();
      option0.addValueForProcessing("?##W");
      // Undeclared exception!
      try { 
        option0.addValueForProcessing("uPmIm=TVHw|wIU?");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot add value, list full.
         //
         verifyException("org.apache.commons.cli.Option", e);
      }
  }
}
