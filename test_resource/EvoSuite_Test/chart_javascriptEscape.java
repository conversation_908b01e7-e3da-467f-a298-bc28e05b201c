/*
 * This file was automatically generated by <PERSON>voSuite
 * Wed Jun 18 07:24:52 GMT 2025
 */

package org.jfree.chart.imagemap;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.awt.Point;
import java.awt.Rectangle;
import java.awt.geom.Line2D;
import java.awt.geom.Rectangle2D;
import java.io.PrintWriter;
import java.io.StringWriter;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.evosuite.runtime.mock.java.io.MockFile;
import org.evosuite.runtime.mock.java.io.MockFileOutputStream;
import org.evosuite.runtime.mock.java.io.MockPrintWriter;
import org.jfree.chart.ChartRenderingInfo;
import org.jfree.chart.entity.ChartEntity;
import org.jfree.chart.entity.EntityCollection;
import org.jfree.chart.entity.StandardEntityCollection;
import org.jfree.chart.imagemap.DynamicDriveToolTipTagFragmentGenerator;
import org.jfree.chart.imagemap.ImageMapUtils;
import org.jfree.chart.imagemap.OverLIBToolTipTagFragmentGenerator;
import org.jfree.chart.imagemap.StandardToolTipTagFragmentGenerator;
import org.jfree.chart.imagemap.StandardURLTagFragmentGenerator;
import org.jfree.chart.imagemap.ToolTipTagFragmentGenerator;
import org.jfree.chart.imagemap.URLTagFragmentGenerator;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true) 
public class ImageMapUtils_ESTest extends ImageMapUtils_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      StandardEntityCollection standardEntityCollection0 = new StandardEntityCollection();
      ChartRenderingInfo chartRenderingInfo0 = new ChartRenderingInfo(standardEntityCollection0);
      Rectangle rectangle0 = new Rectangle();
      ChartEntity chartEntity0 = new ChartEntity(rectangle0, ":+p6Wco<AJ{[cM", ":+p6Wco<AJ{[cM");
      standardEntityCollection0.add(chartEntity0);
      standardEntityCollection0.add(chartEntity0);
      StandardURLTagFragmentGenerator standardURLTagFragmentGenerator0 = new StandardURLTagFragmentGenerator();
      OverLIBToolTipTagFragmentGenerator overLIBToolTipTagFragmentGenerator0 = new OverLIBToolTipTagFragmentGenerator();
      String string0 = ImageMapUtils.getImageMap(":+p6Wco<AJ{[cM", chartRenderingInfo0, (ToolTipTagFragmentGenerator) overLIBToolTipTagFragmentGenerator0, (URLTagFragmentGenerator) standardURLTagFragmentGenerator0);
      assertEquals("<map id=\":+p6Wco&lt;AJ{[cM\" name=\":+p6Wco&lt;AJ{[cM\">\n<area shape=\"rect\" coords=\"0,0,1,1\" onMouseOver=\"return overlib(':+p6Wco<AJ{[cM');\" onMouseOut=\"return nd();\" href=\":+p6Wco<AJ{[cM\"/>\n<area shape=\"rect\" coords=\"0,0,1,1\" onMouseOver=\"return overlib(':+p6Wco<AJ{[cM');\" onMouseOut=\"return nd();\" href=\":+p6Wco<AJ{[cM\"/>\n</map>", string0);
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      MockFile mockFile0 = new MockFile("\\", ">T");
      MockPrintWriter mockPrintWriter0 = new MockPrintWriter(mockFile0);
      StandardEntityCollection standardEntityCollection0 = new StandardEntityCollection();
      ChartRenderingInfo chartRenderingInfo0 = new ChartRenderingInfo(standardEntityCollection0);
      DynamicDriveToolTipTagFragmentGenerator dynamicDriveToolTipTagFragmentGenerator0 = new DynamicDriveToolTipTagFragmentGenerator();
      StandardURLTagFragmentGenerator standardURLTagFragmentGenerator0 = new StandardURLTagFragmentGenerator();
      ImageMapUtils.writeImageMap((PrintWriter) mockPrintWriter0, "", chartRenderingInfo0, (ToolTipTagFragmentGenerator) dynamicDriveToolTipTagFragmentGenerator0, (URLTagFragmentGenerator) standardURLTagFragmentGenerator0);
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      StringWriter stringWriter0 = new StringWriter(92);
      MockPrintWriter mockPrintWriter0 = new MockPrintWriter(stringWriter0, false);
      StandardEntityCollection standardEntityCollection0 = new StandardEntityCollection();
      ChartRenderingInfo chartRenderingInfo0 = new ChartRenderingInfo(standardEntityCollection0);
      ImageMapUtils.writeImageMap((PrintWriter) mockPrintWriter0, "2ksH1vuaX2=;", chartRenderingInfo0);
      assertEquals("<map id=\"2ksH1vuaX2=;\" name=\"2ksH1vuaX2=;\">\n</map>\n", stringWriter0.toString());
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      StandardEntityCollection standardEntityCollection0 = new StandardEntityCollection();
      ChartRenderingInfo chartRenderingInfo0 = new ChartRenderingInfo(standardEntityCollection0);
      String string0 = ImageMapUtils.getImageMap("\\", chartRenderingInfo0);
      assertEquals("<map id=\"&#092;\" name=\"&#092;\">\n</map>", string0);
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      String string0 = ImageMapUtils.htmlEscape("");
      assertEquals("", string0);
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      // Undeclared exception!
      try { 
        ImageMapUtils.javascriptEscape((String) null);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Null 'input' argument.
         //
         verifyException("org.jfree.chart.util.Args", e);
      }
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      // Undeclared exception!
      try { 
        ImageMapUtils.htmlEscape((String) null);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Null 'input' argument.
         //
         verifyException("org.jfree.chart.util.Args", e);
      }
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      StandardURLTagFragmentGenerator standardURLTagFragmentGenerator0 = new StandardURLTagFragmentGenerator();
      OverLIBToolTipTagFragmentGenerator overLIBToolTipTagFragmentGenerator0 = new OverLIBToolTipTagFragmentGenerator();
      // Undeclared exception!
      try { 
        ImageMapUtils.getImageMap("", (ChartRenderingInfo) null, (ToolTipTagFragmentGenerator) overLIBToolTipTagFragmentGenerator0, (URLTagFragmentGenerator) standardURLTagFragmentGenerator0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.jfree.chart.imagemap.ImageMapUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      StandardToolTipTagFragmentGenerator standardToolTipTagFragmentGenerator0 = new StandardToolTipTagFragmentGenerator();
      StandardURLTagFragmentGenerator standardURLTagFragmentGenerator0 = new StandardURLTagFragmentGenerator();
      // Undeclared exception!
      try { 
        ImageMapUtils.getImageMap((String) null, (ChartRenderingInfo) null, (ToolTipTagFragmentGenerator) standardToolTipTagFragmentGenerator0, (URLTagFragmentGenerator) standardURLTagFragmentGenerator0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Null 'input' argument.
         //
         verifyException("org.jfree.chart.util.Args", e);
      }
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      // Undeclared exception!
      try { 
        ImageMapUtils.getImageMap("2,YM,n>QH4<28", (ChartRenderingInfo) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.jfree.chart.imagemap.ImageMapUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      ChartRenderingInfo chartRenderingInfo0 = new ChartRenderingInfo();
      // Undeclared exception!
      try { 
        ImageMapUtils.getImageMap((String) null, chartRenderingInfo0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Null 'input' argument.
         //
         verifyException("org.jfree.chart.util.Args", e);
      }
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      OverLIBToolTipTagFragmentGenerator overLIBToolTipTagFragmentGenerator0 = new OverLIBToolTipTagFragmentGenerator();
      String string0 = overLIBToolTipTagFragmentGenerator0.generateToolTipFragment("uG:rbXt?p{");
      assertEquals(" onMouseOver=\"return overlib('uG:rbXt?p{');\" onMouseOut=\"return nd();\"", string0);
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      String string0 = ImageMapUtils.htmlEscape("wW`_");
      assertEquals("wW`_", string0);
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      String string0 = ImageMapUtils.htmlEscape("'");
      assertEquals("&#39;", string0);
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      String string0 = ImageMapUtils.htmlEscape("</map>");
      assertEquals("&lt;/map&gt;", string0);
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      String string0 = ImageMapUtils.htmlEscape("1tPhk7^SN->\"");
      assertEquals("1tPhk7^SN-&gt;&quot;", string0);
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      String string0 = ImageMapUtils.htmlEscape("&#39;");
      assertEquals("&amp;#39;", string0);
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      StandardEntityCollection standardEntityCollection0 = new StandardEntityCollection();
      ChartRenderingInfo chartRenderingInfo0 = new ChartRenderingInfo(standardEntityCollection0);
      StandardToolTipTagFragmentGenerator standardToolTipTagFragmentGenerator0 = new StandardToolTipTagFragmentGenerator();
      Point point0 = new Point();
      Line2D.Double line2D_Double0 = new Line2D.Double(point0, point0);
      ChartEntity chartEntity0 = new ChartEntity(line2D_Double0, "", (String) null);
      standardEntityCollection0.add(chartEntity0);
      StandardURLTagFragmentGenerator standardURLTagFragmentGenerator0 = new StandardURLTagFragmentGenerator();
      String string0 = ImageMapUtils.getImageMap("<map id=\"vgO;]3L-O6@f&amp;NZ&lt;A^\" name=\"vgO;]3L-O6@f&amp;NZ&lt;A^\">\n</map>", chartRenderingInfo0, (ToolTipTagFragmentGenerator) standardToolTipTagFragmentGenerator0, (URLTagFragmentGenerator) standardURLTagFragmentGenerator0);
      assertNotNull(string0);
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      ChartRenderingInfo chartRenderingInfo0 = new ChartRenderingInfo((EntityCollection) null);
      StandardURLTagFragmentGenerator standardURLTagFragmentGenerator0 = new StandardURLTagFragmentGenerator();
      OverLIBToolTipTagFragmentGenerator overLIBToolTipTagFragmentGenerator0 = new OverLIBToolTipTagFragmentGenerator();
      String string0 = ImageMapUtils.getImageMap("org.jfree.chart.entity.ChartEntity", chartRenderingInfo0, (ToolTipTagFragmentGenerator) overLIBToolTipTagFragmentGenerator0, (URLTagFragmentGenerator) standardURLTagFragmentGenerator0);
      assertEquals("<map id=\"org.jfree.chart.entity.ChartEntity\" name=\"org.jfree.chart.entity.ChartEntity\">\n</map>", string0);
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      ChartRenderingInfo chartRenderingInfo0 = new ChartRenderingInfo();
      StandardURLTagFragmentGenerator standardURLTagFragmentGenerator0 = new StandardURLTagFragmentGenerator();
      // Undeclared exception!
      try { 
        ImageMapUtils.writeImageMap((PrintWriter) null, ".?3S*bc2[0$Y", chartRenderingInfo0, (ToolTipTagFragmentGenerator) null, (URLTagFragmentGenerator) standardURLTagFragmentGenerator0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.jfree.chart.imagemap.ImageMapUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      String string0 = ImageMapUtils.javascriptEscape("]{o4'D./!X$-zcu3]");
      assertEquals("]{o4\\'D./!X$-zcu3]", string0);
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      String string0 = ImageMapUtils.javascriptEscape("}CyxtX%fI%f\"NP%.\"dO");
      assertEquals("}CyxtX%fI%f\\\"NP%.\\\"dO", string0);
  }

  @Test(timeout = 4000)
  public void test22()  throws Throwable  {
      String string0 = ImageMapUtils.javascriptEscape("");
      assertEquals("", string0);
  }

  @Test(timeout = 4000)
  public void test23()  throws Throwable  {
      StandardEntityCollection standardEntityCollection0 = new StandardEntityCollection();
      ChartRenderingInfo chartRenderingInfo0 = new ChartRenderingInfo(standardEntityCollection0);
      Rectangle2D.Float rectangle2D_Float0 = new Rectangle2D.Float(0.0F, 0.0F, 0.0F, (-882.3F));
      ChartEntity chartEntity0 = new ChartEntity(rectangle2D_Float0);
      standardEntityCollection0.add(chartEntity0);
      StandardURLTagFragmentGenerator standardURLTagFragmentGenerator0 = new StandardURLTagFragmentGenerator();
      String string0 = ImageMapUtils.getImageMap(") J>0.J=Rlay^hFs}]", chartRenderingInfo0, (ToolTipTagFragmentGenerator) null, (URLTagFragmentGenerator) standardURLTagFragmentGenerator0);
      assertEquals("<map id=\") J&gt;0.J=Rlay^hFs}]\" name=\") J&gt;0.J=Rlay^hFs}]\">\n</map>", string0);
  }

  @Test(timeout = 4000)
  public void test24()  throws Throwable  {
      StandardEntityCollection standardEntityCollection0 = new StandardEntityCollection();
      ChartRenderingInfo chartRenderingInfo0 = new ChartRenderingInfo(standardEntityCollection0);
      Rectangle2D.Float rectangle2D_Float0 = new Rectangle2D.Float(0.0F, 0.0F, 0.0F, (-882.3F));
      ChartEntity chartEntity0 = new ChartEntity(rectangle2D_Float0, (String) null, ") J>0.J=Rlay^hFs}]");
      standardEntityCollection0.add(chartEntity0);
      StandardURLTagFragmentGenerator standardURLTagFragmentGenerator0 = new StandardURLTagFragmentGenerator();
      String string0 = ImageMapUtils.getImageMap(") J>0.J=Rlay^hFs}]", chartRenderingInfo0, (ToolTipTagFragmentGenerator) null, (URLTagFragmentGenerator) standardURLTagFragmentGenerator0);
      assertEquals("<map id=\") J&gt;0.J=Rlay^hFs}]\" name=\") J&gt;0.J=Rlay^hFs}]\">\n<area shape=\"rect\" coords=\"0,0,1,-882\" href=\") J>0.J=Rlay^hFs}]\" alt=\"\"/>\n</map>", string0);
  }

  @Test(timeout = 4000)
  public void test25()  throws Throwable  {
      MockFileOutputStream mockFileOutputStream0 = new MockFileOutputStream("Lt'TdrL,x");
      MockPrintWriter mockPrintWriter0 = new MockPrintWriter(mockFileOutputStream0);
      StandardEntityCollection standardEntityCollection0 = new StandardEntityCollection();
      ChartRenderingInfo chartRenderingInfo0 = new ChartRenderingInfo(standardEntityCollection0);
      ImageMapUtils.writeImageMap((PrintWriter) mockPrintWriter0, "", chartRenderingInfo0, true);
  }

  @Test(timeout = 4000)
  public void test26()  throws Throwable  {
      ImageMapUtils imageMapUtils0 = new ImageMapUtils();
  }

  @Test(timeout = 4000)
  public void test27()  throws Throwable  {
      ChartRenderingInfo chartRenderingInfo0 = new ChartRenderingInfo();
      // Undeclared exception!
      try { 
        ImageMapUtils.writeImageMap((PrintWriter) null, ".?3S*bc2[0$Y", chartRenderingInfo0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.jfree.chart.imagemap.ImageMapUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test28()  throws Throwable  {
      StandardEntityCollection standardEntityCollection0 = new StandardEntityCollection();
      ChartRenderingInfo chartRenderingInfo0 = new ChartRenderingInfo(standardEntityCollection0);
      // Undeclared exception!
      try { 
        ImageMapUtils.writeImageMap((PrintWriter) null, "", chartRenderingInfo0, false);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.jfree.chart.imagemap.ImageMapUtils", e);
      }
  }
}
