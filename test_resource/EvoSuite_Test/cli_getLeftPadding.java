/*
 * This file was automatically generated by EvoSuite
 * Wed Jun 18 07:39:19 GMT 2025
 */

package org.apache.commons.cli;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.io.DataOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.PipedOutputStream;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.io.UnsupportedEncodingException;
import java.net.URISyntaxException;
import java.util.Comparator;
import java.util.Locale;
import org.apache.commons.cli.HelpFormatter;
import org.apache.commons.cli.Option;
import org.apache.commons.cli.Options;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.evosuite.runtime.mock.java.io.MockFile;
import org.evosuite.runtime.mock.java.io.MockFileOutputStream;
import org.evosuite.runtime.mock.java.io.MockPrintStream;
import org.evosuite.runtime.mock.java.io.MockPrintWriter;
import org.evosuite.runtime.mock.java.net.MockURI;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true) 
public class HelpFormatter_ESTest extends HelpFormatter_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      Options options0 = new Options();
      options0.addOption("", "#TFZe<1");
      options0.hasLongOption("VW<oY)|,");
      helpFormatter0.printHelp("1L=ev 83aD,U+S<G (7", (String) null, options0, "1L=ev 83aD,U+S<G (7", true);
      options0.helpOptions();
      helpFormatter0.setLongOptPrefix("usage: ");
      helpFormatter0.getLongOptPrefix();
      helpFormatter0.getDescPadding();
      PrintWriter printWriter0 = null;
      helpFormatter0.printHelp("x\"RN&F#2^+K&i", options0);
      helpFormatter0.defaultSyntaxPrefix = "2,#y4@U[X";
      String string0 = null;
      int int0 = 2141;
      // Undeclared exception!
      try { 
        helpFormatter0.printUsage((PrintWriter) null, (-332), (String) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.cli.HelpFormatter", e);
      }
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      helpFormatter0.getNewLine();
      helpFormatter0.getNewLine();
      helpFormatter0.setDescPadding(0);
      helpFormatter0.setDescPadding((-1509));
      Options options0 = new Options();
      options0.helpOptions();
      HelpFormatter helpFormatter1 = new HelpFormatter();
      helpFormatter1.setLongOptPrefix("");
      helpFormatter1.getLongOptPrefix();
      helpFormatter0.getDescPadding();
      PrintWriter printWriter0 = null;
      String string0 = "";
      // Undeclared exception!
      try { 
        helpFormatter1.printHelp((String) null, options0, false);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // cmdLineSyntax not provided
         //
         verifyException("org.apache.commons.cli.HelpFormatter", e);
      }
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      String string0 = "/3j";
      int int0 = (-800);
      helpFormatter0.defaultLeftPad = (-800);
      helpFormatter0.setLeftPadding(3268);
      MockPrintWriter mockPrintWriter0 = new MockPrintWriter("/3j");
      int int1 = 0;
      // Undeclared exception!
      helpFormatter0.printUsage((PrintWriter) mockPrintWriter0, 0, "/3j");
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      helpFormatter0.getLeftPadding();
      MockFile mockFile0 = new MockFile("\n", "usage: ");
      String string0 = "]69A@-\\Rj(50zaaq9g";
      MockPrintWriter mockPrintWriter0 = null;
      try {
        mockPrintWriter0 = new MockPrintWriter(mockFile0, "]69A@-Rj(50zaaq9g");
        fail("Expecting exception: UnsupportedEncodingException");
      
      } catch(Throwable e) {
         //
         // ]69A@-Rj(50zaaq9g
         //
         verifyException("org.evosuite.runtime.mock.java.io.MockPrintWriter", e);
      }
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      String string0 = null;
      MockFile mockFile0 = new MockFile((File) null, "]");
      File file0 = MockFile.createTempFile("1yrz E5j?*emP_0L", (String) null, (File) mockFile0);
      MockPrintWriter mockPrintWriter0 = new MockPrintWriter(file0);
      helpFormatter0.defaultSyntaxPrefix = "]";
      mockPrintWriter0.println();
      PrintWriter printWriter0 = mockPrintWriter0.append((CharSequence) "-");
      int int0 = 2;
      helpFormatter0.printWrapped(printWriter0, 2, "1yrz E5j?*emP_0L");
      PipedOutputStream pipedOutputStream0 = new PipedOutputStream();
      MockPrintWriter mockPrintWriter1 = new MockPrintWriter(pipedOutputStream0);
      String string1 = null;
      byte[] byteArray0 = new byte[2];
      byteArray0[0] = (byte) (-47);
      byte byte0 = (byte) (-43);
      byteArray0[1] = (byte) (-43);
      try { 
        pipedOutputStream0.write(byteArray0);
        fail("Expecting exception: IOException");
      
      } catch(IOException e) {
         //
         // Pipe not connected
         //
         verifyException("java.io.PipedOutputStream", e);
      }
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      helpFormatter0.setLongOptSeparator("");
      helpFormatter0.setDescPadding((-3310));
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      int int0 = (-1620);
      helpFormatter0.setDescPadding((-1620));
      MockPrintWriter mockPrintWriter0 = null;
      try {
        mockPrintWriter0 = new MockPrintWriter((File) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("java.io.File", e);
      }
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      MockPrintWriter mockPrintWriter0 = new MockPrintWriter(" ");
      // Undeclared exception!
      try { 
        helpFormatter0.printHelp((PrintWriter) mockPrintWriter0, (-1498), (String) null, "--", (Options) null, 0, (-1498), "   ");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // cmdLineSyntax not provided
         //
         verifyException("org.apache.commons.cli.HelpFormatter", e);
      }
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      helpFormatter0.setNewLine(")o-DSYRGV=gJ");
      String string0 = "9'*`-UuIsrG^*il";
      MockFileOutputStream mockFileOutputStream0 = new MockFileOutputStream("9'*`-UuIsrG^*il", true);
      MockPrintWriter mockPrintWriter0 = new MockPrintWriter(mockFileOutputStream0, true);
      int int0 = 0;
      String string1 = "&S6rFd[iMX.Jjvs";
      Options options0 = new Options();
      Option option0 = null;
      try {
        option0 = new Option("-", true, " ");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Illegal option name '-'
         //
         verifyException("org.apache.commons.cli.OptionValidator", e);
      }
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      PipedOutputStream pipedOutputStream0 = new PipedOutputStream();
      DataOutputStream dataOutputStream0 = new DataOutputStream(pipedOutputStream0);
      MockPrintWriter mockPrintWriter0 = new MockPrintWriter(dataOutputStream0, true);
      Locale locale0 = Locale.CHINESE;
      Object[] objectArray0 = new Object[9];
      objectArray0[0] = (Object) locale0;
      objectArray0[1] = (Object) pipedOutputStream0;
      objectArray0[2] = (Object) dataOutputStream0;
      Object object0 = new Object();
      objectArray0[3] = object0;
      objectArray0[4] = (Object) locale0;
      objectArray0[5] = (Object) locale0;
      objectArray0[6] = (Object) dataOutputStream0;
      objectArray0[7] = (Object) mockPrintWriter0;
      Object object1 = new Object();
      objectArray0[8] = object1;
      PrintWriter printWriter0 = mockPrintWriter0.printf(locale0, "usage: ", objectArray0);
      MockPrintWriter mockPrintWriter1 = new MockPrintWriter(printWriter0, false);
      helpFormatter0.printWrapped((PrintWriter) mockPrintWriter1, (-1791), (-1791), "");
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      helpFormatter0.defaultWidth = (-3806);
      helpFormatter0.getOptPrefix();
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      int int0 = 0;
      helpFormatter0.setLeftPadding(0);
      helpFormatter0.getOptionComparator();
      File file0 = null;
      MockPrintWriter mockPrintWriter0 = null;
      try {
        mockPrintWriter0 = new MockPrintWriter((File) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("java.io.File", e);
      }
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      helpFormatter0.defaultWidth = 158;
      helpFormatter0.setWidth(812);
      Comparator<Option> comparator0 = helpFormatter0.getOptionComparator();
      helpFormatter0.setOptionComparator(comparator0);
      String string0 = "nT\"\\S{";
      Options options0 = new Options();
      Options options1 = options0.addOption("", false, "");
      // Undeclared exception!
      try { 
        options1.addRequiredOption("--", (String) null, true, "].U%(Dfsgwij50LN=");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The option '--' contains an illegal character : '-'
         //
         verifyException("org.apache.commons.cli.OptionValidator", e);
      }
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      helpFormatter0.getWidth();
      String string0 = "";
      MockPrintWriter mockPrintWriter0 = null;
      try {
        mockPrintWriter0 = new MockPrintWriter("-", "");
        fail("Expecting exception: UnsupportedEncodingException");
      
      } catch(Throwable e) {
         //
         // 
         //
         verifyException("org.evosuite.runtime.mock.java.io.MockPrintWriter", e);
      }
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      helpFormatter0.defaultArgName = "+AkSWj;mz)f{7<K(";
      helpFormatter0.defaultNewLine = "!*MEJa.";
      helpFormatter0.getLongOptSeparator();
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      String string0 = "";
      helpFormatter0.setLongOptSeparator("");
      Comparator<Option> comparator0 = helpFormatter0.getOptionComparator();
      helpFormatter0.optionComparator = comparator0;
      helpFormatter0.rtrim("F$S<");
      helpFormatter0.getSyntaxPrefix();
      helpFormatter0.getLongOptPrefix();
      helpFormatter0.setDescPadding((-2660));
      helpFormatter0.setArgName("");
      helpFormatter0.setSyntaxPrefix("");
      helpFormatter0.setArgName("hg962iC:gn");
      helpFormatter0.getArgName();
      try { 
        MockURI.URI("hg962iC:gn", "hg962iC:gn", "NZl)Aj#7ix", 0, "", "F$S<", "-");
        fail("Expecting exception: URISyntaxException");
      
      } catch(URISyntaxException e) {
         //
         // Illegal character in fragment at index 43: hg962iC:gn://hg962iC:gn@NZl)Aj#7ix:0?F$S%3C#-
         //
         verifyException("java.net.URI$Parser", e);
      }
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      helpFormatter0.setArgName("tJ9/H&=1u WTa97");
      helpFormatter0.getSyntaxPrefix();
      helpFormatter0.defaultOptPrefix = "tJ9/H&=1u WTa97";
      MockPrintWriter mockPrintWriter0 = null;
      try {
        mockPrintWriter0 = new MockPrintWriter("", ">a3p;[P*");
        fail("Expecting exception: UnsupportedEncodingException");
      
      } catch(Throwable e) {
         //
         // >a3p;[P*
         //
         verifyException("org.evosuite.runtime.mock.java.io.MockPrintWriter", e);
      }
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      StringBuffer stringBuffer0 = null;
      int int0 = (-801);
      Options options0 = new Options();
      options0.getOption("--");
      boolean boolean0 = true;
      helpFormatter0.printHelp("jHmCtiOopa(fjEA)<", (String) null, options0, "", true);
      String string0 = "A[wh}V8{I'W]{Od1";
      // Undeclared exception!
      try { 
        options0.addOption("A[wh}V8{I'W]{Od1", "--");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The option 'A[wh}V8{I'W]{Od1' contains an illegal character : '['
         //
         verifyException("org.apache.commons.cli.OptionValidator", e);
      }
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      helpFormatter0.setWidth(0);
      helpFormatter0.getOptPrefix();
      helpFormatter0.setLongOptPrefix("-");
      helpFormatter0.getDescPadding();
      Comparator<Option> comparator0 = helpFormatter0.getOptionComparator();
      helpFormatter0.setOptionComparator(comparator0);
      helpFormatter0.setOptPrefix("-");
      helpFormatter0.getDescPadding();
      MockPrintStream mockPrintStream0 = new MockPrintStream("arg");
      MockPrintWriter mockPrintWriter0 = new MockPrintWriter(mockPrintStream0, false);
      // Undeclared exception!
      try { 
        helpFormatter0.printWrapped((PrintWriter) mockPrintWriter0, (-2), 2598, "org.apache.commons.cli.HelpFormatter$OptionComparator");
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
      }
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      helpFormatter0.getArgName();
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      Options options0 = new Options();
      options0.addOption("", "#TFZe<1");
      options0.hasLongOption("VW<oY)|,");
      helpFormatter0.printHelp("1L=ev 83aD,U+S<G (7", (String) null, options0, "1L=ev 83aD,U+S<G (7", true);
      options0.helpOptions();
      helpFormatter0.setLongOptPrefix("usage: ");
      helpFormatter0.getLongOptPrefix();
      helpFormatter0.getDescPadding();
      helpFormatter0.printHelp("x\"RN&F#2^+K&i", options0);
      helpFormatter0.defaultSyntaxPrefix = "2,#y4@U[X";
      // Undeclared exception!
      try { 
        helpFormatter0.printWrapped((PrintWriter) null, 3, (String) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.cli.HelpFormatter", e);
      }
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      MockFile mockFile0 = new MockFile((File) null, "]");
      File file0 = MockFile.createTempFile("1yrz E5j?*emP_0L", (String) null, (File) mockFile0);
      MockPrintWriter mockPrintWriter0 = new MockPrintWriter(file0);
      helpFormatter0.defaultSyntaxPrefix = "]";
      mockPrintWriter0.println();
      PrintWriter printWriter0 = mockPrintWriter0.append((CharSequence) "-");
      helpFormatter0.printWrapped(printWriter0, 74, "1yrz E5j?*emP_0L");
      PipedOutputStream pipedOutputStream0 = new PipedOutputStream();
      MockPrintWriter mockPrintWriter1 = new MockPrintWriter(pipedOutputStream0);
      byte[] byteArray0 = new byte[2];
      byteArray0[0] = (byte) (-47);
      byteArray0[1] = (byte) (-43);
  }

  @Test(timeout = 4000)
  public void test22()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      helpFormatter0.getSyntaxPrefix();
  }

  @Test(timeout = 4000)
  public void test23()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      MockPrintWriter mockPrintWriter0 = new MockPrintWriter(" ");
      helpFormatter0.createPadding(0);
      helpFormatter0.getArgName();
      helpFormatter0.getOptionComparator();
  }

  @Test(timeout = 4000)
  public void test24()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      helpFormatter0.getLongOptSeparator();
      StringWriter stringWriter0 = new StringWriter(1);
      StringWriter stringWriter1 = stringWriter0.append('p');
      MockPrintWriter mockPrintWriter0 = new MockPrintWriter(stringWriter1, false);
      Object[] objectArray0 = new Object[3];
      objectArray0[0] = (Object) " ";
      objectArray0[1] = (Object) " ";
      objectArray0[2] = (Object) " ";
      PrintWriter printWriter0 = mockPrintWriter0.printf((Locale) null, "h", objectArray0);
      // Undeclared exception!
      helpFormatter0.printWrapped(printWriter0, 0, "iD_UZ',]9T,");
  }

  @Test(timeout = 4000)
  public void test25()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      StringBuffer stringBuffer0 = new StringBuffer();
      StringBuffer stringBuffer1 = helpFormatter0.renderWrappedText(stringBuffer0, 469, 469, "");
      helpFormatter0.renderWrappedText(stringBuffer1, 469, 469, "");
      helpFormatter0.setLongOptPrefix("");
  }

  @Test(timeout = 4000)
  public void test26()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      Options options0 = new Options();
      Options options1 = options0.addOption("arg", true, (String) null);
      Options options2 = options1.addRequiredOption((String) null, "arg", false, "sa/&vUuS<iG/%oNb");
      Option option0 = new Option("", "");
      Options options3 = options2.addOption(option0);
      Options options4 = options3.addOption("", "usage: ");
      // Undeclared exception!
      try { 
        helpFormatter0.printHelp((-2), "", "", options4, "");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // cmdLineSyntax not provided
         //
         verifyException("org.apache.commons.cli.HelpFormatter", e);
      }
  }

  @Test(timeout = 4000)
  public void test27()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      Comparator<Option> comparator0 = helpFormatter0.optionComparator;
      helpFormatter0.setOptionComparator(comparator0);
      int int0 = 1;
      Options options0 = new Options();
      helpFormatter0.printHelp("The addValue method is not intended for client use. Subclasses should use the addValueForProcessing method instead. ", options0);
      helpFormatter0.setLongOptPrefix((String) null);
      // Undeclared exception!
      helpFormatter0.printHelp(1, "arg", "arg", options0, "");
  }

  @Test(timeout = 4000)
  public void test28()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      helpFormatter0.createPadding(0);
      helpFormatter0.getArgName();
      helpFormatter0.getOptionComparator();
      Options options0 = new Options();
      Options options1 = options0.addOption("arg", false, "[HNcp>>UKRIe7Py}'4J");
      Options options2 = options1.addOption("", "-", true, "!aN=xuEcCr");
      // Undeclared exception!
      try { 
        helpFormatter0.printHelp("", options2, true);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // cmdLineSyntax not provided
         //
         verifyException("org.apache.commons.cli.HelpFormatter", e);
      }
  }

  @Test(timeout = 4000)
  public void test29()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      helpFormatter0.getNewLine();
      helpFormatter0.getNewLine();
      helpFormatter0.setDescPadding(0);
      helpFormatter0.setDescPadding((-1509));
      Options options0 = new Options();
      String string0 = "";
      options0.addOption("", false, " ");
      options0.hasOption("\n");
      helpFormatter0.createPadding(0);
      String string1 = "";
      Options options1 = options0.addOption("arg", "");
      // Undeclared exception!
      try { 
        helpFormatter0.printHelp("KJ7,$+7p=[", options1);
        fail("Expecting exception: NegativeArraySizeException");
      
      } catch(NegativeArraySizeException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.cli.HelpFormatter", e);
      }
  }
}
