/*
 * This file was automatically generated by EvoSuite
 * Wed Jun 18 09:00:21 GMT 2025
 */

package software.amazon.event.ruler;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.junit.runner.RunWith;
import software.amazon.event.ruler.IntIntMap;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true) 
public class IntIntMap_ESTest extends IntIntMap_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      IntIntMap intIntMap0 = new IntIntMap();
      intIntMap0.put(2, 2);
      intIntMap0.put(1, 1);
      int int0 = intIntMap0.remove(2);
      assertEquals(2, int0);
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      IntIntMap intIntMap0 = new IntIntMap(2);
      intIntMap0.put(2, 0);
      int int0 = intIntMap0.remove(0);
      assertEquals((-1), int0);
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      IntIntMap intIntMap0 = new IntIntMap();
      int int0 = intIntMap0.put(0, 0);
      int int1 = intIntMap0.get((-5363));
      assertTrue(int1 == int0);
      assertEquals((-1), int1);
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      IntIntMap intIntMap0 = null;
      try {
        intIntMap0 = new IntIntMap(0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // initialCapacity must be positive
         //
         verifyException("software.amazon.event.ruler.IntIntMap", e);
      }
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      IntIntMap intIntMap0 = null;
      try {
        intIntMap0 = new IntIntMap(1, 1);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // loadFactor must be in (0, 1)
         //
         verifyException("software.amazon.event.ruler.IntIntMap", e);
      }
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      IntIntMap intIntMap0 = null;
      try {
        intIntMap0 = new IntIntMap(0, 0.0F);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // loadFactor must be in (0, 1)
         //
         verifyException("software.amazon.event.ruler.IntIntMap", e);
      }
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      IntIntMap intIntMap0 = new IntIntMap(1, 0.75F);
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      IntIntMap intIntMap0 = new IntIntMap();
      int int0 = intIntMap0.put(2, 2);
      assertEquals((-1), int0);
      
      int int1 = intIntMap0.size();
      assertEquals(1, int1);
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      IntIntMap intIntMap0 = new IntIntMap();
      int int0 = intIntMap0.put(0, 0);
      int int1 = intIntMap0.put(0, 0);
      assertFalse(int1 == int0);
      assertEquals(0, int1);
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      IntIntMap intIntMap0 = new IntIntMap();
      int int0 = intIntMap0.put(0, 0);
      assertEquals((-1), int0);
      
      int int1 = intIntMap0.get(0);
      assertEquals(0, int1);
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      IntIntMap intIntMap0 = new IntIntMap();
      boolean boolean0 = intIntMap0.isEmpty();
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      IntIntMap intIntMap0 = new IntIntMap(1);
      int int0 = intIntMap0.put(0, 0);
      assertEquals((-1), int0);
      
      boolean boolean0 = intIntMap0.isEmpty();
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      IntIntMap intIntMap0 = new IntIntMap(1);
      int int0 = intIntMap0.put(1, 1);
      int int1 = intIntMap0.remove(1474);
      assertTrue(int1 == int0);
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      IntIntMap intIntMap0 = new IntIntMap();
      int int0 = intIntMap0.put(0, 0);
      int int1 = intIntMap0.remove(0);
      assertFalse(int1 == int0);
      assertEquals(0, int1);
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      IntIntMap intIntMap0 = new IntIntMap();
      int int0 = intIntMap0.put(1, 1);
      assertEquals((-1), int0);
      
      int int1 = intIntMap0.put(1, 1);
      assertEquals(1, int1);
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      IntIntMap intIntMap0 = new IntIntMap(1);
      intIntMap0.put(1, 1);
      int int0 = intIntMap0.put(3560, 913);
      assertEquals((-1), int0);
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      IntIntMap intIntMap0 = new IntIntMap();
      // Undeclared exception!
      try { 
        intIntMap0.put(0, (-1));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // value cannot be negative
         //
         verifyException("software.amazon.event.ruler.IntIntMap", e);
      }
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      IntIntMap intIntMap0 = new IntIntMap();
      // Undeclared exception!
      try { 
        intIntMap0.put((-1), (-1));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // key cannot be negative
         //
         verifyException("software.amazon.event.ruler.IntIntMap", e);
      }
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      IntIntMap intIntMap0 = new IntIntMap(1);
      intIntMap0.put(0, 1);
      int int0 = intIntMap0.get(0);
      assertEquals(1, int0);
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      IntIntMap intIntMap0 = null;
      try {
        intIntMap0 = new IntIntMap(253);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // initialCapacity must be a power of two
         //
         verifyException("software.amazon.event.ruler.IntIntMap", e);
      }
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      IntIntMap intIntMap0 = null;
      try {
        intIntMap0 = new IntIntMap((-1));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // initialCapacity must be positive
         //
         verifyException("software.amazon.event.ruler.IntIntMap", e);
      }
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      IntIntMap intIntMap0 = null;
      try {
        intIntMap0 = new IntIntMap(913, 913);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // loadFactor must be in (0, 1)
         //
         verifyException("software.amazon.event.ruler.IntIntMap", e);
      }
  }

  @Test(timeout = 4000)
  public void test22()  throws Throwable  {
      IntIntMap intIntMap0 = null;
      try {
        intIntMap0 = new IntIntMap((-538), (-106.0F));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // loadFactor must be in (0, 1)
         //
         verifyException("software.amazon.event.ruler.IntIntMap", e);
      }
  }

  @Test(timeout = 4000)
  public void test23()  throws Throwable  {
      IntIntMap intIntMap0 = new IntIntMap(1);
      Object object0 = intIntMap0.clone();
      assertNotSame(object0, intIntMap0);
  }

  @Test(timeout = 4000)
  public void test24()  throws Throwable  {
      IntIntMap intIntMap0 = new IntIntMap();
      int int0 = intIntMap0.size();
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test25()  throws Throwable  {
      IntIntMap intIntMap0 = new IntIntMap(1);
      int int0 = intIntMap0.put(0, 0);
      int int1 = intIntMap0.get(400);
      assertTrue(int1 == int0);
      assertEquals((-1), int1);
  }

  @Test(timeout = 4000)
  public void test26()  throws Throwable  {
      IntIntMap intIntMap0 = new IntIntMap(1);
      Iterable<IntIntMap.Entry> iterable0 = intIntMap0.entries();
      assertNotNull(iterable0);
  }
}
