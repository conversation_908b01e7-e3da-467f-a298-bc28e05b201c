/*
 * This file was automatically generated by EvoSuite
 * Wed Jun 18 07:51:21 GMT 2025
 */

package org.apache.commons.cli;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import org.apache.commons.cli.Option;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true) 
public class Option_ESTest extends Option_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      Option option0 = new Option((String) null, (String) null);
      option0.setArgs((-467));
      boolean boolean0 = option0.requiresArg();
      assertEquals((-467), option0.getArgs());
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      Option option0 = new Option((String) null, true, (String) null);
      option0.setArgs(0);
      // Undeclared exception!
      try { 
        option0.addValueForProcessing("[ option: ");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot add value, list full.
         //
         verifyException("org.apache.commons.cli.Option", e);
      }
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder("V0");
      Option.Builder option_Builder1 = option_Builder0.required(false);
      assertSame(option_Builder0, option_Builder1);
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      Option option0 = new Option((String) null, (String) null);
      option0.setType((Object) null);
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder("");
      option_Builder0.required();
      Option option0 = option_Builder0.build();
      boolean boolean0 = option0.isRequired();
      assertEquals((-1), option0.getArgs());
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      Option option0 = new Option("G", (String) null, true, "");
      option0.addValueForProcessing("G");
      option0.getValuesList();
      assertEquals("", option0.getDescription());
      assertFalse(option0.hasLongOpt());
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder();
      option_Builder0.valueSeparator('9');
      option_Builder0.longOpt("zq,NI");
      Option option0 = option_Builder0.build();
      char char0 = option0.getValueSeparator();
      assertEquals('9', char0);
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      Option option0 = new Option((String) null, " :: ", true, "org.apache.commons.cli.Option$Builder");
      option0.setValueSeparator('R');
      char char0 = option0.getValueSeparator();
      assertEquals('R', char0);
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      Option option0 = new Option((String) null, (String) null, true, (String) null);
      String string0 = option0.getValue((String) null);
      assertNull(string0);
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      Option option0 = new Option("", "", true, "--");
      String string0 = option0.getValue("");
      assertEquals("--", option0.getDescription());
      assertNotNull(string0);
      assertEquals("", option0.getLongOpt());
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      Option option0 = new Option("4c", "4c");
      option0.getOpt();
      assertFalse(option0.hasLongOpt());
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      Option option0 = new Option("", "", false, "");
      option0.getOpt();
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      Option option0 = new Option("", "NO_ARGS_ALLOWED", true, "");
      String string0 = option0.getLongOpt();
      assertEquals("NO_ARGS_ALLOWED", string0);
      assertEquals("", option0.getDescription());
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder("");
      Option.Builder option_Builder1 = option_Builder0.longOpt("");
      Option option0 = option_Builder1.build();
      option0.getLongOpt();
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      Option option0 = new Option((String) null, "NO_ARGS_ALLOWED");
      String string0 = option0.getKey();
      assertNull(string0);
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      Option option0 = new Option("NO_ARGS_ALLOWED", ".");
      option0.getKey();
      assertEquals(".", option0.getDescription());
      assertFalse(option0.hasLongOpt());
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      Option option0 = new Option("", "o[^}");
      option0.getKey();
      assertEquals("o[^}", option0.getDescription());
      assertEquals((-1), option0.getArgs());
      assertFalse(option0.hasLongOpt());
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      Option option0 = new Option("NO_ARGS_ALLOWED", "");
      int int0 = option0.getId();
      assertEquals(78, int0);
      assertEquals((-1), option0.getArgs());
      assertFalse(option0.hasLongOpt());
      assertEquals("", option0.getDescription());
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      Option option0 = new Option("", "", true, "--");
      String string0 = option0.getDescription();
      assertEquals("", option0.getLongOpt());
      assertEquals("--", string0);
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      Option option0 = new Option((String) null, "' contains an illegal character : '", false, "");
      String string0 = option0.getDescription();
      assertEquals("", string0);
      assertTrue(option0.hasLongOpt());
      assertNotNull(string0);
      assertEquals((-1), option0.getArgs());
      assertEquals("' contains an illegal character : '", option0.getLongOpt());
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      Option option0 = new Option("", "", true, (String) null);
      int int0 = option0.getArgs();
      assertEquals(1, int0);
      assertTrue(option0.hasLongOpt());
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder();
      option_Builder0.option("");
      Option.Builder option_Builder1 = option_Builder0.argName("5&#Kw/.a8EzDXa");
      Option option0 = option_Builder1.build();
      option0.getArgName();
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test22()  throws Throwable  {
      Option option0 = new Option("", "");
      option0.setArgName("");
      option0.getArgName();
      assertFalse(option0.hasLongOpt());
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test23()  throws Throwable  {
      Option option0 = new Option("", false, "");
      boolean boolean0 = option0.acceptsArg();
      assertFalse(boolean0);
      assertFalse(option0.hasLongOpt());
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test24()  throws Throwable  {
      Option option0 = new Option((String) null, (String) null, true, (String) null);
      option0.addValueForProcessing("[ option: null  [ARG] :: null :: class java.lang.String ]");
      try { 
        option0.getValue(2);
        fail("Expecting exception: IndexOutOfBoundsException");
      
      } catch(IndexOutOfBoundsException e) {
         //
         // Index: 2, Size: 1
         //
         verifyException("java.util.ArrayList", e);
      }
  }

  @Test(timeout = 4000)
  public void test25()  throws Throwable  {
      // Undeclared exception!
      try { 
        Option.builder("The addValue method is not intended for client use. Subclasses should use the addValueForProcessing method instead. ");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The option 'The addValue method is not intended for client use. Subclasses should use the addValueForProcessing method instead. ' contains an illegal character : ' '
         //
         verifyException("org.apache.commons.cli.OptionValidator", e);
      }
  }

  @Test(timeout = 4000)
  public void test26()  throws Throwable  {
      Option option0 = new Option((String) null, (String) null, true, "P/0VO~1XlD1xph<x");
      option0.setValueSeparator('(');
      // Undeclared exception!
      try { 
        option0.addValueForProcessing((String) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.cli.Option", e);
      }
  }

  @Test(timeout = 4000)
  public void test27()  throws Throwable  {
      Option option0 = null;
      try {
        option0 = new Option(" [ARG]", false, " [ARG]");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The option ' [ARG]' contains an illegal character : ' '
         //
         verifyException("org.apache.commons.cli.OptionValidator", e);
      }
  }

  @Test(timeout = 4000)
  public void test28()  throws Throwable  {
      Option option0 = null;
      try {
        option0 = new Option("uc=!4seUw'*Icw", "uc=!4seUw'*Icw", false, "");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The option 'uc=!4seUw'*Icw' contains an illegal character : '='
         //
         verifyException("org.apache.commons.cli.OptionValidator", e);
      }
  }

  @Test(timeout = 4000)
  public void test29()  throws Throwable  {
      Option option0 = null;
      try {
        option0 = new Option(" [ARG]", " [ARG]");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The option ' [ARG]' contains an illegal character : ' '
         //
         verifyException("org.apache.commons.cli.OptionValidator", e);
      }
  }

  @Test(timeout = 4000)
  public void test30()  throws Throwable  {
      Option option0 = new Option((String) null, (String) null);
      option0.hasValueSeparator();
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test31()  throws Throwable  {
      Option option0 = new Option("", true, "");
      option0.setValueSeparator(']');
      boolean boolean0 = option0.hasValueSeparator();
      assertEquals(']', option0.getValueSeparator());
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test32()  throws Throwable  {
      Option option0 = new Option("", "*<]6hO)+88'f+wWz");
      boolean boolean0 = option0.hasArgs();
      assertFalse(boolean0);
      assertEquals((-1), option0.getArgs());
      assertFalse(option0.hasLongOpt());
      assertEquals("*<]6hO)+88'f+wWz", option0.getDescription());
  }

  @Test(timeout = 4000)
  public void test33()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder();
      option_Builder0.longOpt("zq,NI");
      Option option0 = option_Builder0.build();
      boolean boolean0 = option0.hasArg();
      assertEquals((-1), option0.getArgs());
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test34()  throws Throwable  {
      Option option0 = new Option((String) null, (String) null, true, (String) null);
      boolean boolean0 = option0.hasArg();
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test35()  throws Throwable  {
      Option option0 = new Option((String) null, (String) null, true, (String) null);
      option0.setArgs((-2));
      boolean boolean0 = option0.hasArg();
      assertTrue(option0.hasArgs());
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test36()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder();
      option_Builder0.longOpt("zq,NI");
      Option option0 = option_Builder0.build();
      option0.getValueSeparator();
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test37()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder();
      Option.Builder option_Builder1 = option_Builder0.longOpt("zq,NI");
      Option option0 = option_Builder1.build();
      option0.hasOptionalArg();
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test38()  throws Throwable  {
      Option option0 = new Option((String) null, (String) null, true, (String) null);
      option0.setArgs((-2));
      option0.toString();
      assertEquals((-2), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test39()  throws Throwable  {
      Option option0 = new Option((String) null, (String) null, true, (String) null);
      String string0 = option0.toString();
      assertEquals("[ option: null  [ARG] :: null :: class java.lang.String ]", string0);
  }

  @Test(timeout = 4000)
  public void test40()  throws Throwable  {
      Option option0 = new Option("", true, "");
      option0.setValueSeparator(']');
      option0.addValueForProcessing(")@&P/`*36_A$EL*f,0");
      assertTrue(option0.hasValueSeparator());
  }

  @Test(timeout = 4000)
  public void test41()  throws Throwable  {
      Option option0 = new Option("", "", true, (String) null);
      boolean boolean0 = option0.hasLongOpt();
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test42()  throws Throwable  {
      Option option0 = new Option((String) null, true, "");
      boolean boolean0 = option0.hasLongOpt();
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test43()  throws Throwable  {
      Option option0 = new Option((String) null, (String) null, true, (String) null);
      option0.setArgs((-2));
      boolean boolean0 = option0.hasArgs();
      assertEquals((-2), option0.getArgs());
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test44()  throws Throwable  {
      Option option0 = new Option("NO_ARGS_ALLOWED", "H-4uAD824Twl", true, "wM;#lViKU~>Hna0H'");
      option0.setArgs(1369);
      boolean boolean0 = option0.hasArgs();
      assertEquals(1369, option0.getArgs());
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test45()  throws Throwable  {
      Option option0 = new Option("NO_ARGS_ALLOWED", "H-4uAD824Twl", true, "wM;#lViKU~>Hna0H'");
      boolean boolean0 = option0.hasArgName();
      assertEquals("wM;#lViKU~>Hna0H'", option0.getDescription());
      assertEquals("H-4uAD824Twl", option0.getLongOpt());
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test46()  throws Throwable  {
      Option option0 = new Option((String) null, (String) null, true, (String) null);
      String[] stringArray0 = option0.getValues();
      assertNull(stringArray0);
  }

  @Test(timeout = 4000)
  public void test47()  throws Throwable  {
      Option option0 = new Option((String) null, (String) null, true, (String) null);
      option0.addValueForProcessing((String) null);
      String[] stringArray0 = option0.getValues();
      assertEquals(1, stringArray0.length);
  }

  @Test(timeout = 4000)
  public void test48()  throws Throwable  {
      Option option0 = new Option("4c", "4c");
      String string0 = option0.getValue("4c");
      assertNotNull(string0);
      assertEquals((-1), option0.getArgs());
      assertFalse(option0.hasLongOpt());
  }

  @Test(timeout = 4000)
  public void test49()  throws Throwable  {
      Option option0 = new Option((String) null, (String) null, true, (String) null);
      String string0 = option0.getValue((-169));
      assertNull(string0);
  }

  @Test(timeout = 4000)
  public void test50()  throws Throwable  {
      Option option0 = new Option((String) null, (String) null, true, (String) null);
      option0.addValueForProcessing((String) null);
      try { 
        option0.getValue((-2));
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test51()  throws Throwable  {
      Option option0 = new Option((String) null, (String) null);
      option0.getValue();
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test52()  throws Throwable  {
      Option option0 = new Option("", true, "");
      option0.addValueForProcessing("Illegal option name '");
      String string0 = option0.getValue();
      assertFalse(option0.hasValueSeparator());
      assertFalse(option0.hasLongOpt());
      assertNotNull(string0);
  }

  @Test(timeout = 4000)
  public void test53()  throws Throwable  {
      Option option0 = new Option("", "");
      // Undeclared exception!
      try { 
        option0.getId();
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
      }
  }

  @Test(timeout = 4000)
  public void test54()  throws Throwable  {
      Option option0 = new Option((String) null, (String) null);
      Option option1 = new Option("", (String) null, true, (String) null);
      boolean boolean0 = option0.equals(option1);
      assertFalse(boolean0);
      assertEquals((-1), option0.getArgs());
      assertFalse(option1.hasLongOpt());
  }

  @Test(timeout = 4000)
  public void test55()  throws Throwable  {
      Option option0 = new Option("", true, "");
      Option option1 = new Option("", "Illegal option name '", true, "");
      boolean boolean0 = option0.equals(option1);
      assertEquals("", option1.getDescription());
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test56()  throws Throwable  {
      Option option0 = new Option((String) null, "4c");
      boolean boolean0 = option0.equals("4c");
      assertEquals((-1), option0.getArgs());
      assertFalse(option0.hasLongOpt());
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test57()  throws Throwable  {
      Option option0 = new Option("", true, "");
      Option option1 = new Option("", "");
      boolean boolean0 = option1.equals(option0);
      assertEquals((-1), option1.getArgs());
      assertTrue(boolean0);
      assertFalse(option1.hasLongOpt());
  }

  @Test(timeout = 4000)
  public void test58()  throws Throwable  {
      Option option0 = new Option((String) null, (String) null);
      // Undeclared exception!
      try { 
        option0.addValueForProcessing((String) null);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // NO_ARGS_ALLOWED
         //
         verifyException("org.apache.commons.cli.Option", e);
      }
  }

  @Test(timeout = 4000)
  public void test59()  throws Throwable  {
      Option option0 = new Option((String) null, (String) null, true, (String) null);
      option0.addValueForProcessing((String) null);
      // Undeclared exception!
      try { 
        option0.addValueForProcessing((String) null);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot add value, list full.
         //
         verifyException("org.apache.commons.cli.Option", e);
      }
  }

  @Test(timeout = 4000)
  public void test60()  throws Throwable  {
      Option option0 = new Option("", true, "");
      option0.addValueForProcessing("");
      boolean boolean0 = option0.requiresArg();
      assertFalse(option0.hasValueSeparator());
      assertFalse(option0.hasLongOpt());
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test61()  throws Throwable  {
      Option option0 = new Option("", (String) null, true, "Illegal option name '");
      option0.setArgs((-2));
      boolean boolean0 = option0.acceptsArg();
      assertEquals((-2), option0.getArgs());
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test62()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder();
      Option.Builder option_Builder1 = option_Builder0.optionalArg(true);
      assertSame(option_Builder1, option_Builder0);
  }

  @Test(timeout = 4000)
  public void test63()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder((String) null);
      Option.Builder option_Builder1 = option_Builder0.optionalArg(false);
      assertSame(option_Builder0, option_Builder1);
  }

  @Test(timeout = 4000)
  public void test64()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder("V0");
      Option.Builder option_Builder1 = option_Builder0.hasArg(false);
      assertSame(option_Builder0, option_Builder1);
  }

  @Test(timeout = 4000)
  public void test65()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder();
      // Undeclared exception!
      try { 
        option_Builder0.build();
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Either opt or longOpt must be specified
         //
         verifyException("org.apache.commons.cli.Option$Builder", e);
      }
  }

  @Test(timeout = 4000)
  public void test66()  throws Throwable  {
      Option option0 = new Option((String) null, (String) null);
      option0.setLongOpt((String) null);
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test67()  throws Throwable  {
      Option option0 = new Option((String) null, " :: ", true, "org.apache.commons.cli.Option$Builder");
      option0.getOpt();
      assertTrue(option0.hasLongOpt());
      assertEquals("org.apache.commons.cli.Option$Builder", option0.getDescription());
      assertEquals(" :: ", option0.getLongOpt());
  }

  @Test(timeout = 4000)
  public void test68()  throws Throwable  {
      Option option0 = new Option("", "", false, (String) null);
      option0.getType();
      assertEquals((-1), option0.getArgs());
      assertTrue(option0.hasLongOpt());
  }

  @Test(timeout = 4000)
  public void test69()  throws Throwable  {
      Option option0 = new Option((String) null, (String) null);
      // Undeclared exception!
      try { 
        option0.addValue("Either opt or longOpt must be specified");
        fail("Expecting exception: UnsupportedOperationException");
      
      } catch(UnsupportedOperationException e) {
         //
         // The addValue method is not intended for client use. Subclasses should use the addValueForProcessing method instead. 
         //
         verifyException("org.apache.commons.cli.Option", e);
      }
  }

  @Test(timeout = 4000)
  public void test70()  throws Throwable  {
      Option option0 = new Option("", "*<]6hO)+88'f+wWz");
      String string0 = option0.getLongOpt();
      assertEquals((-1), option0.getArgs());
      assertEquals("*<]6hO)+88'f+wWz", option0.getDescription());
      assertNull(string0);
  }

  @Test(timeout = 4000)
  public void test71()  throws Throwable  {
      Option option0 = new Option((String) null, (String) null);
      option0.hashCode();
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test72()  throws Throwable  {
      Option option0 = new Option((String) null, (String) null);
      option0.isRequired();
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test73()  throws Throwable  {
      Option option0 = new Option((String) null, (String) null);
      option0.getDescription();
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test74()  throws Throwable  {
      Option option0 = new Option("4c", "4c");
      int int0 = option0.getArgs();
      assertEquals((-1), int0);
      assertFalse(option0.hasLongOpt());
  }

  @Test(timeout = 4000)
  public void test75()  throws Throwable  {
      Option option0 = new Option((String) null, (String) null);
      option0.clearValues();
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test76()  throws Throwable  {
      Option option0 = new Option((String) null, (String) null);
      option0.setDescription("seEgF`|DK/@`");
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test77()  throws Throwable  {
      Option option0 = new Option((String) null, (String) null, true, (String) null);
      Object object0 = option0.clone();
      assertNotSame(object0, option0);
  }

  @Test(timeout = 4000)
  public void test78()  throws Throwable  {
      Option option0 = new Option((String) null, (String) null, true, (String) null);
      // Undeclared exception!
      try { 
        option0.getId();
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.cli.Option", e);
      }
  }

  @Test(timeout = 4000)
  public void test79()  throws Throwable  {
      Option option0 = new Option("G", (String) null, true, "");
      option0.getValuesList();
      assertFalse(option0.hasLongOpt());
      assertEquals("", option0.getDescription());
      assertEquals("G", option0.getOpt());
  }

  @Test(timeout = 4000)
  public void test80()  throws Throwable  {
      Option option0 = new Option((String) null, (String) null);
      Class<Object> class0 = Object.class;
      option0.setType(class0);
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test81()  throws Throwable  {
      Option option0 = new Option("", true, "");
      option0.setArgs((-2));
      boolean boolean0 = option0.requiresArg();
      assertTrue(option0.hasArgs());
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test82()  throws Throwable  {
      Option option0 = new Option("", false, "");
      option0.setRequired(false);
      assertEquals((-1), option0.getArgs());
      assertFalse(option0.hasLongOpt());
      assertFalse(option0.isRequired());
  }

  @Test(timeout = 4000)
  public void test83()  throws Throwable  {
      Option option0 = new Option((String) null, (String) null);
      option0.setOptionalArg(false);
      assertEquals((-1), option0.getArgs());
      assertFalse(option0.hasOptionalArg());
  }

  @Test(timeout = 4000)
  public void test84()  throws Throwable  {
      Option option0 = new Option("", "");
      option0.getArgName();
      assertEquals((-1), option0.getArgs());
      assertFalse(option0.hasLongOpt());
  }

  @Test(timeout = 4000)
  public void test85()  throws Throwable  {
      Option option0 = new Option((String) null, (String) null, true, (String) null);
      option0.setValueSeparator('');
      option0.addValueForProcessing(")@&P/`*36_A$EL*f,0");
      assertEquals('', option0.getValueSeparator());
  }

  @Test(timeout = 4000)
  public void test86()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder();
      Option option0 = new Option((String) null, "5S}-Z4qb");
      // Undeclared exception!
      try { 
        option0.setType((Object) option_Builder0);
        fail("Expecting exception: ClassCastException");
      
      } catch(ClassCastException e) {
         //
         // org.apache.commons.cli.Option$Builder cannot be cast to java.lang.Class
         //
         verifyException("org.apache.commons.cli.Option", e);
      }
  }

  @Test(timeout = 4000)
  public void test87()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder("");
      Class<Object> class0 = Object.class;
      Option.Builder option_Builder1 = option_Builder0.type(class0);
      assertSame(option_Builder0, option_Builder1);
  }

  @Test(timeout = 4000)
  public void test88()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder("");
      Option.Builder option_Builder1 = option_Builder0.numberOfArgs(504);
      assertSame(option_Builder0, option_Builder1);
  }

  @Test(timeout = 4000)
  public void test89()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder("");
      Option.Builder option_Builder1 = option_Builder0.hasArgs();
      assertSame(option_Builder0, option_Builder1);
  }

  @Test(timeout = 4000)
  public void test90()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder();
      Option.Builder option_Builder1 = option_Builder0.hasArg();
      assertSame(option_Builder0, option_Builder1);
  }

  @Test(timeout = 4000)
  public void test91()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder();
      Option.Builder option_Builder1 = option_Builder0.desc((String) null);
      assertSame(option_Builder1, option_Builder0);
  }

  @Test(timeout = 4000)
  public void test92()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder();
      option_Builder0.longOpt("zq,NI");
      Option option0 = option_Builder0.build();
      String string0 = option0.toString();
      assertEquals("[ option: null zq,NI  :: null :: class java.lang.String ]", string0);
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test93()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder();
      Option.Builder option_Builder1 = option_Builder0.valueSeparator();
      assertSame(option_Builder0, option_Builder1);
  }
}
