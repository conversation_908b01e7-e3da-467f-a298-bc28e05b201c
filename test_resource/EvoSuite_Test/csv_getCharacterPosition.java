/*
 * This file was automatically generated by EvoSuite
 * Wed Jun 18 08:03:20 GMT 2025
 */

package org.apache.commons.csv;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.io.PipedReader;
import java.io.PipedWriter;
import java.io.Reader;
import java.io.StringReader;
import java.util.HashMap;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true) 
public class CSVRecord_ESTest extends CSVRecord_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      String[] stringArray0 = new String[2];
      CSVRecord cSVRecord0 = new CSVRecord((CSVParser) null, stringArray0, "4U>FbqChbaL8*QMw>r~", (-659L), (-659L));
      boolean boolean0 = cSVRecord0.isSet(0);
      assertTrue(boolean0);
      assertEquals((-659L), cSVRecord0.getCharacterPosition());
      assertEquals((-659L), cSVRecord0.getRecordNumber());
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      StringReader stringReader0 = new StringReader(" w");
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVParser cSVParser0 = new CSVParser(stringReader0, cSVFormat0, 0L, 0L);
      CSVRecord cSVRecord0 = new CSVRecord(cSVParser0, (String[]) null, "9VB7", 0L, 0L);
      int int0 = cSVRecord0.size();
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      String[] stringArray0 = new String[4];
      CSVRecord cSVRecord0 = new CSVRecord((CSVParser) null, stringArray0, "id[Pk>HYBD85OD50", (-2358L), (-1L));
      HashMap<String, String> hashMap0 = new HashMap<String, String>();
      hashMap0.put("SF", "Mapping for %s not found, expected one of %s");
      cSVRecord0.putIn(hashMap0);
      assertEquals((-2358L), cSVRecord0.getRecordNumber());
      assertEquals((-1L), cSVRecord0.getCharacterPosition());
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      PipedReader pipedReader0 = new PipedReader();
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      CSVParser cSVParser0 = new CSVParser(pipedReader0, cSVFormat0, 1537L, 1537L);
      String[] stringArray0 = new String[1];
      CSVRecord cSVRecord0 = new CSVRecord(cSVParser0, stringArray0, "M", (-2784L), (-2348L));
      long long0 = cSVRecord0.getRecordNumber();
      assertEquals((-2784L), long0);
      assertEquals((-2348L), cSVRecord0.getCharacterPosition());
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      PipedReader pipedReader0 = new PipedReader();
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      CSVParser cSVParser0 = CSVParser.parse((Reader) pipedReader0, cSVFormat0);
      String[] stringArray0 = new String[2];
      CSVRecord cSVRecord0 = new CSVRecord(cSVParser0, stringArray0, "", (-1202L), (-1202L));
      cSVRecord0.getComment();
      assertEquals((-1202L), cSVRecord0.getRecordNumber());
      assertEquals((-1202L), cSVRecord0.getCharacterPosition());
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      StringReader stringReader0 = new StringReader(" w");
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVParser cSVParser0 = new CSVParser(stringReader0, cSVFormat0, 0L, 0L);
      CSVRecord cSVRecord0 = cSVParser0.nextRecord();
      long long0 = cSVRecord0.getCharacterPosition();
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      StringReader stringReader0 = new StringReader(" w");
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVParser cSVParser0 = new CSVParser(stringReader0, cSVFormat0, 0L, 0L);
      CSVRecord cSVRecord0 = cSVParser0.nextRecord();
      HashMap<String, String> hashMap0 = new HashMap<String, String>();
      HashMap<String, String> hashMap1 = cSVRecord0.putIn(hashMap0);
      assertSame(hashMap1, hashMap0);
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      PipedWriter pipedWriter0 = new PipedWriter();
      PipedReader pipedReader0 = new PipedReader(pipedWriter0);
      CSVParser cSVParser0 = cSVFormat0.parse(pipedReader0);
      String[] stringArray0 = new String[3];
      CSVRecord cSVRecord0 = new CSVRecord(cSVParser0, stringArray0, "O", 1890L, 0L);
      cSVRecord0.isMapped("iY ");
      assertEquals(1890L, cSVRecord0.getRecordNumber());
      assertEquals(0L, cSVRecord0.getCharacterPosition());
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      String[] stringArray0 = new String[2];
      CSVRecord cSVRecord0 = new CSVRecord((CSVParser) null, stringArray0, "4U>FbqChbaL8*QMw>r~", (-659L), (-659L));
      cSVRecord0.stream();
      assertEquals((-659L), cSVRecord0.getCharacterPosition());
      assertEquals((-659L), cSVRecord0.getRecordNumber());
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      PipedReader pipedReader0 = new PipedReader();
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      CSVParser cSVParser0 = CSVParser.parse((Reader) pipedReader0, cSVFormat0);
      String[] stringArray0 = new String[2];
      CSVRecord cSVRecord0 = new CSVRecord(cSVParser0, stringArray0, "xUp\"l09Fh|", 1310L, 1310L);
      cSVRecord0.toList();
      assertEquals(1310L, cSVRecord0.getCharacterPosition());
      assertEquals(1310L, cSVRecord0.getRecordNumber());
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      PipedReader pipedReader0 = new PipedReader();
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      CSVParser cSVParser0 = CSVParser.parse((Reader) pipedReader0, cSVFormat0);
      String[] stringArray0 = new String[2];
      CSVRecord cSVRecord0 = new CSVRecord(cSVParser0, stringArray0, "xUp\"l09Fh|", 1310L, 1310L);
      boolean boolean0 = cSVRecord0.isSet(866);
      assertFalse(boolean0);
      assertEquals(1310L, cSVRecord0.getRecordNumber());
      assertEquals(1310L, cSVRecord0.getCharacterPosition());
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      StringReader stringReader0 = new StringReader(" w");
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVParser cSVParser0 = new CSVParser(stringReader0, cSVFormat0, 0L, 0L);
      CSVRecord cSVRecord0 = cSVParser0.nextRecord();
      boolean boolean0 = cSVRecord0.isSet((-1483));
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVParser cSVParser0 = CSVParser.parse("!:T|/~B6Ht", cSVFormat0);
      CSVRecord cSVRecord0 = cSVParser0.nextRecord();
      boolean boolean0 = cSVRecord0.isConsistent();
      assertEquals(1L, cSVRecord0.getRecordNumber());
      assertTrue(boolean0);
      assertEquals(0L, cSVRecord0.getCharacterPosition());
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      PipedReader pipedReader0 = new PipedReader();
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      CSVParser cSVParser0 = CSVParser.parse((Reader) pipedReader0, cSVFormat0);
      String[] stringArray0 = new String[2];
      CSVRecord cSVRecord0 = new CSVRecord(cSVParser0, stringArray0, "xUp\"l09Fh|", 1310L, 1310L);
      boolean boolean0 = cSVRecord0.hasComment();
      assertEquals(1310L, cSVRecord0.getCharacterPosition());
      assertEquals(1310L, cSVRecord0.getRecordNumber());
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      StringReader stringReader0 = new StringReader("The delimiter cannot be empty");
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVParser cSVParser0 = new CSVParser(stringReader0, cSVFormat0, (-1L), (-1L));
      CSVRecord cSVRecord0 = cSVParser0.nextRecord();
      boolean boolean0 = cSVRecord0.hasComment();
      assertEquals((-1L), cSVRecord0.getRecordNumber());
      assertFalse(boolean0);
      assertEquals((-1L), cSVRecord0.getCharacterPosition());
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      String[] stringArray0 = new String[8];
      CSVRecord cSVRecord0 = new CSVRecord((CSVParser) null, stringArray0, "gb? !@Tx)Q", 0L, 1L);
      boolean boolean0 = cSVRecord0.isSet("S*'}babMk(,9LxF*Ya");
      assertEquals(0L, cSVRecord0.getRecordNumber());
      assertFalse(boolean0);
      assertEquals(1L, cSVRecord0.getCharacterPosition());
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVParser cSVParser0 = CSVParser.parse("!:T|/~B6Ht", cSVFormat0);
      CSVRecord cSVRecord0 = cSVParser0.nextRecord();
      // Undeclared exception!
      try { 
        cSVRecord0.get("E8FP&");
        fail("Expecting exception: IllegalStateException");
      
      } catch(IllegalStateException e) {
         //
         // No header mapping was specified, the record values can't be accessed by name
         //
         verifyException("org.apache.commons.csv.CSVRecord", e);
      }
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      StringReader stringReader0 = new StringReader(" w");
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVParser cSVParser0 = new CSVParser(stringReader0, cSVFormat0, 0L, 0L);
      CSVRecord cSVRecord0 = new CSVRecord(cSVParser0, (String[]) null, " w", (-2212L), 1L);
      cSVRecord0.toList();
      assertEquals(1L, cSVRecord0.getCharacterPosition());
      assertEquals((-2212L), cSVRecord0.getRecordNumber());
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      StringReader stringReader0 = new StringReader(" w");
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVParser cSVParser0 = new CSVParser(stringReader0, cSVFormat0, 0L, 0L);
      CSVRecord cSVRecord0 = cSVParser0.nextRecord();
      int int0 = cSVRecord0.size();
      assertEquals(1, int0);
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVParser cSVParser0 = CSVParser.parse("!:T|/~B6Ht", cSVFormat0);
      CSVRecord cSVRecord0 = cSVParser0.nextRecord();
      long long0 = cSVRecord0.getRecordNumber();
      assertEquals(1L, long0);
      assertEquals(1, cSVRecord0.size());
      assertEquals(0L, cSVRecord0.getCharacterPosition());
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      StringReader stringReader0 = new StringReader("The delimiter cannot be a line break");
      CSVParser cSVParser0 = cSVFormat0.parse(stringReader0);
      String[] stringArray0 = new String[8];
      CSVRecord cSVRecord0 = new CSVRecord(cSVParser0, stringArray0, "org.apache.commons.csv.CSVParser", (-902L), 0L);
      cSVRecord0.getParser();
      assertEquals(8, cSVRecord0.size());
      assertEquals(0L, cSVRecord0.getCharacterPosition());
      assertEquals((-902L), cSVRecord0.getRecordNumber());
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      StringReader stringReader0 = new StringReader(" w");
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVParser cSVParser0 = new CSVParser(stringReader0, cSVFormat0, 0L, 0L);
      CSVRecord cSVRecord0 = cSVParser0.nextRecord();
      // Undeclared exception!
      try { 
        cSVRecord0.get((-1820));
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -1820
         //
         verifyException("org.apache.commons.csv.CSVRecord", e);
      }
  }

  @Test(timeout = 4000)
  public void test22()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVParser cSVParser0 = CSVParser.parse("!:T|/~B6Ht", cSVFormat0);
      CSVRecord cSVRecord0 = cSVParser0.nextRecord();
      String[] stringArray0 = cSVRecord0.values();
      assertEquals(1L, cSVRecord0.getRecordNumber());
      assertEquals(1, stringArray0.length);
      assertEquals(0L, cSVRecord0.getCharacterPosition());
  }

  @Test(timeout = 4000)
  public void test23()  throws Throwable  {
      PipedReader pipedReader0 = new PipedReader();
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      CSVParser cSVParser0 = CSVParser.parse((Reader) pipedReader0, cSVFormat0);
      String[] stringArray0 = new String[2];
      CSVRecord cSVRecord0 = new CSVRecord(cSVParser0, stringArray0, "xUp\"l09Fh|", 1310L, 1310L);
      String string0 = cSVRecord0.toString();
      assertEquals("CSVRecord [comment='xUp\"l09Fh|', recordNumber=1310, values=[null, null]]", string0);
      assertEquals(1310L, cSVRecord0.getCharacterPosition());
  }

  @Test(timeout = 4000)
  public void test24()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('*');
      CSVParser cSVParser0 = CSVParser.parse("', recordNumber=", cSVFormat0);
      CSVRecord cSVRecord0 = cSVParser0.nextRecord();
      cSVRecord0.toMap();
      assertEquals(1L, cSVRecord0.getRecordNumber());
      assertEquals(1, cSVRecord0.size());
      assertEquals(0L, cSVRecord0.getCharacterPosition());
  }

  @Test(timeout = 4000)
  public void test25()  throws Throwable  {
      String[] stringArray0 = new String[2];
      CSVRecord cSVRecord0 = new CSVRecord((CSVParser) null, stringArray0, "4U>FbqChbaL8*QMw>r~", (-659L), (-659L));
      long long0 = cSVRecord0.getCharacterPosition();
      assertEquals((-659L), cSVRecord0.getRecordNumber());
      assertEquals(2, cSVRecord0.size());
      assertEquals((-659L), long0);
  }

  @Test(timeout = 4000)
  public void test26()  throws Throwable  {
      PipedReader pipedReader0 = new PipedReader();
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      CSVParser cSVParser0 = new CSVParser(pipedReader0, cSVFormat0, 1537L, 1537L);
      String[] stringArray0 = new String[1];
      CSVRecord cSVRecord0 = new CSVRecord(cSVParser0, stringArray0, "No header mapping was specified, the record values can't be accessed by name", 1537L, 1537L);
      cSVRecord0.getComment();
      assertEquals(1537L, cSVRecord0.getRecordNumber());
      assertEquals(1, cSVRecord0.size());
      assertEquals(1537L, cSVRecord0.getCharacterPosition());
  }

  @Test(timeout = 4000)
  public void test27()  throws Throwable  {
      StringReader stringReader0 = new StringReader(" w");
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVParser cSVParser0 = new CSVParser(stringReader0, cSVFormat0, 0L, 0L);
      String[] stringArray0 = new String[0];
      CSVRecord cSVRecord0 = new CSVRecord(cSVParser0, stringArray0, "{wx;IXJL", (-2483L), (-2483L));
      cSVRecord0.iterator();
      assertEquals((-2483L), cSVRecord0.getRecordNumber());
      assertEquals((-2483L), cSVRecord0.getCharacterPosition());
  }
}
