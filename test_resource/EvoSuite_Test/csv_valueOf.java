/*
 * This file was automatically generated by EvoSuite
 * Wed Jun 18 08:35:40 GMT 2025
 */

package org.apache.commons.csv;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.shaded.org.mockito.Mockito.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.io.CharArrayWriter;
import java.io.File;
import java.io.IOException;
import java.io.PipedOutputStream;
import java.io.PipedReader;
import java.io.PipedWriter;
import java.io.StringWriter;
import java.nio.CharBuffer;
import java.nio.ReadOnlyBufferException;
import java.nio.charset.Charset;
import java.nio.file.Path;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import javax.sql.rowset.RowSetMetaDataImpl;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVPrinter;
import org.apache.commons.csv.DuplicateHeaderMode;
import org.apache.commons.csv.QuoteMode;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.evosuite.runtime.ViolatedAssumptionAnswer;
import org.evosuite.runtime.mock.java.io.MockFile;
import org.evosuite.runtime.mock.java.io.MockPrintStream;
import org.evosuite.runtime.mock.java.io.MockPrintWriter;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true) 
public class CSVFormat_ESTest extends CSVFormat_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test000()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.withTrim(false);
      assertFalse(cSVFormat1.getTrim());
  }

  @Test(timeout = 4000)
  public void test001()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVFormat cSVFormat1 = cSVFormat0.withSkipHeaderRecord();
      CSVFormat cSVFormat2 = cSVFormat1.withTrim(false);
      assertFalse(cSVFormat2.getTrim());
      assertTrue(cSVFormat2.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test002()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat(';');
      CSVFormat cSVFormat1 = cSVFormat0.TDF.withTrim(true);
      assertEquals(";", cSVFormat0.getDelimiterString());
      assertFalse(cSVFormat0.getIgnoreHeaderCase());
      assertTrue(cSVFormat1.getTrim());
      assertFalse(cSVFormat0.getSkipHeaderRecord());
      assertFalse(cSVFormat0.getAllowMissingColumnNames());
      assertFalse(cSVFormat0.getTrailingDelimiter());
      assertFalse(cSVFormat0.getIgnoreEmptyLines());
      assertFalse(cSVFormat0.getTrim());
      assertFalse(cSVFormat0.getAutoFlush());
      assertFalse(cSVFormat0.getIgnoreSurroundingSpaces());
  }

  @Test(timeout = 4000)
  public void test003()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('v');
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreHeaderCase();
      CSVFormat cSVFormat2 = cSVFormat1.withTrim(true);
      assertFalse(cSVFormat2.isNullStringSet());
      assertFalse(cSVFormat2.equals((Object)cSVFormat1));
      assertFalse(cSVFormat2.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat2.getAutoFlush());
      assertFalse(cSVFormat1.equals((Object)cSVFormat0));
      assertFalse(cSVFormat2.getTrailingDelimiter());
      assertFalse(cSVFormat2.getIgnoreEmptyLines());
      assertFalse(cSVFormat2.getAllowMissingColumnNames());
      assertFalse(cSVFormat2.getSkipHeaderRecord());
      assertEquals("v", cSVFormat2.getDelimiterString());
  }

  @Test(timeout = 4000)
  public void test004()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      CSVFormat cSVFormat1 = cSVFormat0.withTrim(false);
      assertFalse(cSVFormat1.getTrim());
  }

  @Test(timeout = 4000)
  public void test005()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat cSVFormat1 = cSVFormat0.withTrim();
      assertTrue(cSVFormat1.getTrim());
  }

  @Test(timeout = 4000)
  public void test006()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVFormat cSVFormat1 = cSVFormat0.withSkipHeaderRecord();
      CSVFormat cSVFormat2 = cSVFormat1.withTrim();
      assertTrue(cSVFormat2.getSkipHeaderRecord());
      assertTrue(cSVFormat2.getTrim());
  }

  @Test(timeout = 4000)
  public void test007()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVFormat cSVFormat1 = cSVFormat0.withSkipHeaderRecord();
      CSVFormat cSVFormat2 = cSVFormat1.withTrailingDelimiter(false);
      assertTrue(cSVFormat2.getSkipHeaderRecord());
      assertFalse(cSVFormat2.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test008()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat(';');
      CSVFormat cSVFormat1 = cSVFormat0.INFORMIX_UNLOAD.withAllowDuplicateHeaderNames(true);
      CSVFormat cSVFormat2 = cSVFormat1.withTrailingDelimiter(true);
      assertFalse(cSVFormat0.getIgnoreHeaderCase());
      assertFalse(cSVFormat0.getSkipHeaderRecord());
      assertFalse(cSVFormat0.getAllowMissingColumnNames());
      assertEquals(';', cSVFormat0.getDelimiter());
      assertTrue(cSVFormat2.getTrailingDelimiter());
      assertFalse(cSVFormat0.getTrailingDelimiter());
      assertFalse(cSVFormat0.getAutoFlush());
      assertFalse(cSVFormat0.getIgnoreEmptyLines());
      assertFalse(cSVFormat0.getTrim());
      assertTrue(cSVFormat2.getAllowDuplicateHeaderNames());
      assertFalse(cSVFormat0.getIgnoreSurroundingSpaces());
  }

  @Test(timeout = 4000)
  public void test009()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withAllowMissingColumnNames();
      CSVFormat cSVFormat2 = cSVFormat1.withTrailingDelimiter(false);
      assertTrue(cSVFormat2.getAllowMissingColumnNames());
      assertFalse(cSVFormat2.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test010()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withCommentMarker('F');
      CSVFormat cSVFormat2 = cSVFormat1.withTrailingDelimiter();
      assertEquals('F', (char)cSVFormat2.getCommentMarker());
      assertTrue(cSVFormat2.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test011()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      CSVFormat cSVFormat1 = cSVFormat0.withSkipHeaderRecord();
      CSVFormat cSVFormat2 = cSVFormat1.withTrailingDelimiter();
      assertTrue(cSVFormat2.getSkipHeaderRecord());
      assertTrue(cSVFormat2.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test012()  throws Throwable  {
      CSVFormat.Predefined cSVFormat_Predefined0 = CSVFormat.Predefined.PostgreSQLCsv;
      CSVFormat cSVFormat0 = cSVFormat_Predefined0.getFormat();
      CSVFormat cSVFormat1 = cSVFormat0.EXCEL.withTrailingDelimiter();
      assertTrue(cSVFormat1.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test013()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('v');
      CSVFormat cSVFormat1 = cSVFormat0.withSystemRecordSeparator();
      assertFalse(cSVFormat1.getAutoFlush());
      assertFalse(cSVFormat1.getTrailingDelimiter());
      assertEquals("v", cSVFormat1.getDelimiterString());
      assertFalse(cSVFormat1.getSkipHeaderRecord());
      assertFalse(cSVFormat1.getIgnoreEmptyLines());
      assertFalse(cSVFormat1.getIgnoreHeaderCase());
      assertFalse(cSVFormat1.getAllowMissingColumnNames());
      assertFalse(cSVFormat1.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat1.getTrim());
      assertFalse(cSVFormat1.isNullStringSet());
  }

  @Test(timeout = 4000)
  public void test014()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.EXCEL.withSystemRecordSeparator();
      assertFalse(cSVFormat1.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test015()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withSkipHeaderRecord(true);
      assertTrue(cSVFormat1.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test016()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('j');
      CSVFormat cSVFormat1 = cSVFormat0.withTrailingDelimiter();
      CSVFormat cSVFormat2 = cSVFormat1.withSkipHeaderRecord(false);
      assertFalse(cSVFormat2.getTrim());
      assertFalse(cSVFormat2.isNullStringSet());
      assertFalse(cSVFormat2.getAllowMissingColumnNames());
      assertFalse(cSVFormat2.getIgnoreEmptyLines());
      assertFalse(cSVFormat2.getIgnoreHeaderCase());
      assertFalse(cSVFormat2.getIgnoreSurroundingSpaces());
      assertEquals('j', cSVFormat2.getDelimiter());
      assertFalse(cSVFormat2.getAutoFlush());
      assertTrue(cSVFormat2.equals((Object)cSVFormat1));
      assertFalse(cSVFormat2.equals((Object)cSVFormat0));
  }

  @Test(timeout = 4000)
  public void test017()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      CSVFormat cSVFormat1 = cSVFormat0.withSkipHeaderRecord(true);
      assertTrue(cSVFormat1.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test018()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('3');
      CSVFormat cSVFormat1 = cSVFormat0.withAllowDuplicateHeaderNames(false);
      CSVFormat cSVFormat2 = cSVFormat1.withSkipHeaderRecord(false);
      assertFalse(cSVFormat2.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat2.getAutoFlush());
      assertTrue(cSVFormat2.equals((Object)cSVFormat1));
      assertFalse(cSVFormat2.getIgnoreEmptyLines());
      assertEquals(DuplicateHeaderMode.ALLOW_EMPTY, cSVFormat2.getDuplicateHeaderMode());
      assertFalse(cSVFormat2.getTrim());
      assertFalse(cSVFormat2.isNullStringSet());
      assertFalse(cSVFormat2.getAllowMissingColumnNames());
      assertEquals("3", cSVFormat2.getDelimiterString());
      assertFalse(cSVFormat2.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test019()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withCommentMarker('F');
      CSVFormat cSVFormat2 = cSVFormat1.withSkipHeaderRecord();
      assertTrue(cSVFormat2.getSkipHeaderRecord());
      assertEquals('F', (char)cSVFormat2.getCommentMarker());
  }

  @Test(timeout = 4000)
  public void test020()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withTrailingDelimiter();
      CSVFormat cSVFormat2 = cSVFormat1.withSkipHeaderRecord();
      assertTrue(cSVFormat2.getSkipHeaderRecord());
      assertTrue(cSVFormat2.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test021()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('3');
      CSVFormat cSVFormat1 = cSVFormat0.withAllowDuplicateHeaderNames(false);
      CSVFormat cSVFormat2 = cSVFormat1.withSkipHeaderRecord();
      assertFalse(cSVFormat2.getIgnoreHeaderCase());
      assertFalse(cSVFormat2.getAllowMissingColumnNames());
      assertFalse(cSVFormat2.getIgnoreEmptyLines());
      assertFalse(cSVFormat2.getAllowDuplicateHeaderNames());
      assertFalse(cSVFormat2.equals((Object)cSVFormat1));
      assertFalse(cSVFormat2.getTrailingDelimiter());
      assertEquals("3", cSVFormat2.getDelimiterString());
      assertFalse(cSVFormat2.getAutoFlush());
      assertFalse(cSVFormat2.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat2.getTrim());
      assertFalse(cSVFormat2.isNullStringSet());
  }

  @Test(timeout = 4000)
  public void test022()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('v');
      Character character0 = Character.valueOf('c');
      CSVFormat cSVFormat1 = cSVFormat0.withCommentMarker(character0);
      CSVFormat cSVFormat2 = cSVFormat1.withRecordSeparator("lIO[:X7jt<4");
      assertFalse(cSVFormat2.getIgnoreHeaderCase());
      assertFalse(cSVFormat2.getAllowMissingColumnNames());
      assertFalse(cSVFormat2.getIgnoreEmptyLines());
      assertFalse(cSVFormat2.getSkipHeaderRecord());
      assertFalse(cSVFormat2.getTrailingDelimiter());
      assertFalse(cSVFormat2.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat2.getTrim());
      assertEquals('v', cSVFormat2.getDelimiter());
      assertFalse(cSVFormat2.getAutoFlush());
      assertFalse(cSVFormat2.isNullStringSet());
  }

  @Test(timeout = 4000)
  public void test023()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withRecordSeparator("");
      assertEquals('\\', (char)cSVFormat1.getEscapeCharacter());
  }

  @Test(timeout = 4000)
  public void test024()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('v');
      CSVFormat cSVFormat1 = cSVFormat0.withTrailingDelimiter();
      CSVFormat cSVFormat2 = cSVFormat1.withNullString("lIO[:X7jt<4");
      CSVFormat cSVFormat3 = cSVFormat2.withRecordSeparator("lIO[:X7jt<4");
      assertFalse(cSVFormat3.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat3.getIgnoreEmptyLines());
      assertFalse(cSVFormat3.getAutoFlush());
      assertFalse(cSVFormat1.isNullStringSet());
      assertEquals("v", cSVFormat3.getDelimiterString());
      assertFalse(cSVFormat1.equals((Object)cSVFormat0));
      assertFalse(cSVFormat3.getSkipHeaderRecord());
      assertFalse(cSVFormat3.getAllowMissingColumnNames());
      assertFalse(cSVFormat3.getIgnoreHeaderCase());
      assertFalse(cSVFormat3.getTrim());
  }

  @Test(timeout = 4000)
  public void test025()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat cSVFormat1 = cSVFormat0.withSkipHeaderRecord();
      CSVFormat cSVFormat2 = cSVFormat1.withRecordSeparator("/");
      assertTrue(cSVFormat2.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test026()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      CSVFormat cSVFormat1 = cSVFormat0.withRecordSeparator("");
      assertEquals("\t", cSVFormat1.getDelimiterString());
  }

  @Test(timeout = 4000)
  public void test027()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('v');
      Character character0 = Character.valueOf('c');
      CSVFormat cSVFormat1 = cSVFormat0.withCommentMarker(character0);
      CSVFormat cSVFormat2 = cSVFormat1.withRecordSeparator('c');
      assertFalse(cSVFormat2.getAutoFlush());
      assertFalse(cSVFormat2.getTrailingDelimiter());
      assertEquals("c", cSVFormat2.getRecordSeparator());
      assertFalse(cSVFormat2.getTrim());
      assertFalse(cSVFormat2.getAllowMissingColumnNames());
      assertFalse(cSVFormat2.getIgnoreHeaderCase());
      assertFalse(cSVFormat2.getIgnoreEmptyLines());
      assertFalse(cSVFormat2.getIgnoreSurroundingSpaces());
      assertEquals('v', cSVFormat2.getDelimiter());
  }

  @Test(timeout = 4000)
  public void test028()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('v');
      CSVFormat cSVFormat1 = cSVFormat0.withTrailingDelimiter();
      CSVFormat cSVFormat2 = cSVFormat1.withRecordSeparator('E');
      assertFalse(cSVFormat2.getSkipHeaderRecord());
      assertFalse(cSVFormat2.getAutoFlush());
      assertFalse(cSVFormat2.isNullStringSet());
      assertEquals("E", cSVFormat2.getRecordSeparator());
      assertFalse(cSVFormat2.getIgnoreSurroundingSpaces());
      assertEquals('v', cSVFormat2.getDelimiter());
      assertFalse(cSVFormat2.getTrim());
      assertFalse(cSVFormat2.getAllowMissingColumnNames());
      assertFalse(cSVFormat1.equals((Object)cSVFormat0));
      assertFalse(cSVFormat2.getIgnoreEmptyLines());
      assertFalse(cSVFormat2.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test029()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withSkipHeaderRecord();
      CSVFormat cSVFormat2 = cSVFormat1.withRecordSeparator('');
      assertEquals("", cSVFormat2.getRecordSeparator());
      assertTrue(cSVFormat2.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test030()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.withRecordSeparator('/');
      assertEquals("/", cSVFormat1.getRecordSeparator());
  }

  @Test(timeout = 4000)
  public void test031()  throws Throwable  {
      CSVFormat.Predefined cSVFormat_Predefined0 = CSVFormat.Predefined.MySQL;
      CSVFormat cSVFormat0 = cSVFormat_Predefined0.getFormat();
      CSVFormat cSVFormat1 = cSVFormat0.EXCEL.withRecordSeparator('s');
      assertEquals("s", cSVFormat1.getRecordSeparator());
  }

  @Test(timeout = 4000)
  public void test032()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('v');
      CSVFormat cSVFormat1 = cSVFormat0.withTrailingDelimiter();
      CSVFormat cSVFormat2 = cSVFormat1.withNullString("lIO[:X7jt<4");
      QuoteMode quoteMode0 = QuoteMode.ALL_NON_NULL;
      CSVFormat cSVFormat3 = cSVFormat2.withQuoteMode(quoteMode0);
      assertFalse(cSVFormat1.equals((Object)cSVFormat0));
      assertFalse(cSVFormat3.getSkipHeaderRecord());
      assertFalse(cSVFormat3.getIgnoreEmptyLines());
      assertFalse(cSVFormat3.getIgnoreHeaderCase());
      assertFalse(cSVFormat3.getTrim());
      assertFalse(cSVFormat1.isNullStringSet());
      assertFalse(cSVFormat3.getAllowMissingColumnNames());
      assertEquals('v', cSVFormat3.getDelimiter());
      assertFalse(cSVFormat3.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat3.getAutoFlush());
  }

  @Test(timeout = 4000)
  public void test033()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withSkipHeaderRecord();
      QuoteMode quoteMode0 = QuoteMode.MINIMAL;
      CSVFormat cSVFormat2 = cSVFormat1.withQuoteMode(quoteMode0);
      assertTrue(cSVFormat2.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test034()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      QuoteMode quoteMode0 = QuoteMode.ALL_NON_NULL;
      CSVFormat cSVFormat1 = cSVFormat0.withQuoteMode(quoteMode0);
      assertFalse(cSVFormat1.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test035()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat cSVFormat1 = cSVFormat0.withQuote((Character) null);
      assertEquals("\r\n", cSVFormat1.getRecordSeparator());
  }

  @Test(timeout = 4000)
  public void test036()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withCommentMarker('F');
      Character character0 = new Character('&');
      CSVFormat cSVFormat2 = cSVFormat1.withQuote(character0);
      assertEquals('F', (char)cSVFormat2.getCommentMarker());
  }

  @Test(timeout = 4000)
  public void test037()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withTrailingDelimiter();
      Character character0 = new Character('&');
      CSVFormat cSVFormat2 = cSVFormat1.withQuote(character0);
      assertTrue(cSVFormat2.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test038()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withSkipHeaderRecord();
      Character character0 = new Character('%');
      CSVFormat cSVFormat2 = cSVFormat1.withQuote(character0);
      assertTrue(cSVFormat2.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test039()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVFormat cSVFormat1 = cSVFormat0.withQuote('v');
      assertEquals('v', (char)cSVFormat1.getQuoteCharacter());
  }

  @Test(timeout = 4000)
  public void test040()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withNullString((String) null);
      assertEquals(QuoteMode.MINIMAL, cSVFormat1.getQuoteMode());
  }

  @Test(timeout = 4000)
  public void test041()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat cSVFormat1 = cSVFormat0.MYSQL.withIgnoreSurroundingSpaces();
      CSVFormat cSVFormat2 = cSVFormat1.withNullString("8\"OHG;~>!lT8pp.L6F");
      assertTrue(cSVFormat2.getIgnoreSurroundingSpaces());
  }

  @Test(timeout = 4000)
  public void test042()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      CSVFormat cSVFormat1 = cSVFormat0.withNullString("=j");
      assertEquals('\"', (char)cSVFormat1.getQuoteCharacter());
  }

  @Test(timeout = 4000)
  public void test043()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('v');
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreSurroundingSpaces(false);
      assertFalse(cSVFormat1.getSkipHeaderRecord());
      assertFalse(cSVFormat1.getIgnoreEmptyLines());
      assertFalse(cSVFormat1.getIgnoreHeaderCase());
      assertTrue(cSVFormat1.equals((Object)cSVFormat0));
      assertFalse(cSVFormat1.getAllowMissingColumnNames());
      assertEquals('v', cSVFormat1.getDelimiter());
      assertFalse(cSVFormat1.getTrim());
      assertFalse(cSVFormat1.isNullStringSet());
      assertFalse(cSVFormat1.getAutoFlush());
      assertFalse(cSVFormat1.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test044()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      Character character0 = Character.valueOf('|');
      CSVFormat cSVFormat1 = cSVFormat0.withCommentMarker(character0);
      Class<CSVFormat.Predefined> class0 = CSVFormat.Predefined.class;
      CSVFormat cSVFormat2 = cSVFormat1.withHeader(class0);
      QuoteMode quoteMode0 = QuoteMode.ALL;
      CSVFormat cSVFormat3 = cSVFormat2.withQuoteMode(quoteMode0);
      CSVFormat cSVFormat4 = cSVFormat3.withIgnoreSurroundingSpaces(false);
      assertFalse(cSVFormat4.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat2.equals((Object)cSVFormat1));
  }

  @Test(timeout = 4000)
  public void test045()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreSurroundingSpaces(false);
      assertFalse(cSVFormat1.getIgnoreSurroundingSpaces());
  }

  @Test(timeout = 4000)
  public void test046()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreSurroundingSpaces(true);
      assertTrue(cSVFormat1.getIgnoreSurroundingSpaces());
  }

  @Test(timeout = 4000)
  public void test047()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreSurroundingSpaces();
      assertTrue(cSVFormat1.getIgnoreSurroundingSpaces());
  }

  @Test(timeout = 4000)
  public void test048()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('v');
      CSVFormat cSVFormat1 = cSVFormat0.withTrailingDelimiter();
      CSVFormat cSVFormat2 = cSVFormat1.withIgnoreSurroundingSpaces();
      assertFalse(cSVFormat2.getIgnoreHeaderCase());
      assertFalse(cSVFormat2.getAllowMissingColumnNames());
      assertFalse(cSVFormat2.equals((Object)cSVFormat1));
      assertFalse(cSVFormat2.getIgnoreEmptyLines());
      assertFalse(cSVFormat1.equals((Object)cSVFormat0));
      assertFalse(cSVFormat2.getSkipHeaderRecord());
      assertFalse(cSVFormat2.getAutoFlush());
      assertEquals('v', cSVFormat2.getDelimiter());
      assertFalse(cSVFormat2.getTrim());
      assertFalse(cSVFormat2.isNullStringSet());
  }

  @Test(timeout = 4000)
  public void test049()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat cSVFormat1 = cSVFormat0.withSkipHeaderRecord();
      CSVFormat cSVFormat2 = cSVFormat1.withIgnoreSurroundingSpaces();
      assertTrue(cSVFormat2.getIgnoreSurroundingSpaces());
      assertTrue(cSVFormat2.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test050()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('v');
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreHeaderCase();
      CSVFormat cSVFormat2 = cSVFormat1.withIgnoreSurroundingSpaces();
      assertFalse(cSVFormat1.equals((Object)cSVFormat0));
      assertFalse(cSVFormat2.getAutoFlush());
      assertFalse(cSVFormat2.isNullStringSet());
      assertFalse(cSVFormat2.getTrim());
      assertFalse(cSVFormat2.equals((Object)cSVFormat1));
      assertFalse(cSVFormat2.getTrailingDelimiter());
      assertFalse(cSVFormat2.getSkipHeaderRecord());
      assertFalse(cSVFormat2.getAllowMissingColumnNames());
      assertEquals("v", cSVFormat2.getDelimiterString());
  }

  @Test(timeout = 4000)
  public void test051()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('v');
      CSVFormat cSVFormat1 = cSVFormat0.withAllowMissingColumnNames();
      CSVFormat cSVFormat2 = cSVFormat1.withIgnoreSurroundingSpaces();
      assertFalse(cSVFormat2.getIgnoreHeaderCase());
      assertFalse(cSVFormat2.getTrim());
      assertFalse(cSVFormat1.equals((Object)cSVFormat0));
      assertFalse(cSVFormat2.equals((Object)cSVFormat1));
      assertFalse(cSVFormat2.getIgnoreEmptyLines());
      assertFalse(cSVFormat2.getAutoFlush());
      assertFalse(cSVFormat2.getSkipHeaderRecord());
      assertFalse(cSVFormat2.getTrailingDelimiter());
      assertEquals("v", cSVFormat2.getDelimiterString());
  }

  @Test(timeout = 4000)
  public void test052()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('v');
      QuoteMode quoteMode0 = QuoteMode.NONE;
      CSVFormat cSVFormat1 = cSVFormat0.ORACLE.withQuoteMode(quoteMode0);
      CSVFormat cSVFormat2 = cSVFormat1.withIgnoreHeaderCase(false);
      assertFalse(cSVFormat0.getTrim());
      assertFalse(cSVFormat0.isNullStringSet());
      assertFalse(cSVFormat2.getIgnoreHeaderCase());
      assertFalse(cSVFormat0.getIgnoreEmptyLines());
      assertFalse(cSVFormat0.getIgnoreHeaderCase());
      assertFalse(cSVFormat0.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat0.getTrailingDelimiter());
      assertFalse(cSVFormat0.getAutoFlush());
      assertFalse(cSVFormat0.getSkipHeaderRecord());
      assertFalse(cSVFormat0.getAllowMissingColumnNames());
      assertEquals("v", cSVFormat0.getDelimiterString());
  }

  @Test(timeout = 4000)
  public void test053()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('3');
      CSVFormat cSVFormat1 = cSVFormat0.withTrailingDelimiter(true);
      CSVFormat cSVFormat2 = cSVFormat1.withIgnoreHeaderCase(false);
      assertFalse(cSVFormat2.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat2.getIgnoreEmptyLines());
      assertFalse(cSVFormat2.equals((Object)cSVFormat0));
      assertFalse(cSVFormat2.getTrim());
      assertFalse(cSVFormat2.isNullStringSet());
      assertFalse(cSVFormat2.getAutoFlush());
      assertFalse(cSVFormat2.getAllowMissingColumnNames());
      assertEquals("3", cSVFormat2.getDelimiterString());
      assertFalse(cSVFormat2.getSkipHeaderRecord());
      assertTrue(cSVFormat2.equals((Object)cSVFormat1));
  }

  @Test(timeout = 4000)
  public void test054()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      CSVFormat cSVFormat1 = cSVFormat0.withSkipHeaderRecord();
      CSVFormat cSVFormat2 = cSVFormat1.withHeader((String[]) null);
      CSVFormat cSVFormat3 = cSVFormat2.withIgnoreHeaderCase(true);
      assertTrue(cSVFormat3.getIgnoreHeaderCase());
      assertTrue(cSVFormat3.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test055()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('v');
      CSVFormat cSVFormat1 = cSVFormat0.withAutoFlush(true);
      CSVFormat cSVFormat2 = cSVFormat1.withIgnoreHeaderCase(true);
      assertFalse(cSVFormat2.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat2.getTrailingDelimiter());
      assertFalse(cSVFormat2.getAllowMissingColumnNames());
      assertFalse(cSVFormat2.equals((Object)cSVFormat1));
      assertFalse(cSVFormat2.getTrim());
      assertFalse(cSVFormat2.isNullStringSet());
      assertFalse(cSVFormat2.getSkipHeaderRecord());
      assertEquals("v", cSVFormat2.getDelimiterString());
      assertFalse(cSVFormat1.equals((Object)cSVFormat0));
      assertFalse(cSVFormat2.getIgnoreEmptyLines());
  }

  @Test(timeout = 4000)
  public void test056()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('v');
      CSVFormat cSVFormat1 = cSVFormat0.withAllowMissingColumnNames();
      CSVFormat cSVFormat2 = cSVFormat1.withIgnoreHeaderCase(false);
      assertFalse(cSVFormat2.getIgnoreSurroundingSpaces());
      assertTrue(cSVFormat2.equals((Object)cSVFormat1));
      assertFalse(cSVFormat2.getTrim());
      assertFalse(cSVFormat2.getIgnoreEmptyLines());
      assertFalse(cSVFormat2.getSkipHeaderRecord());
      assertEquals("v", cSVFormat2.getDelimiterString());
      assertFalse(cSVFormat2.getAutoFlush());
      assertFalse(cSVFormat2.getTrailingDelimiter());
      assertFalse(cSVFormat2.equals((Object)cSVFormat0));
  }

  @Test(timeout = 4000)
  public void test057()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      CSVFormat cSVFormat1 = cSVFormat0.withSkipHeaderRecord();
      CSVFormat cSVFormat2 = cSVFormat1.withIgnoreHeaderCase();
      assertTrue(cSVFormat2.getSkipHeaderRecord());
      assertTrue(cSVFormat2.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test058()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreHeaderCase();
      QuoteMode quoteMode0 = QuoteMode.MINIMAL;
      CSVFormat cSVFormat2 = cSVFormat1.withQuoteMode(quoteMode0);
      assertTrue(cSVFormat2.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test059()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('v');
      CSVFormat cSVFormat1 = cSVFormat0.withAutoFlush(true);
      CSVFormat cSVFormat2 = cSVFormat1.withIgnoreHeaderCase();
      assertFalse(cSVFormat2.getSkipHeaderRecord());
      assertFalse(cSVFormat2.getTrailingDelimiter());
      assertFalse(cSVFormat2.getIgnoreEmptyLines());
      assertFalse(cSVFormat2.getAllowMissingColumnNames());
      assertFalse(cSVFormat2.equals((Object)cSVFormat1));
      assertFalse(cSVFormat1.equals((Object)cSVFormat0));
      assertEquals('v', cSVFormat2.getDelimiter());
      assertFalse(cSVFormat2.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat2.getTrim());
  }

  @Test(timeout = 4000)
  public void test060()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('v');
      CSVFormat cSVFormat1 = cSVFormat0.withAllowMissingColumnNames();
      CSVFormat cSVFormat2 = cSVFormat1.withIgnoreHeaderCase();
      assertFalse(cSVFormat2.equals((Object)cSVFormat1));
      assertFalse(cSVFormat2.getIgnoreEmptyLines());
      assertFalse(cSVFormat1.equals((Object)cSVFormat0));
      assertFalse(cSVFormat2.getAutoFlush());
      assertFalse(cSVFormat2.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat2.getTrim());
      assertEquals("v", cSVFormat2.getDelimiterString());
      assertFalse(cSVFormat2.getTrailingDelimiter());
      assertFalse(cSVFormat2.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test061()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('3');
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreEmptyLines(false);
      assertFalse(cSVFormat1.getAllowMissingColumnNames());
      assertFalse(cSVFormat1.getIgnoreHeaderCase());
      assertEquals("3", cSVFormat1.getDelimiterString());
      assertFalse(cSVFormat1.getAutoFlush());
      assertFalse(cSVFormat1.getTrim());
      assertFalse(cSVFormat1.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat1.getSkipHeaderRecord());
      assertTrue(cSVFormat1.equals((Object)cSVFormat0));
      assertFalse(cSVFormat1.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test062()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreEmptyLines(true);
      assertTrue(cSVFormat1.getIgnoreEmptyLines());
  }

  @Test(timeout = 4000)
  public void test063()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('v');
      CSVFormat cSVFormat1 = cSVFormat0.EXCEL.withIgnoreEmptyLines(false);
      assertFalse(cSVFormat0.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat0.getIgnoreEmptyLines());
      assertFalse(cSVFormat0.getTrim());
      assertFalse(cSVFormat0.getIgnoreHeaderCase());
      assertFalse(cSVFormat0.isNullStringSet());
      assertFalse(cSVFormat0.getAutoFlush());
      assertFalse(cSVFormat1.getIgnoreEmptyLines());
      assertFalse(cSVFormat0.getTrailingDelimiter());
      assertEquals("v", cSVFormat0.getDelimiterString());
      assertFalse(cSVFormat0.getAllowMissingColumnNames());
      assertFalse(cSVFormat0.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test064()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MYSQL;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreEmptyLines();
      assertTrue(cSVFormat1.getIgnoreEmptyLines());
  }

  @Test(timeout = 4000)
  public void test065()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      CSVFormat cSVFormat1 = cSVFormat0.withTrim(true);
      CSVFormat cSVFormat2 = cSVFormat1.withIgnoreEmptyLines();
      assertTrue(cSVFormat2.getIgnoreEmptyLines());
      assertTrue(cSVFormat2.getTrim());
  }

  @Test(timeout = 4000)
  public void test066()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat cSVFormat1 = cSVFormat0.withFirstRecordAsHeader();
      CSVFormat cSVFormat2 = cSVFormat1.withQuote('{');
      CSVFormat cSVFormat3 = cSVFormat2.withIgnoreEmptyLines();
      assertTrue(cSVFormat3.getSkipHeaderRecord());
      assertTrue(cSVFormat3.getIgnoreEmptyLines());
      assertEquals('{', (char)cSVFormat3.getQuoteCharacter());
  }

  @Test(timeout = 4000)
  public void test067()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      CSVFormat cSVFormat1 = cSVFormat0.TDF.withIgnoreEmptyLines();
      assertTrue(cSVFormat1.getIgnoreEmptyLines());
  }

  @Test(timeout = 4000)
  public void test068()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat cSVFormat1 = cSVFormat0.withAllowMissingColumnNames();
      CSVFormat cSVFormat2 = cSVFormat1.withIgnoreEmptyLines();
      assertTrue(cSVFormat2.getAllowMissingColumnNames());
      assertTrue(cSVFormat2.getIgnoreEmptyLines());
  }

  @Test(timeout = 4000)
  public void test069()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      Object[] objectArray0 = new Object[7];
      CSVFormat cSVFormat1 = cSVFormat0.withHeaderComments(objectArray0);
      assertEquals(',', cSVFormat1.getDelimiter());
  }

  @Test(timeout = 4000)
  public void test070()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      Object[] objectArray0 = new Object[9];
      CSVFormat cSVFormat1 = cSVFormat0.withHeaderComments(objectArray0);
      assertFalse(cSVFormat1.getTrim());
  }

  @Test(timeout = 4000)
  public void test071()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      Object[] objectArray0 = new Object[8];
      CSVFormat cSVFormat1 = cSVFormat0.withHeaderComments(objectArray0);
      assertEquals(DuplicateHeaderMode.ALLOW_ALL, cSVFormat1.getDuplicateHeaderMode());
  }

  @Test(timeout = 4000)
  public void test072()  throws Throwable  {
      CSVFormat[] cSVFormatArray0 = new CSVFormat[8];
      CSVFormat cSVFormat0 = cSVFormatArray0[0].POSTGRESQL_TEXT.withHeader((String[]) null);
      assertFalse(cSVFormat0.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test073()  throws Throwable  {
      CSVFormat.Predefined cSVFormat_Predefined0 = CSVFormat.Predefined.MySQL;
      CSVFormat cSVFormat0 = cSVFormat_Predefined0.getFormat();
      CSVFormat cSVFormat1 = cSVFormat0.ORACLE.withHeader((String[]) null);
      assertFalse(cSVFormat1.isCommentMarkerSet());
  }

  @Test(timeout = 4000)
  public void test074()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      CSVFormat cSVFormat1 = cSVFormat0.EXCEL.withHeader((String[]) null);
      assertFalse(cSVFormat1.getIgnoreSurroundingSpaces());
  }

  @Test(timeout = 4000)
  public void test075()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('v');
      CSVFormat cSVFormat1 = cSVFormat0.withAutoFlush(true);
      CSVFormat cSVFormat2 = cSVFormat1.withHeader((ResultSetMetaData) null);
      assertFalse(cSVFormat2.getTrailingDelimiter());
      assertFalse(cSVFormat2.getIgnoreSurroundingSpaces());
      assertEquals('v', cSVFormat2.getDelimiter());
      assertFalse(cSVFormat2.getIgnoreEmptyLines());
      assertFalse(cSVFormat2.equals((Object)cSVFormat0));
      assertFalse(cSVFormat2.getIgnoreHeaderCase());
      assertFalse(cSVFormat2.getTrim());
      assertFalse(cSVFormat2.getAllowMissingColumnNames());
      assertFalse(cSVFormat2.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test076()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      RowSetMetaDataImpl rowSetMetaDataImpl0 = new RowSetMetaDataImpl();
      CSVFormat cSVFormat1 = cSVFormat0.withHeader((ResultSetMetaData) rowSetMetaDataImpl0);
      assertEquals("\r\n", cSVFormat1.getRecordSeparator());
  }

  @Test(timeout = 4000)
  public void test077()  throws Throwable  {
      CSVFormat.Predefined cSVFormat_Predefined0 = CSVFormat.Predefined.MySQL;
      CSVFormat cSVFormat0 = cSVFormat_Predefined0.getFormat();
      Class<CSVFormat.Predefined> class0 = CSVFormat.Predefined.class;
      CSVFormat cSVFormat1 = cSVFormat0.withHeader(class0);
      assertFalse(cSVFormat1.equals((Object)cSVFormat0));
  }

  @Test(timeout = 4000)
  public void test078()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVFormat cSVFormat1 = cSVFormat0.withTrim();
      Class<CSVFormat.Predefined> class0 = CSVFormat.Predefined.class;
      CSVFormat cSVFormat2 = cSVFormat1.withHeader(class0);
      assertFalse(cSVFormat2.equals((Object)cSVFormat1));
      assertTrue(cSVFormat2.getTrim());
  }

  @Test(timeout = 4000)
  public void test079()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVFormat cSVFormat1 = cSVFormat0.withSkipHeaderRecord();
      Class<CSVFormat.Predefined> class0 = CSVFormat.Predefined.class;
      CSVFormat cSVFormat2 = cSVFormat1.withHeader(class0);
      assertTrue(cSVFormat2.getSkipHeaderRecord());
      assertFalse(cSVFormat2.equals((Object)cSVFormat1));
  }

  @Test(timeout = 4000)
  public void test080()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      Class<CSVFormat.Predefined> class0 = CSVFormat.Predefined.class;
      CSVFormat cSVFormat1 = cSVFormat0.withHeader(class0);
      assertFalse(cSVFormat1.equals((Object)cSVFormat0));
  }

  @Test(timeout = 4000)
  public void test081()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreSurroundingSpaces(true);
      CSVFormat cSVFormat2 = cSVFormat1.withAllowDuplicateHeaderNames();
      CSVFormat cSVFormat3 = cSVFormat2.withFirstRecordAsHeader();
      assertTrue(cSVFormat3.getIgnoreSurroundingSpaces());
      assertTrue(cSVFormat3.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test082()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVFormat cSVFormat1 = cSVFormat0.withFirstRecordAsHeader();
      assertTrue(cSVFormat1.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test083()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      CSVFormat cSVFormat1 = cSVFormat0.withFirstRecordAsHeader();
      assertTrue(cSVFormat1.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test084()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.withEscape((Character) null);
      assertFalse(cSVFormat1.isCommentMarkerSet());
  }

  @Test(timeout = 4000)
  public void test085()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('v');
      Character character0 = Character.valueOf('c');
      Character character1 = Character.valueOf('E');
      CSVFormat cSVFormat1 = cSVFormat0.withCommentMarker(character1);
      CSVFormat cSVFormat2 = cSVFormat1.withEscape(character0);
      assertFalse(cSVFormat2.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat2.getAutoFlush());
      assertFalse(cSVFormat2.getTrailingDelimiter());
      assertFalse(cSVFormat2.getSkipHeaderRecord());
      assertFalse(cSVFormat2.getAllowMissingColumnNames());
      assertFalse(cSVFormat2.getIgnoreHeaderCase());
      assertFalse(cSVFormat2.getTrim());
      assertFalse(cSVFormat2.isNullStringSet());
      assertFalse(cSVFormat2.getIgnoreEmptyLines());
  }

  @Test(timeout = 4000)
  public void test086()  throws Throwable  {
      Character character0 = Character.valueOf('J');
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withEscape(character0);
      assertNull(cSVFormat1.getCommentMarker());
  }

  @Test(timeout = 4000)
  public void test087()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('v');
      CSVFormat cSVFormat1 = cSVFormat0.withTrailingDelimiter();
      Character character0 = Character.valueOf('c');
      CSVFormat cSVFormat2 = cSVFormat1.withEscape(character0);
      assertFalse(cSVFormat2.getIgnoreHeaderCase());
      assertFalse(cSVFormat1.equals((Object)cSVFormat0));
      assertFalse(cSVFormat2.getSkipHeaderRecord());
      assertFalse(cSVFormat2.getAllowMissingColumnNames());
      assertEquals('v', cSVFormat2.getDelimiter());
      assertFalse(cSVFormat2.getAutoFlush());
      assertFalse(cSVFormat2.getIgnoreEmptyLines());
      assertFalse(cSVFormat2.getTrim());
      assertFalse(cSVFormat2.getIgnoreSurroundingSpaces());
  }

  @Test(timeout = 4000)
  public void test088()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      Character character0 = cSVFormat0.getQuoteCharacter();
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreHeaderCase();
      CSVFormat cSVFormat2 = cSVFormat1.withEscape(character0);
      assertTrue(cSVFormat2.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test089()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      Character character0 = Character.valueOf('[');
      CSVFormat cSVFormat1 = cSVFormat0.withEscape(character0);
      assertFalse(cSVFormat1.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test090()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      Character character0 = new Character('1');
      CSVFormat cSVFormat1 = cSVFormat0.withEscape(character0);
      assertFalse(cSVFormat1.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test091()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('3');
      CSVFormat cSVFormat1 = cSVFormat0.withEscape('.');
      assertFalse(cSVFormat1.getAllowMissingColumnNames());
      assertFalse(cSVFormat1.getIgnoreEmptyLines());
      assertFalse(cSVFormat1.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat1.getTrim());
      assertEquals('.', (char)cSVFormat1.getEscapeCharacter());
      assertFalse(cSVFormat1.getAutoFlush());
      assertFalse(cSVFormat1.getTrailingDelimiter());
      assertEquals("3", cSVFormat1.getDelimiterString());
      assertFalse(cSVFormat1.getSkipHeaderRecord());
      assertFalse(cSVFormat1.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test092()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withEscape('9');
      assertEquals('9', (char)cSVFormat1.getEscapeCharacter());
  }

  @Test(timeout = 4000)
  public void test093()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat cSVFormat1 = cSVFormat0.withFirstRecordAsHeader();
      CSVFormat cSVFormat2 = cSVFormat1.withEscape('{');
      assertTrue(cSVFormat2.getSkipHeaderRecord());
      assertEquals('{', (char)cSVFormat2.getEscapeCharacter());
  }

  @Test(timeout = 4000)
  public void test094()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('v');
      CSVFormat cSVFormat1 = cSVFormat0.TDF.withEscape('c');
      assertFalse(cSVFormat0.getIgnoreHeaderCase());
      assertFalse(cSVFormat0.getIgnoreEmptyLines());
      assertFalse(cSVFormat0.getAllowMissingColumnNames());
      assertEquals('c', (char)cSVFormat1.getEscapeCharacter());
      assertFalse(cSVFormat0.getSkipHeaderRecord());
      assertFalse(cSVFormat0.getTrailingDelimiter());
      assertFalse(cSVFormat0.getIgnoreSurroundingSpaces());
      assertEquals('v', cSVFormat0.getDelimiter());
      assertFalse(cSVFormat0.getTrim());
  }

  @Test(timeout = 4000)
  public void test095()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withDelimiter('X');
      assertEquals("X", cSVFormat1.getDelimiterString());
  }

  @Test(timeout = 4000)
  public void test096()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('v');
      CSVFormat cSVFormat1 = cSVFormat0.withTrailingDelimiter();
      CSVFormat cSVFormat2 = cSVFormat1.withDelimiter('v');
      assertFalse(cSVFormat2.equals((Object)cSVFormat0));
      assertFalse(cSVFormat2.getIgnoreEmptyLines());
      assertFalse(cSVFormat2.getIgnoreHeaderCase());
      assertFalse(cSVFormat2.getAllowMissingColumnNames());
      assertFalse(cSVFormat2.getSkipHeaderRecord());
      assertTrue(cSVFormat2.equals((Object)cSVFormat1));
      assertFalse(cSVFormat2.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat2.getAutoFlush());
      assertFalse(cSVFormat2.isNullStringSet());
      assertFalse(cSVFormat2.getTrim());
  }

  @Test(timeout = 4000)
  public void test097()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      CSVFormat cSVFormat1 = cSVFormat0.withSkipHeaderRecord();
      CSVFormat cSVFormat2 = cSVFormat1.withHeader((String[]) null);
      CSVFormat cSVFormat3 = cSVFormat2.withDelimiter('*');
      assertTrue(cSVFormat3.getSkipHeaderRecord());
      assertEquals('*', cSVFormat3.getDelimiter());
  }

  @Test(timeout = 4000)
  public void test098()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('U');
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreHeaderCase();
      CSVFormat cSVFormat2 = cSVFormat1.withDelimiter('d');
      assertFalse(cSVFormat1.equals((Object)cSVFormat0));
      assertFalse(cSVFormat2.getAllowMissingColumnNames());
      assertFalse(cSVFormat2.getSkipHeaderRecord());
      assertEquals("U", cSVFormat1.getDelimiterString());
      assertFalse(cSVFormat2.getTrailingDelimiter());
      assertFalse(cSVFormat2.getAutoFlush());
      assertFalse(cSVFormat2.getIgnoreEmptyLines());
      assertFalse(cSVFormat2.getTrim());
      assertFalse(cSVFormat2.isNullStringSet());
      assertEquals('d', cSVFormat2.getDelimiter());
      assertFalse(cSVFormat2.getIgnoreSurroundingSpaces());
  }

  @Test(timeout = 4000)
  public void test099()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('v');
      CSVFormat cSVFormat1 = cSVFormat0.withAutoFlush(true);
      CSVFormat cSVFormat2 = cSVFormat1.withDelimiter('v');
      assertFalse(cSVFormat2.equals((Object)cSVFormat0));
      assertFalse(cSVFormat2.isNullStringSet());
      assertFalse(cSVFormat2.getIgnoreHeaderCase());
      assertFalse(cSVFormat2.getSkipHeaderRecord());
      assertFalse(cSVFormat2.getAllowMissingColumnNames());
      assertFalse(cSVFormat2.getTrailingDelimiter());
      assertFalse(cSVFormat2.getIgnoreEmptyLines());
      assertFalse(cSVFormat2.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat2.getTrim());
      assertTrue(cSVFormat2.equals((Object)cSVFormat1));
  }

  @Test(timeout = 4000)
  public void test100()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVFormat cSVFormat1 = cSVFormat0.withAllowMissingColumnNames(true);
      CSVFormat cSVFormat2 = cSVFormat1.withDelimiter('`');
      CSVFormat cSVFormat3 = cSVFormat2.withSkipHeaderRecord();
      assertTrue(cSVFormat3.getAllowMissingColumnNames());
      assertTrue(cSVFormat3.getSkipHeaderRecord());
      assertEquals('`', cSVFormat3.getDelimiter());
  }

  @Test(timeout = 4000)
  public void test101()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('v');
      CSVFormat cSVFormat1 = cSVFormat0.withTrailingDelimiter();
      CSVFormat cSVFormat2 = cSVFormat1.withNullString("lIO[:X7jt<4");
      Character character0 = Character.valueOf('c');
      CSVFormat cSVFormat3 = cSVFormat2.withCommentMarker(character0);
      assertFalse(cSVFormat1.equals((Object)cSVFormat0));
      assertFalse(cSVFormat1.isNullStringSet());
      assertFalse(cSVFormat3.getSkipHeaderRecord());
      assertFalse(cSVFormat3.getAllowMissingColumnNames());
      assertEquals('v', cSVFormat3.getDelimiter());
      assertFalse(cSVFormat3.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat3.getTrim());
      assertFalse(cSVFormat3.getIgnoreHeaderCase());
      assertFalse(cSVFormat3.getIgnoreEmptyLines());
  }

  @Test(timeout = 4000)
  public void test102()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreHeaderCase();
      Character character0 = new Character('x');
      CSVFormat cSVFormat2 = cSVFormat1.withCommentMarker(character0);
      assertTrue(cSVFormat2.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test103()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      Character character0 = Character.valueOf('c');
      CSVFormat cSVFormat1 = cSVFormat0.withAllowMissingColumnNames();
      CSVFormat cSVFormat2 = cSVFormat1.withCommentMarker(character0);
      assertTrue(cSVFormat2.getAllowMissingColumnNames());
  }

  @Test(timeout = 4000)
  public void test104()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      CSVFormat cSVFormat1 = cSVFormat0.withTrailingDelimiter();
      CSVFormat cSVFormat2 = cSVFormat1.withCommentMarker('C');
      assertTrue(cSVFormat2.getTrailingDelimiter());
      assertEquals('C', (char)cSVFormat2.getCommentMarker());
  }

  @Test(timeout = 4000)
  public void test105()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat cSVFormat1 = cSVFormat0.TDF.withAutoFlush(true);
      assertTrue(cSVFormat1.getAutoFlush());
  }

  @Test(timeout = 4000)
  public void test106()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withAllowMissingColumnNames();
      CSVFormat cSVFormat2 = cSVFormat1.withAutoFlush(false);
      assertTrue(cSVFormat2.getAllowMissingColumnNames());
      assertFalse(cSVFormat2.getAutoFlush());
  }

  @Test(timeout = 4000)
  public void test107()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.withAllowMissingColumnNames(false);
      assertFalse(cSVFormat1.getAllowMissingColumnNames());
  }

  @Test(timeout = 4000)
  public void test108()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      CSVFormat cSVFormat1 = cSVFormat0.withAllowMissingColumnNames(true);
      assertTrue(cSVFormat1.getAllowMissingColumnNames());
  }

  @Test(timeout = 4000)
  public void test109()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('v');
      QuoteMode quoteMode0 = QuoteMode.NONE;
      CSVFormat cSVFormat1 = cSVFormat0.ORACLE.withQuoteMode(quoteMode0);
      CSVFormat cSVFormat2 = cSVFormat1.withAllowMissingColumnNames();
      CSVFormat cSVFormat3 = cSVFormat2.withQuote((Character) 'v');
      assertFalse(cSVFormat0.getTrailingDelimiter());
      assertFalse(cSVFormat0.getAllowMissingColumnNames());
      assertFalse(cSVFormat0.getIgnoreHeaderCase());
      assertFalse(cSVFormat0.getIgnoreEmptyLines());
      assertTrue(cSVFormat3.getAllowMissingColumnNames());
      assertFalse(cSVFormat0.getSkipHeaderRecord());
      assertFalse(cSVFormat0.getTrim());
      assertFalse(cSVFormat0.isNullStringSet());
      assertFalse(cSVFormat0.getAutoFlush());
      assertFalse(cSVFormat0.getIgnoreSurroundingSpaces());
  }

  @Test(timeout = 4000)
  public void test110()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat cSVFormat1 = cSVFormat0.withFirstRecordAsHeader();
      CSVFormat cSVFormat2 = cSVFormat1.withQuote('{');
      CSVFormat cSVFormat3 = cSVFormat2.withAllowMissingColumnNames();
      assertTrue(cSVFormat3.getAllowMissingColumnNames());
      assertTrue(cSVFormat3.getSkipHeaderRecord());
      assertEquals('{', (char)cSVFormat3.getQuoteCharacter());
  }

  @Test(timeout = 4000)
  public void test111()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVFormat cSVFormat1 = cSVFormat0.TDF.withIgnoreEmptyLines(false);
      CSVFormat cSVFormat2 = cSVFormat1.withAllowMissingColumnNames();
      assertFalse(cSVFormat2.getIgnoreEmptyLines());
      assertTrue(cSVFormat2.getAllowMissingColumnNames());
  }

  @Test(timeout = 4000)
  public void test112()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreEmptyLines();
      CSVFormat cSVFormat2 = cSVFormat1.withAllowMissingColumnNames();
      assertTrue(cSVFormat2.getIgnoreEmptyLines());
      assertTrue(cSVFormat2.getAllowMissingColumnNames());
  }

  @Test(timeout = 4000)
  public void test113()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      Character character0 = Character.valueOf('|');
      CSVFormat cSVFormat1 = cSVFormat0.withCommentMarker(character0);
      Class<CSVFormat.Predefined> class0 = CSVFormat.Predefined.class;
      CSVFormat cSVFormat2 = cSVFormat1.withHeader(class0);
      CSVFormat cSVFormat3 = cSVFormat2.withAllowDuplicateHeaderNames(true);
      assertFalse(cSVFormat3.equals((Object)cSVFormat1));
      assertTrue(cSVFormat3.getAllowDuplicateHeaderNames());
  }

  @Test(timeout = 4000)
  public void test114()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withAllowDuplicateHeaderNames(true);
      assertEquals(DuplicateHeaderMode.ALLOW_ALL, cSVFormat1.getDuplicateHeaderMode());
  }

  @Test(timeout = 4000)
  public void test115()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('v');
      CSVFormat cSVFormat1 = cSVFormat0.withTrailingDelimiter();
      CSVFormat cSVFormat2 = cSVFormat1.withAllowDuplicateHeaderNames(true);
      assertFalse(cSVFormat2.getSkipHeaderRecord());
      assertEquals(DuplicateHeaderMode.ALLOW_ALL, cSVFormat2.getDuplicateHeaderMode());
      assertFalse(cSVFormat2.getAutoFlush());
      assertFalse(cSVFormat2.getTrim());
      assertFalse(cSVFormat2.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat2.isNullStringSet());
      assertFalse(cSVFormat2.getIgnoreEmptyLines());
      assertFalse(cSVFormat2.equals((Object)cSVFormat0));
      assertEquals("v", cSVFormat2.getDelimiterString());
      assertFalse(cSVFormat2.getIgnoreHeaderCase());
      assertFalse(cSVFormat2.getAllowMissingColumnNames());
  }

  @Test(timeout = 4000)
  public void test116()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      CSVFormat cSVFormat1 = cSVFormat0.withAllowDuplicateHeaderNames(true);
      assertTrue(cSVFormat1.getAllowDuplicateHeaderNames());
  }

  @Test(timeout = 4000)
  public void test117()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('v');
      CSVFormat cSVFormat1 = cSVFormat0.withAllowDuplicateHeaderNames();
      assertFalse(cSVFormat1.getTrim());
      assertFalse(cSVFormat1.getAllowMissingColumnNames());
      assertFalse(cSVFormat1.getIgnoreHeaderCase());
      assertFalse(cSVFormat1.getIgnoreEmptyLines());
      assertFalse(cSVFormat1.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat1.isNullStringSet());
      assertFalse(cSVFormat1.getSkipHeaderRecord());
      assertEquals("v", cSVFormat1.getDelimiterString());
      assertFalse(cSVFormat1.getAutoFlush());
      assertFalse(cSVFormat1.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test118()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      CSVFormat cSVFormat1 = cSVFormat0.withSkipHeaderRecord();
      CSVFormat cSVFormat2 = cSVFormat1.withAllowDuplicateHeaderNames();
      assertTrue(cSVFormat2.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test119()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MYSQL;
      CSVFormat cSVFormat1 = cSVFormat0.withAllowMissingColumnNames();
      CSVFormat cSVFormat2 = cSVFormat1.withAllowDuplicateHeaderNames();
      assertTrue(cSVFormat2.getAllowMissingColumnNames());
  }

  @Test(timeout = 4000)
  public void test120()  throws Throwable  {
      String[] stringArray0 = CSVFormat.toStringArray((Object[]) null);
      assertNull(stringArray0);
  }

  @Test(timeout = 4000)
  public void test121()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      File file0 = MockFile.createTempFile("E1AeJL%JJ,a[njt9(;9", "E1AeJL%JJ,a[njt9(;9");
      MockPrintWriter mockPrintWriter0 = new MockPrintWriter(file0);
      CSVPrinter cSVPrinter0 = cSVFormat0.TDF.print((Appendable) mockPrintWriter0);
      assertNotNull(cSVPrinter0);
  }

  @Test(timeout = 4000)
  public void test122()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      boolean boolean0 = cSVFormat0.getTrim();
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test123()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.valueOf("MongoDBTsv");
      QuoteMode quoteMode0 = cSVFormat0.getQuoteMode();
      assertEquals(QuoteMode.MINIMAL, quoteMode0);
  }

  @Test(timeout = 4000)
  public void test124()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MYSQL;
      Character character0 = cSVFormat0.getQuoteCharacter();
      assertNull(character0);
  }

  @Test(timeout = 4000)
  public void test125()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('v');
      String string0 = cSVFormat0.getNullString();
      assertFalse(cSVFormat0.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat0.getAutoFlush());
      assertFalse(cSVFormat0.getAllowMissingColumnNames());
      assertEquals("v", cSVFormat0.getDelimiterString());
      assertFalse(cSVFormat0.getTrailingDelimiter());
      assertFalse(cSVFormat0.getSkipHeaderRecord());
      assertFalse(cSVFormat0.getIgnoreHeaderCase());
      assertFalse(cSVFormat0.getTrim());
      assertNull(string0);
  }

  @Test(timeout = 4000)
  public void test126()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      String string0 = cSVFormat0.getNullString();
      assertEquals("", string0);
  }

  @Test(timeout = 4000)
  public void test127()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      boolean boolean0 = cSVFormat0.getIgnoreEmptyLines();
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test128()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      Character character0 = cSVFormat0.getEscapeCharacter();
      assertEquals('\\', (char)character0);
  }

  @Test(timeout = 4000)
  public void test129()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('v');
      String[] stringArray0 = new String[0];
      String string0 = cSVFormat0.format(stringArray0);
      assertEquals("", string0);
      assertFalse(cSVFormat0.getAutoFlush());
      assertFalse(cSVFormat0.getIgnoreEmptyLines());
      assertFalse(cSVFormat0.getAllowMissingColumnNames());
      assertFalse(cSVFormat0.getIgnoreHeaderCase());
      assertFalse(cSVFormat0.getSkipHeaderRecord());
      assertFalse(cSVFormat0.getTrim());
      assertFalse(cSVFormat0.isNullStringSet());
      assertFalse(cSVFormat0.getIgnoreSurroundingSpaces());
      assertEquals('v', cSVFormat0.getDelimiter());
  }

  @Test(timeout = 4000)
  public void test130()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat cSVFormat1 = cSVFormat0.withSkipHeaderRecord();
      CSVFormat cSVFormat2 = cSVFormat1.copy();
      assertTrue(cSVFormat2.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test131()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      CSVFormat cSVFormat1 = cSVFormat0.copy();
      assertEquals('\t', cSVFormat1.getDelimiter());
  }

  @Test(timeout = 4000)
  public void test132()  throws Throwable  {
      CSVFormat[] cSVFormatArray0 = CSVFormat.clone((CSVFormat[]) null);
      assertNull(cSVFormatArray0);
  }

  @Test(timeout = 4000)
  public void test133()  throws Throwable  {
      CSVFormat[] cSVFormatArray0 = new CSVFormat[8];
      CSVFormat[] cSVFormatArray1 = CSVFormat.clone(cSVFormatArray0);
      assertNotSame(cSVFormatArray0, cSVFormatArray1);
  }

  @Test(timeout = 4000)
  public void test134()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('3');
      // Undeclared exception!
      try { 
        cSVFormat0.withEscape('3');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The escape character and the delimiter cannot be the same ('3')
         //
         verifyException("org.apache.commons.csv.CSVFormat", e);
      }
  }

  @Test(timeout = 4000)
  public void test135()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      CharBuffer charBuffer0 = CharBuffer.allocate(10);
      // Undeclared exception!
      try { 
        cSVFormat0.printRecord(charBuffer0, (Object[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.csv.CSVFormat", e);
      }
  }

  @Test(timeout = 4000)
  public void test136()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      StringWriter stringWriter0 = new StringWriter();
      StringBuffer stringBuffer0 = stringWriter0.getBuffer();
      CharBuffer charBuffer0 = CharBuffer.wrap((CharSequence) stringBuffer0);
      // Undeclared exception!
      try { 
        cSVFormat0.DEFAULT.print((Object) cSVFormat0, (Appendable) charBuffer0, true);
        fail("Expecting exception: ReadOnlyBufferException");
      
      } catch(ReadOnlyBufferException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("java.nio.CharBuffer", e);
      }
  }

  @Test(timeout = 4000)
  public void test137()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      CSVFormat.Predefined cSVFormat_Predefined0 = CSVFormat.Predefined.RFC4180;
      PipedWriter pipedWriter0 = new PipedWriter();
      try { 
        cSVFormat0.print((Object) cSVFormat_Predefined0, (Appendable) pipedWriter0, true);
        fail("Expecting exception: IOException");
      
      } catch(IOException e) {
         //
         // Pipe not connected
         //
         verifyException("java.io.PipedWriter", e);
      }
  }

  @Test(timeout = 4000)
  public void test138()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      // Undeclared exception!
      try { 
        cSVFormat0.MYSQL.print((File) null, (Charset) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("java.io.File", e);
      }
  }

  @Test(timeout = 4000)
  public void test139()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      CSVFormat[] cSVFormatArray0 = new CSVFormat[8];
      cSVFormatArray0[1] = cSVFormat0;
      StringWriter stringWriter0 = new StringWriter();
      cSVFormatArray0[1].print((Object) cSVFormatArray0[4], (Appendable) stringWriter0, true);
      assertEquals("", stringWriter0.toString());
  }

  @Test(timeout = 4000)
  public void test140()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      boolean boolean0 = cSVFormat0.isQuoteCharacterSet();
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test141()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      boolean boolean0 = cSVFormat0.isQuoteCharacterSet();
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test142()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      boolean boolean0 = cSVFormat0.isNullStringSet();
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test143()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      String[] stringArray0 = cSVFormat0.getHeaderComments();
      assertNull(stringArray0);
  }

  @Test(timeout = 4000)
  public void test144()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      String[] stringArray0 = cSVFormat0.getHeader();
      assertNull(stringArray0);
  }

  @Test(timeout = 4000)
  public void test145()  throws Throwable  {
      RowSetMetaDataImpl rowSetMetaDataImpl0 = new RowSetMetaDataImpl();
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withHeader((ResultSetMetaData) rowSetMetaDataImpl0);
      String[] stringArray0 = cSVFormat1.getHeader();
      assertEquals(0, stringArray0.length);
  }

  @Test(timeout = 4000)
  public void test146()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      Character character0 = Character.valueOf('x');
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setQuote(character0);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test147()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      RowSetMetaDataImpl rowSetMetaDataImpl0 = new RowSetMetaDataImpl();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setHeader((ResultSetMetaData) rowSetMetaDataImpl0);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test148()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setHeader((ResultSetMetaData) null);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test149()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      ResultSet resultSet0 = mock(ResultSet.class, new ViolatedAssumptionAnswer());
      doReturn((ResultSetMetaData) null).when(resultSet0).getMetaData();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setHeader(resultSet0);
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test150()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      Class<CSVFormat.Predefined> class0 = CSVFormat.Predefined.class;
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setHeader(class0);
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test151()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('+');
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      Character character0 = new Character('F');
      cSVFormat_Builder0.setEscape(character0);
      assertFalse(cSVFormat0.getIgnoreHeaderCase());
      assertFalse(cSVFormat0.getTrim());
      assertFalse(cSVFormat0.isNullStringSet());
      assertFalse(cSVFormat0.getAllowMissingColumnNames());
      assertFalse(cSVFormat0.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat0.getSkipHeaderRecord());
      assertFalse(cSVFormat0.getAutoFlush());
      assertFalse(cSVFormat0.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test152()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('+');
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      cSVFormat_Builder0.setDelimiter("MongoDBTsv");
      assertFalse(cSVFormat0.getSkipHeaderRecord());
      assertFalse(cSVFormat0.getTrailingDelimiter());
      assertFalse(cSVFormat0.getIgnoreHeaderCase());
      assertFalse(cSVFormat0.getAllowMissingColumnNames());
      assertFalse(cSVFormat0.getIgnoreEmptyLines());
      assertEquals("+", cSVFormat0.getDelimiterString());
      assertFalse(cSVFormat0.getAutoFlush());
      assertFalse(cSVFormat0.getTrim());
      assertFalse(cSVFormat0.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat0.isNullStringSet());
  }

  @Test(timeout = 4000)
  public void test153()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat.Builder cSVFormat_Builder0 = cSVFormat0.builder();
      Character character0 = Character.valueOf('P');
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setCommentMarker(character0);
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test154()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setNullString("");
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test155()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setSkipHeaderRecord(false);
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test156()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setRecordSeparator('/');
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test157()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat(';');
      cSVFormat0.getQuoteMode();
      assertFalse(cSVFormat0.getIgnoreHeaderCase());
      assertFalse(cSVFormat0.getTrim());
      assertFalse(cSVFormat0.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat0.isNullStringSet());
      assertFalse(cSVFormat0.getTrailingDelimiter());
      assertFalse(cSVFormat0.getAutoFlush());
      assertEquals(';', cSVFormat0.getDelimiter());
      assertFalse(cSVFormat0.getSkipHeaderRecord());
      assertFalse(cSVFormat0.getIgnoreEmptyLines());
      assertFalse(cSVFormat0.getAllowMissingColumnNames());
  }

  @Test(timeout = 4000)
  public void test158()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setTrim(false);
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test159()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      String[] stringArray0 = new String[0];
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setHeader(stringArray0);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test160()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      String string0 = cSVFormat0.getDelimiterString();
      assertEquals(",", string0);
  }

  @Test(timeout = 4000)
  public void test161()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setAllowMissingColumnNames(false);
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test162()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat.Builder cSVFormat_Builder0 = cSVFormat0.builder();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setAutoFlush(false);
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test163()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      CSVFormat cSVFormat1 = cSVFormat_Builder0.build();
      assertEquals("\\N", cSVFormat1.getNullString());
  }

  @Test(timeout = 4000)
  public void test164()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setEscape('/');
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test165()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      Character character0 = cSVFormat0.getCommentMarker();
      assertNull(character0);
  }

  @Test(timeout = 4000)
  public void test166()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setQuote('x');
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test167()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setIgnoreHeaderCase(false);
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test168()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat.Builder cSVFormat_Builder0 = cSVFormat0.builder();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setDelimiter('{');
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test169()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setIgnoreEmptyLines(false);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test170()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setIgnoreSurroundingSpaces(false);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test171()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setTrailingDelimiter(false);
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test172()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('3');
      boolean boolean0 = cSVFormat0.getTrailingDelimiter();
      assertFalse(boolean0);
      assertFalse(cSVFormat0.getAutoFlush());
      assertFalse(cSVFormat0.getTrim());
      assertFalse(cSVFormat0.getIgnoreHeaderCase());
      assertFalse(cSVFormat0.isNullStringSet());
      assertFalse(cSVFormat0.getIgnoreEmptyLines());
      assertFalse(cSVFormat0.getIgnoreSurroundingSpaces());
      assertEquals("3", cSVFormat0.getDelimiterString());
      assertFalse(cSVFormat0.getSkipHeaderRecord());
      assertFalse(cSVFormat0.getAllowMissingColumnNames());
  }

  @Test(timeout = 4000)
  public void test173()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      boolean boolean0 = cSVFormat0.getAutoFlush();
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test174()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat.Builder cSVFormat_Builder0 = cSVFormat0.builder();
      DuplicateHeaderMode duplicateHeaderMode0 = DuplicateHeaderMode.ALLOW_EMPTY;
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setDuplicateHeaderMode(duplicateHeaderMode0);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test175()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat.Builder cSVFormat_Builder0 = cSVFormat0.builder();
      QuoteMode quoteMode0 = QuoteMode.NONE;
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setQuoteMode(quoteMode0);
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test176()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      boolean boolean0 = cSVFormat0.getTrim();
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test177()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setRecordSeparator("");
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test178()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('3');
      boolean boolean0 = cSVFormat0.getIgnoreSurroundingSpaces();
      assertFalse(boolean0);
      assertFalse(cSVFormat0.getSkipHeaderRecord());
      assertFalse(cSVFormat0.getTrim());
      assertFalse(cSVFormat0.getTrailingDelimiter());
      assertFalse(cSVFormat0.isNullStringSet());
      assertFalse(cSVFormat0.getAutoFlush());
      assertEquals("3", cSVFormat0.getDelimiterString());
      assertFalse(cSVFormat0.getAllowMissingColumnNames());
      assertFalse(cSVFormat0.getIgnoreHeaderCase());
      assertFalse(cSVFormat0.getIgnoreEmptyLines());
  }

  @Test(timeout = 4000)
  public void test179()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      boolean boolean0 = cSVFormat0.getIgnoreEmptyLines();
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test180()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('3');
      cSVFormat0.getEscapeCharacter();
      assertFalse(cSVFormat0.getSkipHeaderRecord());
      assertFalse(cSVFormat0.getAutoFlush());
      assertFalse(cSVFormat0.getTrim());
      assertFalse(cSVFormat0.getIgnoreSurroundingSpaces());
      assertEquals("3", cSVFormat0.getDelimiterString());
      assertFalse(cSVFormat0.getIgnoreHeaderCase());
      assertFalse(cSVFormat0.getIgnoreEmptyLines());
      assertFalse(cSVFormat0.getTrailingDelimiter());
      assertFalse(cSVFormat0.getAllowMissingColumnNames());
  }

  @Test(timeout = 4000)
  public void test181()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      QuoteMode quoteMode0 = QuoteMode.NONE;
      // Undeclared exception!
      try { 
        cSVFormat0.withQuoteMode(quoteMode0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // No quotes mode set but no escape character is set
         //
         verifyException("org.apache.commons.csv.CSVFormat", e);
      }
  }

  @Test(timeout = 4000)
  public void test182()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      Character character0 = Character.valueOf('j');
      CSVFormat cSVFormat1 = cSVFormat0.withCommentMarker(character0);
      // Undeclared exception!
      try { 
        cSVFormat1.withEscape(character0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The comment start and the escape character cannot be the same ('j')
         //
         verifyException("org.apache.commons.csv.CSVFormat", e);
      }
  }

  @Test(timeout = 4000)
  public void test183()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      Character character0 = cSVFormat0.getQuoteCharacter();
      // Undeclared exception!
      try { 
        cSVFormat0.withCommentMarker(character0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The comment start character and the quoteChar cannot be the same ('\"')
         //
         verifyException("org.apache.commons.csv.CSVFormat", e);
      }
  }

  @Test(timeout = 4000)
  public void test184()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('v');
      Character character0 = new Character('v');
      // Undeclared exception!
      try { 
        cSVFormat0.withCommentMarker(character0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The comment start character and the delimiter cannot be the same ('v')
         //
         verifyException("org.apache.commons.csv.CSVFormat", e);
      }
  }

  @Test(timeout = 4000)
  public void test185()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      String string0 = cSVFormat0.trim("");
      assertEquals("", string0);
  }

  @Test(timeout = 4000)
  public void test186()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat cSVFormat1 = cSVFormat0.withFirstRecordAsHeader();
      assertTrue(cSVFormat1.getSkipHeaderRecord());
      
      Object[] objectArray0 = new Object[5];
      objectArray0[2] = (Object) cSVFormat1;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      cSVFormat_Builder0.setHeaderComments(objectArray0);
      assertFalse(cSVFormat0.isCommentMarkerSet());
      assertTrue(cSVFormat0.isQuoteCharacterSet());
      assertFalse(cSVFormat0.isNullStringSet());
  }

  @Test(timeout = 4000)
  public void test187()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreHeaderCase();
      Object[] objectArray0 = new Object[9];
      objectArray0[1] = (Object) cSVFormat1;
      cSVFormat_Builder0.setHeaderComments(objectArray0);
      assertTrue(cSVFormat0.isQuoteCharacterSet());
      assertTrue(cSVFormat0.isNullStringSet());
      assertFalse(cSVFormat0.equals((Object)cSVFormat1));
      assertFalse(cSVFormat0.isCommentMarkerSet());
  }

  @Test(timeout = 4000)
  public void test188()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat(';');
      CSVFormat cSVFormat1 = cSVFormat0.INFORMIX_UNLOAD.withAllowDuplicateHeaderNames(true);
      String string0 = cSVFormat1.toString();
      assertEquals("Delimiter=<|> Escape=<\\> QuoteChar=<\"> RecordSeparator=<\n> EmptyLines:ignored SkipHeaderRecord:false", string0);
      assertFalse(cSVFormat0.getAllowMissingColumnNames());
      assertEquals(';', cSVFormat0.getDelimiter());
      assertFalse(cSVFormat0.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat0.getTrim());
      assertFalse(cSVFormat0.isNullStringSet());
      assertFalse(cSVFormat0.getTrailingDelimiter());
      assertFalse(cSVFormat0.getSkipHeaderRecord());
      assertEquals(DuplicateHeaderMode.ALLOW_ALL, cSVFormat1.getDuplicateHeaderMode());
      assertFalse(cSVFormat0.getIgnoreEmptyLines());
      assertFalse(cSVFormat0.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test189()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.MONGODB_TSV.withEscape('S');
      StringWriter stringWriter0 = new StringWriter(71);
      Object[] objectArray0 = new Object[7];
      objectArray0[0] = (Object) cSVFormat0;
      cSVFormat1.printRecord(stringWriter0, objectArray0);
      assertEquals('S', (char)cSVFormat1.getEscapeCharacter());
      assertTrue(cSVFormat1.isEscapeCharacterSet());
      assertTrue(cSVFormat1.isQuoteCharacterSet());
  }

  @Test(timeout = 4000)
  public void test190()  throws Throwable  {
      PipedOutputStream pipedOutputStream0 = new PipedOutputStream();
      MockPrintStream mockPrintStream0 = new MockPrintStream(pipedOutputStream0, false);
      CharBuffer charBuffer0 = CharBuffer.allocate(10);
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      Object[] objectArray0 = new Object[7];
      objectArray0[0] = (Object) charBuffer0;
      cSVFormat0.printRecord(mockPrintStream0, objectArray0);
      assertTrue(cSVFormat0.isQuoteCharacterSet());
  }

  @Test(timeout = 4000)
  public void test191()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('v');
      QuoteMode quoteMode0 = QuoteMode.NONE;
      CSVFormat cSVFormat1 = cSVFormat0.ORACLE.withQuoteMode(quoteMode0);
      CharArrayWriter charArrayWriter0 = new CharArrayWriter();
      cSVFormat1.print((Object) cSVFormat1, (Appendable) charArrayWriter0, true);
      assertEquals(43, charArrayWriter0.size());
      assertFalse(cSVFormat0.getIgnoreEmptyLines());
  }

  @Test(timeout = 4000)
  public void test192()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      QuoteMode quoteMode0 = QuoteMode.ALL;
      CSVFormat cSVFormat1 = cSVFormat0.withQuoteMode(quoteMode0);
      StringWriter stringWriter0 = new StringWriter();
      cSVFormat1.print((Object) cSVFormat0, (Appendable) stringWriter0, true);
      assertTrue(cSVFormat1.isQuoteCharacterSet());
  }

  @Test(timeout = 4000)
  public void test193()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('v');
      CSVFormat cSVFormat1 = cSVFormat0.withTrailingDelimiter();
      CSVFormat cSVFormat2 = cSVFormat1.withNullString("lIO[:X7jt<4");
      Object[] objectArray0 = new Object[7];
      String string0 = cSVFormat2.format(objectArray0);
      assertFalse(cSVFormat2.getSkipHeaderRecord());
      assertEquals("lIO[:X7jt<4vlIO[:X7jt<4vlIO[:X7jt<4vlIO[:X7jt<4vlIO[:X7jt<4vlIO[:X7jt<4vlIO[:X7jt<4v", string0);
      assertFalse(cSVFormat2.getIgnoreHeaderCase());
      assertFalse(cSVFormat1.isNullStringSet());
      assertFalse(cSVFormat2.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat2.getAllowMissingColumnNames());
      assertFalse(cSVFormat2.getAutoFlush());
      assertFalse(cSVFormat0.getTrailingDelimiter());
      assertFalse(cSVFormat2.getTrim());
  }

  @Test(timeout = 4000)
  public void test194()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      assertFalse(cSVFormat0.isQuoteCharacterSet());
      
      StringWriter stringWriter0 = new StringWriter();
      cSVFormat0.print((Object) cSVFormat0, (Appendable) stringWriter0, true);
      assertTrue(cSVFormat0.isEscapeCharacterSet());
  }

  @Test(timeout = 4000)
  public void test195()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('3');
      StringWriter stringWriter0 = new StringWriter();
      cSVFormat0.print((Object) cSVFormat0, (Appendable) stringWriter0, false);
      assertFalse(cSVFormat0.getTrailingDelimiter());
      assertFalse(cSVFormat0.getSkipHeaderRecord());
      assertFalse(cSVFormat0.getAllowMissingColumnNames());
      assertFalse(cSVFormat0.getIgnoreSurroundingSpaces());
      assertEquals("3", cSVFormat0.getDelimiterString());
      assertFalse(cSVFormat0.getIgnoreEmptyLines());
      assertFalse(cSVFormat0.getIgnoreHeaderCase());
      assertFalse(cSVFormat0.getAutoFlush());
      assertFalse(cSVFormat0.getTrim());
  }

  @Test(timeout = 4000)
  public void test196()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      assertFalse(cSVFormat0.isEscapeCharacterSet());
      
      PipedOutputStream pipedOutputStream0 = new PipedOutputStream();
      MockPrintStream mockPrintStream0 = new MockPrintStream(pipedOutputStream0, false);
      CharBuffer charBuffer0 = CharBuffer.allocate(10);
      Object[] objectArray0 = new Object[3];
      objectArray0[1] = (Object) charBuffer0;
      cSVFormat0.printRecord(mockPrintStream0, objectArray0);
      assertTrue(cSVFormat0.isQuoteCharacterSet());
  }

  @Test(timeout = 4000)
  public void test197()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      String string0 = cSVFormat0.toString();
      assertEquals("Delimiter=<,> QuoteChar=<\"> QuoteMode=<ALL_NON_NULL> NullString=<> RecordSeparator=<\n> SkipHeaderRecord:false", string0);
  }

  @Test(timeout = 4000)
  public void test198()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat(';');
      String string0 = cSVFormat0.toString();
      assertEquals("Delimiter=<;> SkipHeaderRecord:false", string0);
      assertFalse(cSVFormat0.getTrim());
      assertFalse(cSVFormat0.getAllowMissingColumnNames());
      assertFalse(cSVFormat0.getTrailingDelimiter());
      assertFalse(cSVFormat0.getAutoFlush());
  }

  @Test(timeout = 4000)
  public void test199()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.INFORMIX_UNLOAD_CSV.withIgnoreEmptyLines(false);
      boolean boolean0 = cSVFormat1.equals(cSVFormat0);
      assertFalse(boolean0);
      assertFalse(cSVFormat0.equals((Object)cSVFormat1));
  }

  @Test(timeout = 4000)
  public void test200()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('v');
      CSVFormat cSVFormat1 = cSVFormat0.withNullString("lIO[:X7jt<4");
      cSVFormat1.equals(cSVFormat0);
      assertFalse(cSVFormat1.getIgnoreEmptyLines());
      assertFalse(cSVFormat1.getAutoFlush());
      assertFalse(cSVFormat1.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat1.getTrim());
      assertFalse(cSVFormat1.getIgnoreHeaderCase());
      assertFalse(cSVFormat1.getSkipHeaderRecord());
      assertFalse(cSVFormat1.getAllowMissingColumnNames());
  }

  @Test(timeout = 4000)
  public void test201()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      Character character0 = Character.valueOf('c');
      CSVFormat cSVFormat1 = cSVFormat0.withCommentMarker(character0);
      boolean boolean0 = cSVFormat1.equals(cSVFormat0);
      assertFalse(cSVFormat0.equals((Object)cSVFormat1));
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test202()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat.Predefined cSVFormat_Predefined0 = CSVFormat.Predefined.Default;
      boolean boolean0 = cSVFormat0.equals(cSVFormat_Predefined0);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test203()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      boolean boolean0 = cSVFormat0.equals(cSVFormat0);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test204()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      boolean boolean0 = cSVFormat0.POSTGRESQL_TEXT.equals(cSVFormat0);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test205()  throws Throwable  {
      char[] charArray0 = new char[7];
      charArray0[0] = 'v';
      charArray0[6] = 'v';
      CharBuffer charBuffer0 = CharBuffer.wrap(charArray0);
      CharSequence charSequence0 = CSVFormat.trim(charBuffer0);
      assertEquals("v\u0000\u0000\u0000\u0000\u0000v", charSequence0.toString());
  }

  @Test(timeout = 4000)
  public void test206()  throws Throwable  {
      char[] charArray0 = new char[7];
      charArray0[0] = 'v';
      CharBuffer charBuffer0 = CharBuffer.wrap(charArray0);
      CharSequence charSequence0 = CSVFormat.trim(charBuffer0);
      assertEquals("v", charSequence0.toString());
  }

  @Test(timeout = 4000)
  public void test207()  throws Throwable  {
      Object[] objectArray0 = new Object[7];
      String[] stringArray0 = CSVFormat.toStringArray(objectArray0);
      assertEquals(7, stringArray0.length);
  }

  @Test(timeout = 4000)
  public void test208()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('v');
      CSVFormat cSVFormat1 = cSVFormat0.withCommentMarker((Character) null);
      assertFalse(cSVFormat1.getSkipHeaderRecord());
      assertFalse(cSVFormat1.getAutoFlush());
      assertFalse(cSVFormat1.getTrim());
      assertFalse(cSVFormat1.isNullStringSet());
      assertFalse(cSVFormat1.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat1.getIgnoreEmptyLines());
      assertFalse(cSVFormat1.getIgnoreHeaderCase());
      assertFalse(cSVFormat1.getTrailingDelimiter());
      assertFalse(cSVFormat1.getAllowMissingColumnNames());
      assertEquals("v", cSVFormat1.getDelimiterString());
  }

  @Test(timeout = 4000)
  public void test209()  throws Throwable  {
      boolean boolean0 = CSVFormat.isBlank("");
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test210()  throws Throwable  {
      boolean boolean0 = CSVFormat.isBlank("kqXFzPJpvz58+G3v");
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test211()  throws Throwable  {
      boolean boolean0 = CSVFormat.isBlank((String) null);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test212()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      CSVFormat.Builder cSVFormat_Builder0 = cSVFormat0.builder();
      // Undeclared exception!
      try { 
        cSVFormat_Builder0.setDelimiter("");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The delimiter cannot be empty
         //
         verifyException("org.apache.commons.csv.CSVFormat$Builder", e);
      }
  }

  @Test(timeout = 4000)
  public void test213()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      CSVFormat.Builder cSVFormat_Builder0 = cSVFormat0.builder();
      // Undeclared exception!
      try { 
        cSVFormat_Builder0.setDelimiter("\r\n");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The delimiter cannot be a line break
         //
         verifyException("org.apache.commons.csv.CSVFormat$Builder", e);
      }
  }

  @Test(timeout = 4000)
  public void test214()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      String string0 = cSVFormat0.getNullString();
      assertEquals("\\N", string0);
  }

  @Test(timeout = 4000)
  public void test215()  throws Throwable  {
      CSVFormat.Predefined cSVFormat_Predefined0 = CSVFormat.Predefined.PostgreSQLCsv;
      CSVFormat cSVFormat0 = cSVFormat_Predefined0.getFormat();
      boolean boolean0 = cSVFormat0.getIgnoreHeaderCase();
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test216()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      RowSetMetaDataImpl rowSetMetaDataImpl0 = new RowSetMetaDataImpl();
      CSVFormat cSVFormat1 = cSVFormat0.withHeader((ResultSetMetaData) rowSetMetaDataImpl0);
      assertFalse(cSVFormat1.isCommentMarkerSet());
  }

  @Test(timeout = 4000)
  public void test217()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat cSVFormat1 = cSVFormat0.withAllowMissingColumnNames();
      CSVFormat cSVFormat2 = cSVFormat1.withEscape('{');
      assertEquals('{', (char)cSVFormat2.getEscapeCharacter());
      assertTrue(cSVFormat2.getAllowMissingColumnNames());
  }

  @Test(timeout = 4000)
  public void test218()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      MockFile mockFile0 = new MockFile("string");
      Charset charset0 = Charset.defaultCharset();
      CSVPrinter cSVPrinter0 = cSVFormat0.print((File) mockFile0, charset0);
      assertNotNull(cSVPrinter0);
  }

  @Test(timeout = 4000)
  public void test219()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      DuplicateHeaderMode duplicateHeaderMode0 = cSVFormat0.getDuplicateHeaderMode();
      assertEquals(DuplicateHeaderMode.ALLOW_ALL, duplicateHeaderMode0);
  }

  @Test(timeout = 4000)
  public void test220()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreSurroundingSpaces(true);
      CSVFormat cSVFormat2 = cSVFormat1.withAllowDuplicateHeaderNames();
      CSVFormat cSVFormat3 = cSVFormat2.withCommentMarker('F');
      assertEquals('F', (char)cSVFormat3.getCommentMarker());
      assertTrue(cSVFormat3.getIgnoreSurroundingSpaces());
  }

  @Test(timeout = 4000)
  public void test221()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('3');
      CSVFormat cSVFormat1 = cSVFormat0.EXCEL.withSkipHeaderRecord(true);
      assertFalse(cSVFormat0.getAutoFlush());
      assertFalse(cSVFormat0.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat0.getIgnoreHeaderCase());
      assertFalse(cSVFormat0.getTrim());
      assertFalse(cSVFormat0.isNullStringSet());
      assertFalse(cSVFormat0.getIgnoreEmptyLines());
      assertEquals('3', cSVFormat0.getDelimiter());
      assertFalse(cSVFormat0.getTrailingDelimiter());
      assertFalse(cSVFormat0.getSkipHeaderRecord());
      assertTrue(cSVFormat1.getSkipHeaderRecord());
      assertFalse(cSVFormat0.getAllowMissingColumnNames());
  }

  @Test(timeout = 4000)
  public void test222()  throws Throwable  {
    Future<?> future = executor.submit(new Runnable(){ 
            @Override public void run() { 
        try {
          CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
          MockFile mockFile0 = new MockFile("org.apache.commons.csv.CSVParser", "");
          Path path0 = mockFile0.toPath();
          Charset charset0 = Charset.defaultCharset();
          // Undeclared exception!
          try { 
            cSVFormat0.print(path0, charset0);
            fail("Expecting exception: SecurityException");
          
          } catch(SecurityException e) {
             //
             // Security manager blocks (\"java.io.FilePermission\" \"/Users/<USER>/Desktop/java-project/commons-csv-rel-commons-csv-1.10.0/org.apache.commons.csv.CSVParser\" \"write\")
             // java.lang.Thread.getStackTrace(Thread.java:1559)
             // org.evosuite.runtime.sandbox.MSecurityManager.checkPermission(MSecurityManager.java:424)
             // java.lang.SecurityManager.checkWrite(SecurityManager.java:979)
             // sun.nio.fs.UnixChannelFactory.open(UnixChannelFactory.java:247)
             // sun.nio.fs.UnixChannelFactory.newFileChannel(UnixChannelFactory.java:136)
             // sun.nio.fs.UnixChannelFactory.newFileChannel(UnixChannelFactory.java:148)
             // sun.nio.fs.UnixFileSystemProvider.newByteChannel(UnixFileSystemProvider.java:212)
             // java.nio.file.spi.FileSystemProvider.newOutputStream(FileSystemProvider.java:434)
             // java.nio.file.Files.newOutputStream(Files.java:216)
             // java.nio.file.Files.newBufferedWriter(Files.java:2860)
             // org.apache.commons.csv.CSVFormat.print(CSVFormat.java:1886)
             // sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
             // sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
             // sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
             // java.lang.reflect.Method.invoke(Method.java:498)
             // org.evosuite.testcase.statements.MethodStatement$1.execute(MethodStatement.java:256)
             // org.evosuite.testcase.statements.AbstractStatement.exceptionHandler(AbstractStatement.java:165)
             // org.evosuite.testcase.statements.MethodStatement.execute(MethodStatement.java:219)
             // org.evosuite.testcase.execution.TestRunnable.executeStatements(TestRunnable.java:286)
             // org.evosuite.testcase.execution.TestRunnable.call(TestRunnable.java:192)
             // org.evosuite.testcase.execution.TestRunnable.call(TestRunnable.java:49)
             // java.util.concurrent.FutureTask.run(FutureTask.java:266)
             // java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
             // java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
             // java.lang.Thread.run(Thread.java:748)
             //
             verifyException("org.evosuite.runtime.sandbox.MSecurityManager", e);
          }
        } catch(Throwable t) {
            // Need to catch declared exceptions
        }
      } 
    });
    future.get(4000, TimeUnit.MILLISECONDS);
  }

  @Test(timeout = 4000)
  public void test223()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      // Undeclared exception!
      try { 
        cSVFormat0.print((Appendable) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // appendable
         //
         verifyException("java.util.Objects", e);
      }
  }

  @Test(timeout = 4000)
  public void test224()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      Character character0 = cSVFormat0.getQuoteCharacter();
      Object[] objectArray0 = new Object[7];
      objectArray0[0] = (Object) character0;
      cSVFormat0.format(objectArray0);
      assertTrue(cSVFormat0.isQuoteCharacterSet());
      assertTrue(cSVFormat0.isEscapeCharacterSet());
  }

  @Test(timeout = 4000)
  public void test225()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('3');
      CSVFormat cSVFormat1 = cSVFormat0.withTrailingDelimiter(true);
      CSVFormat cSVFormat2 = cSVFormat1.withIgnoreHeaderCase();
      assertFalse(cSVFormat2.getSkipHeaderRecord());
      assertFalse(cSVFormat2.getAllowMissingColumnNames());
      assertFalse(cSVFormat1.equals((Object)cSVFormat0));
      assertFalse(cSVFormat2.getIgnoreEmptyLines());
      assertFalse(cSVFormat2.equals((Object)cSVFormat1));
      assertEquals('3', cSVFormat2.getDelimiter());
      assertFalse(cSVFormat2.getTrim());
      assertFalse(cSVFormat2.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat2.getAutoFlush());
  }

  @Test(timeout = 4000)
  public void test226()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('v');
      cSVFormat0.POSTGRESQL_TEXT.hashCode();
      assertFalse(cSVFormat0.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat0.getIgnoreEmptyLines());
      assertFalse(cSVFormat0.getAutoFlush());
      assertFalse(cSVFormat0.getIgnoreHeaderCase());
      assertFalse(cSVFormat0.isNullStringSet());
      assertEquals("v", cSVFormat0.getDelimiterString());
      assertFalse(cSVFormat0.getTrailingDelimiter());
      assertFalse(cSVFormat0.getAllowMissingColumnNames());
      assertFalse(cSVFormat0.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test227()  throws Throwable  {
      char[] charArray0 = new char[7];
      CharBuffer charBuffer0 = CharBuffer.wrap(charArray0);
      CharSequence charSequence0 = CSVFormat.trim(charBuffer0);
      assertEquals(0, charSequence0.length());
  }

  @Test(timeout = 4000)
  public void test228()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      boolean boolean0 = cSVFormat0.getAllowMissingColumnNames();
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test229()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.withTrim();
      cSVFormat1.trim("6W0\"y+!#tLKTZ77$9 &");
      assertTrue(cSVFormat1.getTrim());
  }

  @Test(timeout = 4000)
  public void test230()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      PipedReader pipedReader0 = new PipedReader(24);
      CSVParser cSVParser0 = cSVFormat0.parse(pipedReader0);
      assertEquals(0L, cSVParser0.getRecordNumber());
  }

  @Test(timeout = 4000)
  public void test231()  throws Throwable  {
      Character character0 = new Character('0');
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat cSVFormat1 = cSVFormat0.MONGODB_CSV.withQuote(character0);
      assertNull(cSVFormat1.getNullString());
  }

  @Test(timeout = 4000)
  public void test232()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('+');
      ResultSet resultSet0 = mock(ResultSet.class, new ViolatedAssumptionAnswer());
      doReturn((ResultSetMetaData) null).when(resultSet0).getMetaData();
      CSVFormat cSVFormat1 = cSVFormat0.withHeader(resultSet0);
      assertFalse(cSVFormat1.getTrim());
      assertFalse(cSVFormat1.getAllowMissingColumnNames());
      assertFalse(cSVFormat1.getIgnoreHeaderCase());
      assertFalse(cSVFormat1.isNullStringSet());
      assertFalse(cSVFormat1.getIgnoreEmptyLines());
      assertFalse(cSVFormat1.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat1.getAutoFlush());
      assertEquals('+', cSVFormat1.getDelimiter());
      assertFalse(cSVFormat1.getTrailingDelimiter());
      assertFalse(cSVFormat1.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test233()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('3');
      Character character0 = Character.valueOf('i');
      cSVFormat0.TDF.withCommentMarker(character0);
      assertFalse(cSVFormat0.getIgnoreHeaderCase());
      assertFalse(cSVFormat0.getIgnoreEmptyLines());
      assertFalse(cSVFormat0.getAutoFlush());
      assertFalse(cSVFormat0.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat0.getTrim());
      assertFalse(cSVFormat0.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test234()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('3');
      CSVFormat cSVFormat1 = cSVFormat0.withCommentMarker('*');
      CSVFormat cSVFormat2 = cSVFormat1.withIgnoreEmptyLines();
      assertFalse(cSVFormat2.getIgnoreSurroundingSpaces());
      assertEquals('*', (char)cSVFormat2.getCommentMarker());
      assertFalse(cSVFormat2.getTrailingDelimiter());
      assertFalse(cSVFormat2.getAllowMissingColumnNames());
      assertFalse(cSVFormat2.getIgnoreHeaderCase());
      assertFalse(cSVFormat2.equals((Object)cSVFormat1));
      assertFalse(cSVFormat2.getSkipHeaderRecord());
      assertFalse(cSVFormat2.getTrim());
      assertFalse(cSVFormat2.isNullStringSet());
      assertFalse(cSVFormat2.getAutoFlush());
  }

  @Test(timeout = 4000)
  public void test235()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      String string0 = cSVFormat0.getRecordSeparator();
      assertEquals("\n", string0);
  }

  @Test(timeout = 4000)
  public void test236()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVPrinter cSVPrinter0 = cSVFormat0.printer();
      assertNotNull(cSVPrinter0);
  }

  @Test(timeout = 4000)
  public void test237()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('3');
      CSVFormat cSVFormat1 = cSVFormat0.withFirstRecordAsHeader();
      // Undeclared exception!
      try { 
        cSVFormat1.format((Object[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("java.util.Objects", e);
      }
  }

  @Test(timeout = 4000)
  public void test238()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      char char0 = cSVFormat0.getDelimiter();
      assertEquals(',', char0);
  }

  @Test(timeout = 4000)
  public void test239()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVFormat cSVFormat1 = cSVFormat0.withSkipHeaderRecord();
      Character character0 = Character.valueOf('i');
      CSVFormat cSVFormat2 = cSVFormat1.withEscape(character0);
      assertTrue(cSVFormat2.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test240()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setCommentMarker('(');
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test241()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      CSVFormat.Builder cSVFormat_Builder0 = cSVFormat0.builder();
      String[] stringArray0 = new String[5];
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setHeaderComments(stringArray0);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test242()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreSurroundingSpaces();
      assertTrue(cSVFormat1.getIgnoreSurroundingSpaces());
  }

  @Test(timeout = 4000)
  public void test243()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVFormat cSVFormat1 = cSVFormat0.INFORMIX_UNLOAD_CSV.withSystemRecordSeparator();
      assertNull(cSVFormat1.getEscapeCharacter());
  }

  @Test(timeout = 4000)
  public void test244()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setHeader((ResultSet) null);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test245()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreHeaderCase();
      assertTrue(cSVFormat1.getIgnoreHeaderCase());
  }
}
