/*
 * This file was automatically generated by EvoSuite
 * Wed Jun 18 09:27:56 GMT 2025
 */

package org.apache.commons.lang3;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.util.Arrays;
import java.util.UUID;
import org.apache.commons.lang3.Conversion;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.evosuite.runtime.mock.java.util.MockUUID;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true) 
public class Conversion_ESTest extends Conversion_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test000()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[6];
      // Undeclared exception!
      try { 
        Conversion.intToBinary(73, (short) (-1), booleanArray0, 9, 34);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+srcPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test001()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.shortToHex((short)5387, (short)5387, "(nBytes-1)*8+dstPos is greater or equal to than 32", (short)5387, (-587));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+srcPos is greater or equal to than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test002()  throws Throwable  {
      String string0 = Conversion.intToHex(0, (byte)0, "(nHexs-1)*4+srcPos is greater or equal to than 32", (-1178), (-818));
      assertEquals("(nHexs-1)*4+srcPos is greater or equal to than 32", string0);
  }

  @Test(timeout = 4000)
  public void test003()  throws Throwable  {
      String string0 = Conversion.longToHex(5, 1, "RW?#)osdCnA%7n", 1, 1);
      assertEquals("R2?#)osdCnA%7n", string0);
  }

  @Test(timeout = 4000)
  public void test004()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      byte byte0 = Conversion.binaryToByte(booleanArray0, (short)1263, (byte) (-80), (-1302), 0);
      assertEquals((byte) (-80), byte0);
  }

  @Test(timeout = 4000)
  public void test005()  throws Throwable  {
      short[] shortArray0 = new short[7];
      shortArray0[1] = (short) (-1896);
      // Undeclared exception!
      try { 
        Conversion.shortArrayToInt(shortArray0, (short)0, (short) (-1896), (-3614), 67);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 7
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test006()  throws Throwable  {
      short[] shortArray0 = new short[0];
      // Undeclared exception!
      try { 
        Conversion.shortArrayToInt(shortArray0, (-1651), 4131, (byte) (-77), (byte)111);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nShorts-1)*16+dstPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test007()  throws Throwable  {
      int[] intArray0 = new int[0];
      // Undeclared exception!
      try { 
        Conversion.intArrayToLong(intArray0, (short)231, 0, 70, 3179);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nInts-1)*32+dstPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test008()  throws Throwable  {
      UUID uUID0 = MockUUID.randomUUID();
      byte[] byteArray0 = Conversion.uuidToByteArray(uUID0, (byte[]) null, (-1), (-534));
      assertNull(byteArray0);
  }

  @Test(timeout = 4000)
  public void test009()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      byte[] byteArray1 = Conversion.shortToByteArray((short)0, (short)0, byteArray0, (-2268), (-501));
      assertArrayEquals(new byte[] {}, byteArray1);
  }

  @Test(timeout = 4000)
  public void test010()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      boolean[] booleanArray1 = Conversion.shortToBinary((short)3, (short) (-983), booleanArray0, (short) (-834), (-2582));
      assertSame(booleanArray0, booleanArray1);
  }

  @Test(timeout = 4000)
  public void test011()  throws Throwable  {
      short[] shortArray0 = new short[4];
      long long0 = Conversion.shortArrayToLong(shortArray0, (short)1061, (short)1061, (short)0, (short)0);
      assertEquals(1061L, long0);
  }

  @Test(timeout = 4000)
  public void test012()  throws Throwable  {
      short[] shortArray0 = new short[1];
      int int0 = Conversion.shortArrayToInt(shortArray0, (short)639, 0, (short)639, (-2658));
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test013()  throws Throwable  {
      short[] shortArray0 = new short[9];
      int int0 = Conversion.shortArrayToInt(shortArray0, (short)1263, 216, (short)1842, (-2345));
      assertEquals(216, int0);
  }

  @Test(timeout = 4000)
  public void test014()  throws Throwable  {
      short[] shortArray0 = Conversion.longToShortArray((-1L), (short)0, (short[]) null, 1, (short)0);
      assertNull(shortArray0);
  }

  @Test(timeout = 4000)
  public void test015()  throws Throwable  {
      String string0 = Conversion.longToHex(0L, (-471), "", (-565), (-565));
      assertEquals("", string0);
  }

  @Test(timeout = 4000)
  public void test016()  throws Throwable  {
      byte[] byteArray0 = Conversion.longToByteArray(944L, 51, (byte[]) null, 51, (-1950));
      assertNull(byteArray0);
  }

  @Test(timeout = 4000)
  public void test017()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      boolean[] booleanArray1 = Conversion.longToBinary((short) (-1616), 2679, booleanArray0, (-640), (short)0);
      assertEquals(0, booleanArray1.length);
  }

  @Test(timeout = 4000)
  public void test018()  throws Throwable  {
      short[] shortArray0 = new short[9];
      short[] shortArray1 = Conversion.intToShortArray((short)98, (short)0, shortArray0, (short) (-1), (short) (-390));
      assertEquals(9, shortArray1.length);
  }

  @Test(timeout = 4000)
  public void test019()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      boolean[] booleanArray1 = Conversion.intToBinary(611, 1, booleanArray0, 611, (-925));
      assertSame(booleanArray1, booleanArray0);
  }

  @Test(timeout = 4000)
  public void test020()  throws Throwable  {
      int[] intArray0 = new int[1];
      long long0 = Conversion.intArrayToLong(intArray0, 11, 2341, 2341, 0);
      assertEquals(2341L, long0);
  }

  @Test(timeout = 4000)
  public void test021()  throws Throwable  {
      short short0 = Conversion.hexToShort("", 4, (short) (-2336), 327, (-1449));
      assertEquals((short) (-2336), short0);
  }

  @Test(timeout = 4000)
  public void test022()  throws Throwable  {
      long long0 = Conversion.hexToLong("", (short)0, 65535L, 57, (short)0);
      assertEquals(65535L, long0);
  }

  @Test(timeout = 4000)
  public void test023()  throws Throwable  {
      int int0 = Conversion.hexToInt("\"t)*I)+;9", 514, 98, 0, (byte) (-25));
      assertEquals(98, int0);
  }

  @Test(timeout = 4000)
  public void test024()  throws Throwable  {
      int int0 = Conversion.hexToInt("Lku~p0Ka<", 98, (-1015), 98, (-729));
      assertEquals((-1015), int0);
  }

  @Test(timeout = 4000)
  public void test025()  throws Throwable  {
      int int0 = Conversion.hexDigitToInt('0');
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test026()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.byteToBinary((byte)91, (short) (-1), (boolean[]) null, (short) (-1), (short) (-1));
      assertNull(booleanArray0);
  }

  @Test(timeout = 4000)
  public void test027()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      long long0 = Conversion.byteArrayToLong(byteArray0, (-1), (-159L), 349, 0);
      assertEquals((-159L), long0);
  }

  @Test(timeout = 4000)
  public void test028()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[6];
      long long0 = Conversion.binaryToLong(booleanArray0, 0, 2073L, 0, 0);
      assertEquals(2073L, long0);
  }

  @Test(timeout = 4000)
  public void test029()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.uuidToByteArray((UUID) null, (byte[]) null, (short)0, (byte)16);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test030()  throws Throwable  {
      UUID uUID0 = MockUUID.fromString("(nBytes-1)*8+srcPos is greater or equal to than 64");
      byte[] byteArray0 = new byte[5];
      // Undeclared exception!
      try { 
        Conversion.uuidToByteArray(uUID0, byteArray0, (byte) (-109), 3);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -109
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test031()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.shortArrayToLong((short[]) null, 871, (-1), (-3335), (byte) (-18));
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test032()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.shortArrayToInt((short[]) null, 2233, (-95), 2233, 2233);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test033()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.longToBinary(0L, 7, (boolean[]) null, (-1), 7);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test034()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.intArrayToLong((int[]) null, 171, 0L, 171, (byte) (-1));
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test035()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.byteArrayToUuid((byte[]) null, (byte) (-18));
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test036()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.byteArrayToLong((byte[]) null, (byte) (-32), (-650L), (-2355), (byte)0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test037()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.byteArrayToInt((byte[]) null, 1748, (byte) (-96), (byte) (-96), (-1623));
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test038()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.binaryToLong((boolean[]) null, 97, 97, (-1), 2);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test039()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      // Undeclared exception!
      try { 
        Conversion.binaryToLong(booleanArray0, (-2186), (-992L), (-2186), 94);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -2186
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test040()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.binaryToInt((boolean[]) null, 3, 3, 3, 3);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test041()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigitMsb0_4bits((boolean[]) null, (int) (short)0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test042()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigit((boolean[]) null, (-205));
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test043()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigit((boolean[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test044()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.binaryToByte((boolean[]) null, 101, (byte) (-1), (byte) (-96), 101);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test045()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.binaryBeMsb0ToHexDigit((boolean[]) null, (-122));
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test046()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[9];
      // Undeclared exception!
      try { 
        Conversion.binaryBeMsb0ToHexDigit(booleanArray0, (-458));
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
      }
  }

  @Test(timeout = 4000)
  public void test047()  throws Throwable  {
      char char0 = Conversion.intToHexDigit(0);
      assertEquals('0', char0);
  }

  @Test(timeout = 4000)
  public void test048()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, 748);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // src.length-srcPos<4: src.length=0, srcPos=748
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test049()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[9];
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, 2391);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // src.length>8: src.length=9
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test050()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigit(booleanArray0, 2155);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 2155
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test051()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[9];
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigit(booleanArray0, (-1084));
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -1081
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test052()  throws Throwable  {
      byte[] byteArray0 = new byte[4];
      // Undeclared exception!
      try { 
        Conversion.byteArrayToUuid(byteArray0, 14);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Need at least 16 bytes for UUID
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test053()  throws Throwable  {
      byte[] byteArray0 = new byte[1];
      // Undeclared exception!
      try { 
        Conversion.byteArrayToUuid(byteArray0, (byte) (-65));
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -65
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test054()  throws Throwable  {
      UUID uUID0 = MockUUID.randomUUID();
      byte[] byteArray0 = new byte[3];
      // Undeclared exception!
      try { 
        Conversion.uuidToByteArray(uUID0, byteArray0, (byte) (-102), 911);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBytes is greater than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test055()  throws Throwable  {
      UUID uUID0 = MockUUID.randomUUID();
      byte[] byteArray0 = new byte[0];
      byte[] byteArray1 = Conversion.uuidToByteArray(uUID0, byteArray0, (short)0, (short)0);
      assertSame(byteArray0, byteArray1);
  }

  @Test(timeout = 4000)
  public void test056()  throws Throwable  {
      UUID uUID0 = MockUUID.randomUUID();
      byte[] byteArray0 = new byte[9];
      byte[] byteArray1 = Conversion.uuidToByteArray(uUID0, byteArray0, (-4646), (short) (-732));
      assertSame(byteArray1, byteArray0);
  }

  @Test(timeout = 4000)
  public void test057()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[9];
      // Undeclared exception!
      try { 
        Conversion.byteToBinary((byte)0, (byte)0, booleanArray0, (-2825), 4);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -2825
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test058()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.byteToBinary((byte) (-1), (-3078), (boolean[]) null, 70, 371);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test059()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      // Undeclared exception!
      try { 
        Conversion.byteToBinary((byte)99, 3387, booleanArray0, 0, 101);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+srcPos is greater or equal to than 8
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test060()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      boolean[] booleanArray1 = Conversion.byteToBinary((byte)91, (byte)91, booleanArray0, (-52), (short)0);
      assertSame(booleanArray0, booleanArray1);
  }

  @Test(timeout = 4000)
  public void test061()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[1];
      boolean[] booleanArray1 = Conversion.byteToBinary((byte)84, (-2003), booleanArray0, (byte) (-96), (-2003));
      assertEquals(1, booleanArray1.length);
  }

  @Test(timeout = 4000)
  public void test062()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[4];
      // Undeclared exception!
      try { 
        Conversion.shortToBinary((short) (-823), (-4200), booleanArray0, 4, 4);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 4
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test063()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.shortToBinary((short)2931, (-613), (boolean[]) null, (-613), 97);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test064()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      // Undeclared exception!
      try { 
        Conversion.shortToBinary((byte)4, 93, booleanArray0, 0, 1244);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+srcPos is greater or equal to than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test065()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[4];
      boolean[] booleanArray1 = Conversion.shortToBinary((short)0, 92, booleanArray0, 1110, 0);
      assertTrue(Arrays.equals(new boolean[] {false, false, false, false}, booleanArray1));
  }

  @Test(timeout = 4000)
  public void test066()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.shortToBinary((byte) (-18), (byte) (-18), (boolean[]) null, 0, (byte) (-18));
      assertNull(booleanArray0);
  }

  @Test(timeout = 4000)
  public void test067()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      // Undeclared exception!
      try { 
        Conversion.intToBinary(1, (-2737), booleanArray0, (-137), 1218);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -137
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test068()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      boolean[] booleanArray1 = Conversion.intToBinary(611, 1, booleanArray0, 611, (-925));
      assertSame(booleanArray0, booleanArray1);
  }

  @Test(timeout = 4000)
  public void test069()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.intToBinary((-477), 102, (boolean[]) null, (-1509), 234);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+srcPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test070()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[9];
      // Undeclared exception!
      try { 
        Conversion.intToBinary((-1755), (-1891), booleanArray0, (-1891), 98);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -1891
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test071()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[6];
      boolean[] booleanArray1 = Conversion.longToBinary((short)0, (short)0, booleanArray0, 2223, (-352));
      assertSame(booleanArray1, booleanArray0);
  }

  @Test(timeout = 4000)
  public void test072()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      // Undeclared exception!
      try { 
        Conversion.longToBinary(2032L, (byte) (-77), booleanArray0, (byte) (-77), 50);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -77
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test073()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.longToBinary((-1291L), 717, (boolean[]) null, 717, 717);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+srcPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test074()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.byteToHex((byte)1, (byte)1, "", 3065, (byte)1);
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
         //
         // String index out of range: 3065
         //
         verifyException("java.lang.AbstractStringBuilder", e);
      }
  }

  @Test(timeout = 4000)
  public void test075()  throws Throwable  {
      String string0 = Conversion.byteToHex((byte)0, (-1319), "(nHexs-1)*4+srcPos is greater or equal to than 32", (-1319), (byte)0);
      assertEquals("(nHexs-1)*4+srcPos is greater or equal to than 32", string0);
  }

  @Test(timeout = 4000)
  public void test076()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.byteToHex((byte) (-70), 911, "", (-4782), (short)98);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+srcPos is greater or equal to than 8
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test077()  throws Throwable  {
      String string0 = Conversion.shortToHex((short)0, (-3046), "", 13, 0);
      assertEquals("", string0);
  }

  @Test(timeout = 4000)
  public void test078()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.shortToHex((short)54, 102, "\";opp6gh_ Un6T6SB8", 102, 88);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+srcPos is greater or equal to than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test079()  throws Throwable  {
      String string0 = Conversion.intToHex(134, 1, "&|T`ceCD/U", 2, 1);
      assertEquals("&|3`ceCD/U", string0);
  }

  @Test(timeout = 4000)
  public void test080()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.intToHex(4745, 4745, "", 8, 8);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+srcPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test081()  throws Throwable  {
      String string0 = Conversion.intToHex(2761, 2761, "", 0, 0);
      assertEquals("", string0);
  }

  @Test(timeout = 4000)
  public void test082()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.longToHex((-1898), (-471), "", (byte)4, 93);
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
         //
         // String index out of range: 4
         //
         verifyException("java.lang.AbstractStringBuilder", e);
      }
  }

  @Test(timeout = 4000)
  public void test083()  throws Throwable  {
      String string0 = Conversion.longToHex((byte)0, 171, "CzWl_z;WP", 171, (byte)0);
      assertEquals("CzWl_z;WP", string0);
  }

  @Test(timeout = 4000)
  public void test084()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.longToHex((-2618), 721, "", 48, 16);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+srcPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test085()  throws Throwable  {
      byte[] byteArray0 = new byte[4];
      byte[] byteArray1 = Conversion.shortToByteArray((short)137, 4745, byteArray0, (byte) (-39), (-789));
      assertSame(byteArray1, byteArray0);
  }

  @Test(timeout = 4000)
  public void test086()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.shortToByteArray((short)32, 77, (byte[]) null, (byte)29, 76);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nBytes-1)*8+srcPos is greater or equal to than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test087()  throws Throwable  {
      byte[] byteArray0 = Conversion.shortToByteArray((short)2, 0, (byte[]) null, 0, 0);
      assertNull(byteArray0);
  }

  @Test(timeout = 4000)
  public void test088()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      // Undeclared exception!
      try { 
        Conversion.shortToByteArray((byte) (-33), (-2784), byteArray0, 16, 16);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test089()  throws Throwable  {
      byte[] byteArray0 = new byte[8];
      byte[] byteArray1 = Conversion.intToByteArray((byte) (-44), (byte) (-44), byteArray0, 92, (short) (-1566));
      assertArrayEquals(new byte[] {(byte)0, (byte)0, (byte)0, (byte)0, (byte)0, (byte)0, (byte)0, (byte)0}, byteArray1);
  }

  @Test(timeout = 4000)
  public void test090()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      // Undeclared exception!
      try { 
        Conversion.intToByteArray((short) (-1), (-2833), byteArray0, (short) (-1), 68);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -1
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test091()  throws Throwable  {
      byte[] byteArray0 = new byte[1];
      // Undeclared exception!
      try { 
        Conversion.intToByteArray(75, 75, byteArray0, 13, 82);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nBytes-1)*8+srcPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test092()  throws Throwable  {
      byte[] byteArray0 = new byte[1];
      byte[] byteArray1 = Conversion.longToByteArray((-1L), (-771), byteArray0, (byte)25, (-771));
      assertSame(byteArray1, byteArray0);
  }

  @Test(timeout = 4000)
  public void test093()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.longToByteArray(1, (-1), (byte[]) null, 0, 80);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nBytes-1)*8+srcPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test094()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      // Undeclared exception!
      try { 
        Conversion.longToByteArray(0L, 6, byteArray0, 6, 1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 6
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test095()  throws Throwable  {
      short[] shortArray0 = new short[9];
      // Undeclared exception!
      try { 
        Conversion.intToShortArray((short) (-390), 0, shortArray0, 1, 102);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nShorts-1)*16+srcPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test096()  throws Throwable  {
      short[] shortArray0 = Conversion.intToShortArray((short)0, 0, (short[]) null, 1, 0);
      assertNull(shortArray0);
  }

  @Test(timeout = 4000)
  public void test097()  throws Throwable  {
      short[] shortArray0 = new short[7];
      short[] shortArray1 = Conversion.longToShortArray(2215, 16, shortArray0, (short)1, (short)1);
      assertArrayEquals(new short[] {(short)0, (short)0, (short)0, (short)0, (short)0, (short)0, (short)0}, shortArray1);
  }

  @Test(timeout = 4000)
  public void test098()  throws Throwable  {
      short[] shortArray0 = new short[5];
      short[] shortArray1 = Conversion.longToShortArray((-960L), 50, shortArray0, (-732), (short) (-1));
      assertSame(shortArray1, shortArray0);
  }

  @Test(timeout = 4000)
  public void test099()  throws Throwable  {
      short[] shortArray0 = new short[9];
      // Undeclared exception!
      try { 
        Conversion.longToShortArray((short)648, 1727, shortArray0, 59, 54);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nShorts-1)*16+srcPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test100()  throws Throwable  {
      int[] intArray0 = new int[5];
      // Undeclared exception!
      try { 
        Conversion.longToIntArray(0, (-2382), intArray0, 259, 65);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 259
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test101()  throws Throwable  {
      int[] intArray0 = new int[3];
      // Undeclared exception!
      try { 
        Conversion.longToIntArray(1417, 0, intArray0, 1417, 1417);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nInts-1)*32+srcPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test102()  throws Throwable  {
      int[] intArray0 = new int[5];
      int[] intArray1 = Conversion.longToIntArray(99, 0, intArray0, 122, 0);
      assertEquals(5, intArray1.length);
  }

  @Test(timeout = 4000)
  public void test103()  throws Throwable  {
      int[] intArray0 = Conversion.longToIntArray((-803L), (-1783), (int[]) null, (short)0, (-2335));
      assertNull(intArray0);
  }

  @Test(timeout = 4000)
  public void test104()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[1];
      // Undeclared exception!
      try { 
        Conversion.binaryToByte(booleanArray0, (byte)57, (byte)57, 1306, 1912);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+dstPos is greater or equal to than 8
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test105()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      byte byte0 = Conversion.binaryToByte(booleanArray0, 0, (byte) (-96), (byte) (-96), 343);
      assertEquals((byte) (-96), byte0);
  }

  @Test(timeout = 4000)
  public void test106()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      byte byte0 = Conversion.binaryToByte(booleanArray0, (-46), (byte) (-81), (byte) (-81), (-46));
      assertEquals((byte) (-81), byte0);
  }

  @Test(timeout = 4000)
  public void test107()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[1];
      byte byte0 = Conversion.binaryToByte(booleanArray0, (-404), (byte)57, (-404), (byte) (-96));
      assertEquals((byte)57, byte0);
  }

  @Test(timeout = 4000)
  public void test108()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[22];
      short short0 = Conversion.binaryToShort(booleanArray0, 0, (short) (-983), (short) (-983), (-180));
      assertEquals((short) (-983), short0);
  }

  @Test(timeout = 4000)
  public void test109()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[3];
      short short0 = Conversion.binaryToShort(booleanArray0, (-5864), (short) (-1019), (-1), 0);
      assertEquals((short) (-1019), short0);
  }

  @Test(timeout = 4000)
  public void test110()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      // Undeclared exception!
      try { 
        Conversion.binaryToInt(booleanArray0, 1, 16, 1, 16);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 8
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test111()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      int int0 = Conversion.binaryToInt(booleanArray0, (-2027), (-2027), (-2027), (-1060));
      assertEquals((-2027), int0);
  }

  @Test(timeout = 4000)
  public void test112()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[4];
      // Undeclared exception!
      try { 
        Conversion.binaryToInt(booleanArray0, (byte)50, (byte)50, (byte)50, (byte)64);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+dstPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test113()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[4];
      int int0 = Conversion.binaryToInt(booleanArray0, 4197, 523, 4197, (byte)0);
      assertEquals(523, int0);
  }

  @Test(timeout = 4000)
  public void test114()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[3];
      long long0 = Conversion.binaryToLong(booleanArray0, 0, (-1L), 55, 1);
      assertEquals((-36028797018963969L), long0);
  }

  @Test(timeout = 4000)
  public void test115()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[1];
      long long0 = Conversion.binaryToLong(booleanArray0, 4745, 0, (-1), (-1));
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test116()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToByte((String) null, (-2031), (byte)4, (-2031), 26);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test117()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToByte("nBools-1+dstPos is greater or equal to than 64", 69, (byte)2, 80, (byte)2);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+dstPos is greater or equal to than 8
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test118()  throws Throwable  {
      byte byte0 = Conversion.hexToByte("", (byte)0, (byte)101, (-2340), (byte)0);
      assertEquals((byte)101, byte0);
  }

  @Test(timeout = 4000)
  public void test119()  throws Throwable  {
      byte byte0 = Conversion.hexToByte("", (-1), (byte) (-18), (byte) (-18), (-1));
      assertEquals((byte) (-18), byte0);
  }

  @Test(timeout = 4000)
  public void test120()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToShort("", 1, (short)73, 1, 1);
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
      }
  }

  @Test(timeout = 4000)
  public void test121()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToShort("nBools-1+dstPos is greater or equal to than 32", 2025, (short)95, 2025, 2025);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+dstPos is greater or equal to than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test122()  throws Throwable  {
      short short0 = Conversion.hexToShort("Cnnot interpret '", (-3046), (short)0, (-3046), 0);
      assertEquals((short)0, short0);
  }

  @Test(timeout = 4000)
  public void test123()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToInt("@q", 97, (short)0, (-4472), 60);
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
      }
  }

  @Test(timeout = 4000)
  public void test124()  throws Throwable  {
      int int0 = Conversion.hexToInt((String) null, (-794), 0, 0, (-1));
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test125()  throws Throwable  {
      int int0 = Conversion.hexToInt("nBools-1+dstPos is greater or equal to than 64", 60, (short)0, (byte)0, (short)0);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test126()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToInt("", 2505, 616, 0, 32);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+dstPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test127()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToLong((String) null, 11, 0L, 102, 102);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+dstPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test128()  throws Throwable  {
      long long0 = Conversion.hexToLong("", (short)0, (-1075L), 57, (short)0);
      assertEquals((-1075L), long0);
  }

  @Test(timeout = 4000)
  public void test129()  throws Throwable  {
      long long0 = Conversion.hexToLong("a99e)SoeJH", (-356), 0L, (-1902), (-2003));
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test130()  throws Throwable  {
      byte[] byteArray0 = new byte[2];
      // Undeclared exception!
      try { 
        Conversion.byteArrayToInt(byteArray0, (-2375), 0, 0, 1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -2375
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test131()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      int int0 = Conversion.byteArrayToInt(byteArray0, (short) (-1616), (short) (-2422), (-258), (byte)0);
      assertEquals((-2422), int0);
  }

  @Test(timeout = 4000)
  public void test132()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      int int0 = Conversion.byteArrayToInt(byteArray0, (short)0, (short) (-1616), 83, (short) (-2422));
      assertEquals((-1616), int0);
  }

  @Test(timeout = 4000)
  public void test133()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      int int0 = Conversion.byteArrayToInt(byteArray0, (byte) (-18), (-1), (-18), (-2617));
      assertEquals((-1), int0);
  }

  @Test(timeout = 4000)
  public void test134()  throws Throwable  {
      byte[] byteArray0 = new byte[9];
      // Undeclared exception!
      try { 
        Conversion.byteArrayToInt(byteArray0, (-1763), (byte) (-16), (byte)0, 2150);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nBytes-1)*8+dstPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test135()  throws Throwable  {
      byte[] byteArray0 = new byte[8];
      // Undeclared exception!
      try { 
        Conversion.byteArrayToLong(byteArray0, 1908, 65535L, (-3148), 11);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 1908
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test136()  throws Throwable  {
      byte[] byteArray0 = new byte[8];
      long long0 = Conversion.byteArrayToLong(byteArray0, (-1227), 1L, (-1589), (short)0);
      assertEquals(1L, long0);
  }

  @Test(timeout = 4000)
  public void test137()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      // Undeclared exception!
      try { 
        Conversion.byteArrayToLong(byteArray0, (byte) (-18), 49, (-1), 101);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nBytes-1)*8+dstPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test138()  throws Throwable  {
      byte[] byteArray0 = new byte[8];
      long long0 = Conversion.byteArrayToLong(byteArray0, (-289), 1L, (byte) (-77), (-429));
      assertEquals(1L, long0);
  }

  @Test(timeout = 4000)
  public void test139()  throws Throwable  {
      short[] shortArray0 = new short[1];
      int int0 = Conversion.shortArrayToInt(shortArray0, (-2345), (short) (-1896), 216, (-2345));
      assertEquals((-1896), int0);
  }

  @Test(timeout = 4000)
  public void test140()  throws Throwable  {
      short[] shortArray0 = new short[0];
      // Undeclared exception!
      try { 
        Conversion.shortArrayToInt(shortArray0, 98, 98, 98, 98);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nShorts-1)*16+dstPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test141()  throws Throwable  {
      short[] shortArray0 = new short[9];
      long long0 = Conversion.shortArrayToLong(shortArray0, (short)0, (short) (-2219), 0, (short) (-834));
      assertEquals((-2219L), long0);
  }

  @Test(timeout = 4000)
  public void test142()  throws Throwable  {
      short[] shortArray0 = new short[4];
      // Undeclared exception!
      try { 
        Conversion.shortArrayToLong(shortArray0, (short) (-1), (-887L), (short) (-1), 14);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nShorts-1)*16+dstPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test143()  throws Throwable  {
      short[] shortArray0 = new short[9];
      long long0 = Conversion.shortArrayToLong(shortArray0, 1814, (short)0, (short)55, (short)0);
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test144()  throws Throwable  {
      int[] intArray0 = new int[5];
      long long0 = Conversion.intArrayToLong(intArray0, 90, (-1578), (-1578), (-1721));
      assertEquals((-1578L), long0);
  }

  @Test(timeout = 4000)
  public void test145()  throws Throwable  {
      int[] intArray0 = new int[0];
      long long0 = Conversion.intArrayToLong(intArray0, (short)0, 0, 70, 3179);
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test146()  throws Throwable  {
      int[] intArray0 = new int[0];
      // Undeclared exception!
      try { 
        Conversion.intArrayToLong(intArray0, (-1980), 0L, (-1980), 29);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -1980
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test147()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.intToHexDigitMsb0(71);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nibble value not between 0 and 15: 71
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test148()  throws Throwable  {
      char char0 = Conversion.intToHexDigitMsb0((short)0);
      assertEquals('0', char0);
  }

  @Test(timeout = 4000)
  public void test149()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.intToHexDigit((-416));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nibble value not between 0 and 15: -416
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test150()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.intToHex(4745, 0, "", 8, 8);
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
         //
         // String index out of range: 8
         //
         verifyException("java.lang.AbstractStringBuilder", e);
      }
  }

  @Test(timeout = 4000)
  public void test151()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[3];
      booleanArray0[2] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0);
      assertEquals('1', char0);
  }

  @Test(timeout = 4000)
  public void test152()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[7];
      booleanArray0[5] = true;
      booleanArray0[6] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0);
      assertEquals('3', char0);
  }

  @Test(timeout = 4000)
  public void test153()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[6];
      booleanArray0[4] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0, 0);
      assertEquals('2', char0);
  }

  @Test(timeout = 4000)
  public void test154()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[4];
      booleanArray0[1] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0, 0);
      assertEquals('4', char0);
  }

  @Test(timeout = 4000)
  public void test155()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[6];
      booleanArray0[2] = true;
      booleanArray0[3] = true;
      booleanArray0[5] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0);
      assertEquals('d', char0);
  }

  @Test(timeout = 4000)
  public void test156()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[6];
      booleanArray0[2] = true;
      booleanArray0[3] = true;
      booleanArray0[4] = true;
      booleanArray0[5] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0);
      assertEquals('f', char0);
  }

  @Test(timeout = 4000)
  public void test157()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[6];
      booleanArray0[2] = true;
      booleanArray0[3] = true;
      booleanArray0[4] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0);
      assertEquals('e', char0);
  }

  @Test(timeout = 4000)
  public void test158()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      booleanArray0[1] = true;
      booleanArray0[2] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0, 0);
      assertEquals('c', char0);
  }

  @Test(timeout = 4000)
  public void test159()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      booleanArray0[1] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0, 0);
      assertEquals('8', char0);
  }

  @Test(timeout = 4000)
  public void test160()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[4];
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0, 0);
      assertEquals('0', char0);
  }

  @Test(timeout = 4000)
  public void test161()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      booleanArray0[0] = true;
      booleanArray0[1] = true;
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0);
      assertEquals('c', char0);
  }

  @Test(timeout = 4000)
  public void test162()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[4];
      booleanArray0[1] = true;
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0);
      assertEquals('4', char0);
  }

  @Test(timeout = 4000)
  public void test163()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      booleanArray0[0] = true;
      booleanArray0[1] = true;
      booleanArray0[2] = true;
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0);
      assertEquals('e', char0);
  }

  @Test(timeout = 4000)
  public void test164()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      booleanArray0[1] = true;
      booleanArray0[2] = true;
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0);
      assertEquals('6', char0);
  }

  @Test(timeout = 4000)
  public void test165()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      booleanArray0[2] = true;
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0);
      assertEquals('2', char0);
  }

  @Test(timeout = 4000)
  public void test166()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      booleanArray0[1] = true;
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, 0);
      assertEquals('5', char0);
  }

  @Test(timeout = 4000)
  public void test167()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, 0);
      assertEquals('1', char0);
  }

  @Test(timeout = 4000)
  public void test168()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[4];
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0);
      assertEquals('0', char0);
  }

  @Test(timeout = 4000)
  public void test169()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[9];
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigitMsb0_4bits(booleanArray0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // src.length>8: src.length=9
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test170()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[2];
      booleanArray0[0] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0);
      assertEquals('1', char0);
  }

  @Test(timeout = 4000)
  public void test171()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[2];
      booleanArray0[0] = true;
      booleanArray0[1] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0);
      assertEquals('3', char0);
  }

  @Test(timeout = 4000)
  public void test172()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[2];
      booleanArray0[1] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0);
      assertEquals('2', char0);
  }

  @Test(timeout = 4000)
  public void test173()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[1];
      char char0 = Conversion.binaryToHexDigit(booleanArray0);
      assertEquals('0', char0);
  }

  @Test(timeout = 4000)
  public void test174()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[3];
      booleanArray0[2] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0);
      assertEquals('4', char0);
  }

  @Test(timeout = 4000)
  public void test175()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      booleanArray0[1] = true;
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0);
      assertEquals('a', char0);
  }

  @Test(timeout = 4000)
  public void test176()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      char char0 = Conversion.binaryToHexDigit(booleanArray0);
      assertEquals('0', char0);
  }

  @Test(timeout = 4000)
  public void test177()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigit(booleanArray0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot convert an empty array.
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test178()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToBinary('%');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret '%' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test179()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitMsb0ToBinary('f');
      assertEquals(4, booleanArray0.length);
  }

  @Test(timeout = 4000)
  public void test180()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToBinary('^');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret '^' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test181()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToBinary('\\');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret '\\' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test182()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToBinary('X');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'X' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test183()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToBinary('W');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'W' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test184()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToBinary('P');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'P' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test185()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToBinary('O');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'O' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test186()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToBinary('M');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'M' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test187()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToBinary('I');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'I' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test188()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitMsb0ToBinary('D');
      assertTrue(Arrays.equals(new boolean[] {true, true, false, true}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test189()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitMsb0ToBinary('A');
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, (-1752));
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -1749
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test190()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToBinary('<');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret '<' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test191()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToBinary(';');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret ';' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test192()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitMsb0ToBinary('4');
      assertTrue(Arrays.equals(new boolean[] {false, true, false, false}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test193()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitMsb0ToBinary('3');
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0);
      assertEquals('3', char0);
  }

  @Test(timeout = 4000)
  public void test194()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitMsb0ToBinary('1');
      assertTrue(Arrays.equals(new boolean[] {false, false, false, true}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test195()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitMsb0ToBinary('a');
      // Undeclared exception!
      try { 
        Conversion.binaryToLong(booleanArray0, 1, 16, 16, 134);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+dstPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test196()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitMsb0ToBinary('0');
      assertTrue(Arrays.equals(new boolean[] {false, false, false, false}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test197()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitToBinary('.');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret '.' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test198()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitToBinary('e');
      assertTrue(Arrays.equals(new boolean[] {false, true, true, true}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test199()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitToBinary('b');
      char char0 = Conversion.binaryToHexDigit(booleanArray0);
      assertEquals('b', char0);
  }

  @Test(timeout = 4000)
  public void test200()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitToBinary('V');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'V' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test201()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitToBinary('I');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'I' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test202()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitToBinary('E');
      char char0 = Conversion.binaryToHexDigit(booleanArray0);
      assertEquals('e', char0);
  }

  @Test(timeout = 4000)
  public void test203()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitToBinary('A');
      assertTrue(Arrays.equals(new boolean[] {false, true, false, true}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test204()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitToBinary('4');
      assertTrue(Arrays.equals(new boolean[] {false, false, true, false}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test205()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitToBinary('3');
      assertTrue(Arrays.equals(new boolean[] {true, true, false, false}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test206()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitToBinary('W');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'W' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test207()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitToBinary('0');
      assertTrue(Arrays.equals(new boolean[] {false, false, false, false}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test208()  throws Throwable  {
      int int0 = Conversion.hexDigitMsb0ToInt('f');
      assertEquals(15, int0);
  }

  @Test(timeout = 4000)
  public void test209()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToInt('\\');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret '\\' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test210()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToInt('L');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'L' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test211()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToInt('=');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret '=' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test212()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToInt(';');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret ';' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test213()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToInt('h');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'h' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test214()  throws Throwable  {
      int int0 = Conversion.hexDigitMsb0ToInt('4');
      assertEquals(2, int0);
  }

  @Test(timeout = 4000)
  public void test215()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitToInt('q');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'q' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test216()  throws Throwable  {
      int int0 = Conversion.hexDigitToInt('e');
      assertEquals(14, int0);
  }

  @Test(timeout = 4000)
  public void test217()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.binaryBeMsb0ToHexDigit((boolean[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test218()  throws Throwable  {
      Conversion conversion0 = new Conversion();
  }
}
