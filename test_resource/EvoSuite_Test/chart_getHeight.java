/*
 * This file was automatically generated by EvoSuite
 * Wed Jun 18 07:20:29 GMT 2025
 */

package org.jfree.chart.ui;

import org.junit.Test;
import static org.junit.Assert.*;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.jfree.chart.ui.Size2D;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true) 
public class Size2D_ESTest extends Size2D_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      Size2D size2D0 = new Size2D((-2664.732064), (-2664.732064));
      size2D0.hashCode();
      assertEquals((-2664.732064), size2D0.width, 0.01);
      assertEquals((-2664.732064), size2D0.height, 0.01);
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      Size2D size2D0 = new Size2D();
      assertEquals(0.0, size2D0.getHeight(), 0.01);
      
      size2D0.height = 0.0;
      size2D0.height = (-1.0);
      size2D0.hashCode();
      assertEquals(0.0, size2D0.getWidth(), 0.01);
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      Size2D size2D0 = new Size2D();
      assertEquals(0.0, size2D0.height, 0.01);
      
      size2D0.height = (-184.7183);
      Object object0 = size2D0.clone();
      size2D0.height = 1618.169;
      boolean boolean0 = size2D0.equals(object0);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      Size2D size2D0 = new Size2D();
      assertEquals(0.0, size2D0.width, 0.01);
      
      size2D0.width = (-274.0625);
      Object object0 = size2D0.clone();
      boolean boolean0 = size2D0.equals(object0);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      Size2D size2D0 = new Size2D((-1.0), 915.793822405);
      Size2D size2D1 = new Size2D(1.0, (-878.9842047));
      boolean boolean0 = size2D0.equals(size2D1);
      assertEquals((-878.9842047), size2D1.height, 0.01);
      assertEquals(1.0, size2D1.width, 0.01);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      Size2D size2D0 = new Size2D();
      assertEquals(0.0, size2D0.height, 0.01);
      
      size2D0.height = 0.0;
      size2D0.height = (-1.0);
      String string0 = size2D0.toString();
      assertEquals("Size2D[width=0.0, height=-1.0]", string0);
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      Size2D size2D0 = new Size2D(1.0, (-878.9842047));
      double double0 = size2D0.getWidth();
      assertEquals(1.0, double0, 0.01);
      assertEquals((-878.9842047), size2D0.height, 0.01);
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      Size2D size2D0 = new Size2D();
      size2D0.width = (-274.0625);
      double double0 = size2D0.getWidth();
      assertEquals((-274.0625), double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      Size2D size2D0 = new Size2D((-2664.732064), (-2664.732064));
      size2D0.setHeight(245.43268852);
      double double0 = size2D0.getHeight();
      assertEquals(245.43268852, size2D0.height, 0.01);
      assertEquals(245.43268852, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      Size2D size2D0 = new Size2D();
      size2D0.height = 0.0;
      size2D0.height = (-1.0);
      double double0 = size2D0.getHeight();
      assertEquals((-1.0), double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      Size2D size2D0 = new Size2D();
      assertEquals(0.0, size2D0.height, 0.01);
      
      size2D0.height = 0.0;
      size2D0.height = (-1.0);
      Size2D size2D1 = new Size2D();
      boolean boolean0 = size2D0.equals(size2D1);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      Size2D size2D0 = new Size2D();
      Object object0 = size2D0.clone();
      size2D0.setWidth(1.0);
      boolean boolean0 = size2D0.equals(object0);
      assertEquals(1.0, size2D0.width, 0.01);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      Size2D size2D0 = new Size2D();
      Object object0 = new Object();
      boolean boolean0 = size2D0.equals(object0);
      assertEquals(0.0, size2D0.height, 0.01);
      assertEquals(0.0, size2D0.width, 0.01);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      Size2D size2D0 = new Size2D();
      boolean boolean0 = size2D0.equals(size2D0);
      assertEquals(0.0, size2D0.width, 0.01);
      assertEquals(0.0, size2D0.height, 0.01);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      Size2D size2D0 = new Size2D();
      double double0 = size2D0.getWidth();
      assertEquals(0.0, double0, 0.01);
      assertEquals(0.0, size2D0.height, 0.01);
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      Size2D size2D0 = new Size2D();
      double double0 = size2D0.getHeight();
      assertEquals(0.0, size2D0.width, 0.01);
      assertEquals(0.0, double0, 0.01);
  }
}
