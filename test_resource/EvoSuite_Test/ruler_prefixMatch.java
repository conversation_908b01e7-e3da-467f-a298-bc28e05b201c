/*
 * This file was automatically generated by EvoSuite
 * Wed Jun 18 09:04:44 GMT 2025
 */

package software.amazon.event.ruler;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.util.LinkedHashSet;
import java.util.LinkedList;
import java.util.Set;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.junit.runner.RunWith;
import software.amazon.event.ruler.AnythingBut;
import software.amazon.event.ruler.AnythingButValuesSet;
import software.amazon.event.ruler.MatchType;
import software.amazon.event.ruler.Patterns;
import software.amazon.event.ruler.ValuePatterns;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true) 
public class Patterns_ESTest extends Patterns_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      LinkedList<String> linkedList0 = new LinkedList<String>();
      linkedList0.add("A967s.0Kx_*kmla");
      LinkedHashSet<String> linkedHashSet0 = new LinkedHashSet<String>(linkedList0);
      AnythingButValuesSet anythingButValuesSet0 = Patterns.anythingButSuffix((Set<String>) linkedHashSet0);
      assertNull(anythingButValuesSet0.pattern());
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      ValuePatterns valuePatterns0 = Patterns.suffixEqualsIgnoreCaseMatch("software.amazon.event.ruler.Patterns");
      assertEquals("snrettaP.relur.tneve.nozama.erawtfos", valuePatterns0.pattern());
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      ValuePatterns valuePatterns0 = Patterns.prefixEqualsIgnoreCaseMatch("-H9n[\"h");
      String string0 = valuePatterns0.pattern();
      assertEquals("-H9n[\"h", string0);
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      ValuePatterns valuePatterns0 = Patterns.prefixEqualsIgnoreCaseMatch("");
      String string0 = valuePatterns0.pattern();
      assertEquals("", string0);
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      LinkedHashSet<String> linkedHashSet0 = new LinkedHashSet<String>();
      AnythingButValuesSet anythingButValuesSet0 = Patterns.anythingButWildcard((Set<String>) linkedHashSet0);
      assertNull(anythingButValuesSet0.pattern());
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      LinkedHashSet<String> linkedHashSet0 = new LinkedHashSet<String>();
      AnythingBut anythingBut0 = Patterns.anythingButMatch((Set<String>) linkedHashSet0);
      assertNull(anythingBut0.pattern());
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      // Undeclared exception!
      try { 
        Patterns.suffixMatch((String) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
      }
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      // Undeclared exception!
      try { 
        Patterns.anythingButSuffix((String) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
      }
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      // Undeclared exception!
      try { 
        Patterns.anythingButPrefix((Set<String>) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("java.util.Collections$UnmodifiableCollection", e);
      }
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      // Undeclared exception!
      try { 
        Patterns.anythingButNumbersMatch((Set<String>) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("software.amazon.event.ruler.Patterns", e);
      }
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      // Undeclared exception!
      try { 
        Patterns.anythingButNumberMatch((Set<Double>) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("software.amazon.event.ruler.Patterns", e);
      }
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      // Undeclared exception!
      try { 
        Patterns.anythingButIgnoreCaseMatch((Set<String>) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("java.util.Collections$UnmodifiableCollection", e);
      }
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      LinkedHashSet<String> linkedHashSet0 = new LinkedHashSet<String>();
      AnythingBut anythingBut0 = Patterns.anythingButNumbersMatch(linkedHashSet0);
      assertNull(anythingBut0.pattern());
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      LinkedHashSet<String> linkedHashSet0 = new LinkedHashSet<String>();
      linkedHashSet0.add("");
      // Undeclared exception!
      try { 
        Patterns.anythingButNumbersMatch(linkedHashSet0);
        fail("Expecting exception: NoClassDefFoundError");
      
      } catch(NoClassDefFoundError e) {
         //
         // ch/randelshofer/fastdoubleparser/JavaBigDecimalParser
         //
         verifyException("software.amazon.event.ruler.ComparableNumber", e);
      }
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      // Undeclared exception!
      try { 
        Patterns.numericEquals("ANYTHING_BUT");
        fail("Expecting exception: NoClassDefFoundError");
      
      } catch(NoClassDefFoundError e) {
         //
         // ch/randelshofer/fastdoubleparser/JavaBigDecimalParser
         //
         verifyException("software.amazon.event.ruler.ComparableNumber", e);
      }
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      // Undeclared exception!
      try { 
        Patterns.anythingButNumberMatch("N");
        fail("Expecting exception: NoClassDefFoundError");
      
      } catch(NoClassDefFoundError e) {
         //
         // ch/randelshofer/fastdoubleparser/JavaBigDecimalParser
         //
         verifyException("software.amazon.event.ruler.ComparableNumber", e);
      }
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      LinkedHashSet<String> linkedHashSet0 = new LinkedHashSet<String>();
      AnythingButValuesSet anythingButValuesSet0 = Patterns.anythingButPrefix((Set<String>) linkedHashSet0);
      anythingButValuesSet0.hashCode();
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      MatchType matchType0 = MatchType.EXISTS;
      Patterns patterns0 = new Patterns(matchType0);
      boolean boolean0 = patterns0.equals((Object) null);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      MatchType matchType0 = MatchType.EXISTS;
      Patterns patterns0 = new Patterns(matchType0);
      boolean boolean0 = patterns0.equals(patterns0);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      LinkedHashSet<String> linkedHashSet0 = new LinkedHashSet<String>();
      AnythingButValuesSet anythingButValuesSet0 = Patterns.anythingButPrefix((Set<String>) linkedHashSet0);
      AnythingButValuesSet anythingButValuesSet1 = Patterns.anythingButWildcard("");
      boolean boolean0 = anythingButValuesSet0.equals(anythingButValuesSet1);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      LinkedHashSet<Double> linkedHashSet0 = new LinkedHashSet<Double>();
      Double double0 = new Double(0.0);
      linkedHashSet0.add(double0);
      // Undeclared exception!
      try { 
        Patterns.anythingButNumberMatch((Set<Double>) linkedHashSet0);
        fail("Expecting exception: NoClassDefFoundError");
      
      } catch(NoClassDefFoundError e) {
         //
         // ch/randelshofer/fastdoubleparser/JavaBigDecimalParser
         //
         verifyException("software.amazon.event.ruler.ComparableNumber", e);
      }
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      AnythingButValuesSet anythingButValuesSet0 = Patterns.anythingButIgnoreCaseMatch("Z>M[krQ)OJ'`wcL");
      Object object0 = anythingButValuesSet0.clone();
      boolean boolean0 = anythingButValuesSet0.equals(object0);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test22()  throws Throwable  {
      // Undeclared exception!
      try { 
        Patterns.suffixEqualsIgnoreCaseMatch((String) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
      }
  }

  @Test(timeout = 4000)
  public void test23()  throws Throwable  {
      // Undeclared exception!
      try { 
        Patterns.anythingButMatch((Set<String>) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("java.util.Collections$UnmodifiableCollection", e);
      }
  }

  @Test(timeout = 4000)
  public void test24()  throws Throwable  {
      LinkedHashSet<Double> linkedHashSet0 = new LinkedHashSet<Double>();
      AnythingBut anythingBut0 = Patterns.anythingButNumberMatch((Set<Double>) linkedHashSet0);
      assertEquals(MatchType.ANYTHING_BUT, anythingBut0.type());
  }

  @Test(timeout = 4000)
  public void test25()  throws Throwable  {
      ValuePatterns valuePatterns0 = Patterns.exactMatch("N");
      assertEquals(MatchType.EXACT, valuePatterns0.type());
  }

  @Test(timeout = 4000)
  public void test26()  throws Throwable  {
      LinkedHashSet<String> linkedHashSet0 = new LinkedHashSet<String>();
      AnythingButValuesSet anythingButValuesSet0 = Patterns.anythingButIgnoreCaseMatch((Set<String>) linkedHashSet0);
      assertNull(anythingButValuesSet0.pattern());
  }

  @Test(timeout = 4000)
  public void test27()  throws Throwable  {
      AnythingBut anythingBut0 = Patterns.anythingButMatch("");
      String string0 = anythingBut0.pattern();
      assertNull(string0);
  }

  @Test(timeout = 4000)
  public void test28()  throws Throwable  {
      ValuePatterns valuePatterns0 = Patterns.wildcardMatch("N");
      assertEquals(MatchType.WILDCARD, valuePatterns0.type());
  }

  @Test(timeout = 4000)
  public void test29()  throws Throwable  {
      ValuePatterns valuePatterns0 = Patterns.suffixMatch("");
      assertEquals("", valuePatterns0.pattern());
  }

  @Test(timeout = 4000)
  public void test30()  throws Throwable  {
      ValuePatterns valuePatterns0 = Patterns.prefixMatch("N");
      assertEquals(MatchType.PREFIX, valuePatterns0.type());
  }

  @Test(timeout = 4000)
  public void test31()  throws Throwable  {
      LinkedHashSet<String> linkedHashSet0 = new LinkedHashSet<String>();
      AnythingButValuesSet anythingButValuesSet0 = Patterns.anythingButPrefix((Set<String>) linkedHashSet0);
      String string0 = anythingButValuesSet0.toString();
      assertEquals("ABVS:[], (T:ANYTHING_BUT_PREFIX)", string0);
  }

  @Test(timeout = 4000)
  public void test32()  throws Throwable  {
      // Undeclared exception!
      try { 
        Patterns.anythingButMatch((-1.0));
        fail("Expecting exception: NoClassDefFoundError");
      
      } catch(NoClassDefFoundError e) {
         //
         // ch/randelshofer/fastdoubleparser/JavaBigDecimalParser
         //
         verifyException("software.amazon.event.ruler.ComparableNumber", e);
      }
  }

  @Test(timeout = 4000)
  public void test33()  throws Throwable  {
      // Undeclared exception!
      try { 
        Patterns.numericEquals((-1.0));
        fail("Expecting exception: NoClassDefFoundError");
      
      } catch(NoClassDefFoundError e) {
         //
         // ch/randelshofer/fastdoubleparser/JavaBigDecimalParser
         //
         verifyException("software.amazon.event.ruler.ComparableNumber", e);
      }
  }

  @Test(timeout = 4000)
  public void test34()  throws Throwable  {
      AnythingButValuesSet anythingButValuesSet0 = Patterns.anythingButSuffix("N");
      assertNull(anythingButValuesSet0.pattern());
  }

  @Test(timeout = 4000)
  public void test35()  throws Throwable  {
      AnythingButValuesSet anythingButValuesSet0 = Patterns.anythingButIgnoreCaseMatch("Z>M[krQ)OJ'`wcL");
      MatchType matchType0 = anythingButValuesSet0.type();
      assertEquals(MatchType.ANYTHING_BUT_IGNORE_CASE, matchType0);
  }

  @Test(timeout = 4000)
  public void test36()  throws Throwable  {
      ValuePatterns valuePatterns0 = Patterns.equalsIgnoreCaseMatch("N");
      assertEquals(MatchType.EQUALS_IGNORE_CASE, valuePatterns0.type());
  }

  @Test(timeout = 4000)
  public void test37()  throws Throwable  {
      // Undeclared exception!
      try { 
        Patterns.anythingButSuffix((Set<String>) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("software.amazon.event.ruler.Patterns", e);
      }
  }

  @Test(timeout = 4000)
  public void test38()  throws Throwable  {
      AnythingButValuesSet anythingButValuesSet0 = Patterns.anythingButPrefix("`");
      assertEquals(MatchType.ANYTHING_BUT_PREFIX, anythingButValuesSet0.type());
  }

  @Test(timeout = 4000)
  public void test39()  throws Throwable  {
      Patterns patterns0 = Patterns.existencePatterns();
      Object object0 = new Object();
      boolean boolean0 = patterns0.equals(object0);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test40()  throws Throwable  {
      Patterns patterns0 = Patterns.absencePatterns();
      assertEquals(MatchType.ABSENT, patterns0.type());
  }

  @Test(timeout = 4000)
  public void test41()  throws Throwable  {
      // Undeclared exception!
      try { 
        Patterns.anythingButWildcard((Set<String>) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("java.util.Collections$UnmodifiableCollection", e);
      }
  }
}
