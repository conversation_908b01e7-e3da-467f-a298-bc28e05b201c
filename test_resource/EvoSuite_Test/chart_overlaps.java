/*
 * This file was automatically generated by EvoSuite
 * Wed Jun 18 07:27:34 GMT 2025
 */

package org.jfree.chart.renderer;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.awt.geom.Point2D;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.jfree.chart.renderer.Outlier;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true) 
public class Outlier_ESTest extends Outlier_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      Outlier outlier0 = new Outlier(590.7424795741315, 590.7424795741315, 0.0);
      assertEquals(0.0, outlier0.getRadius(), 0.01);
      
      outlier0.setRadius(590.7424795741315);
      Outlier outlier1 = new Outlier(590.7424795741315, 590.7424795741315, 0.0);
      boolean boolean0 = outlier0.equals(outlier1);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      Outlier outlier0 = new Outlier(0.0, (-1.0), 1.1);
      boolean boolean0 = outlier0.overlaps(outlier0);
      assertEquals(1.1, outlier0.getRadius(), 0.01);
      assertTrue(boolean0);
      assertEquals((-2.1), outlier0.getY(), 0.01);
      assertEquals((-1.1), outlier0.getX(), 0.01);
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      Outlier outlier0 = new Outlier(125.0, 0.0, 0.0);
      Outlier outlier1 = new Outlier(0.0, 0.0, 0.0);
      int int0 = outlier0.compareTo(outlier1);
      assertEquals(0.0, outlier0.getY(), 0.01);
      assertEquals(0.0, outlier0.getRadius(), 0.01);
      assertEquals(0.0, outlier1.getX(), 0.01);
      assertEquals(1, int0);
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      Outlier outlier0 = new Outlier(0.0, (-1.0), 1.1);
      assertEquals((-1.1), outlier0.getX(), 0.01);
      
      Point2D.Float point2D_Float0 = new Point2D.Float(0.0F, 0.0F);
      point2D_Float0.setLocation(0.0, 773.73);
      outlier0.setPoint(point2D_Float0);
      outlier0.getY();
      assertEquals(1.1, outlier0.getRadius(), 0.01);
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      Outlier outlier0 = new Outlier(0.0, (-1.0), 1.1);
      double double0 = outlier0.getY();
      assertEquals((-1.1), outlier0.getX(), 0.01);
      assertEquals((-2.1), double0, 0.01);
      assertEquals(1.1, outlier0.getRadius(), 0.01);
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      Outlier outlier0 = new Outlier(0.0, (-1.0), 1.1);
      assertEquals((-1.1), outlier0.getX(), 0.01);
      
      Point2D.Float point2D_Float0 = new Point2D.Float(0.0F, 0.0F);
      outlier0.setPoint(point2D_Float0);
      outlier0.getX();
      assertEquals(1.1, outlier0.getRadius(), 0.01);
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      Outlier outlier0 = new Outlier(1076.35, (-3810.230835613), (-1.0));
      double double0 = outlier0.getX();
      assertEquals((-1.0), outlier0.getRadius(), 0.01);
      assertEquals(1077.35, double0, 0.01);
      assertEquals((-3809.230835613), outlier0.getY(), 0.01);
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      Outlier outlier0 = new Outlier(0.0, 0.0, 0.0);
      double double0 = outlier0.getRadius();
      assertEquals(0.0, double0, 0.01);
      assertEquals(0.0, outlier0.getX(), 0.01);
      assertEquals(0.0, outlier0.getY(), 0.01);
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      Outlier outlier0 = new Outlier(1302.305, 1302.305, 1.0);
      double double0 = outlier0.getRadius();
      assertEquals(1.0, double0, 0.01);
      assertEquals(1301.305, outlier0.getX(), 0.01);
      assertEquals(1301.305, outlier0.getY(), 0.01);
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      Outlier outlier0 = new Outlier(0.0, 0.0, 0.0);
      assertEquals(0.0, outlier0.getX(), 0.01);
      assertEquals(0.0, outlier0.getY(), 0.01);
      
      outlier0.setPoint((Point2D) null);
      outlier0.getPoint();
      assertEquals(0.0, outlier0.getRadius(), 0.01);
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      Outlier outlier0 = new Outlier(0.0, (-725.830761244068), (-1.0));
      // Undeclared exception!
      try { 
        outlier0.overlaps((Outlier) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.jfree.chart.renderer.Outlier", e);
      }
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      Outlier outlier0 = new Outlier(590.7424795741315, 590.7424795741315, 590.7424795741315);
      outlier0.setPoint((Point2D) null);
      Outlier outlier1 = new Outlier(590.7424795741315, 590.7424795741315, 0.0);
      // Undeclared exception!
      try { 
        outlier0.equals(outlier1);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.jfree.chart.renderer.Outlier", e);
      }
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      Outlier outlier0 = new Outlier((-3574.70975), (-1.0), (-3574.70975));
      // Undeclared exception!
      try { 
        outlier0.compareTo((Object) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.jfree.chart.renderer.Outlier", e);
      }
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      Outlier outlier0 = new Outlier(0.0, 2579.72441, (-2515.322927));
      Object object0 = new Object();
      // Undeclared exception!
      try { 
        outlier0.compareTo(object0);
        fail("Expecting exception: ClassCastException");
      
      } catch(ClassCastException e) {
         //
         // java.lang.Object cannot be cast to org.jfree.chart.renderer.Outlier
         //
         verifyException("org.jfree.chart.renderer.Outlier", e);
      }
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      Outlier outlier0 = new Outlier(0.0, (-1.0), 1.1);
      double double0 = outlier0.getX();
      assertEquals(1.1, outlier0.getRadius(), 0.01);
      assertEquals((-2.1), outlier0.getY(), 0.01);
      assertEquals((-1.1), double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      Outlier outlier0 = new Outlier((-2598.0036838868687), (-2598.0036838868687), (-2598.0036838868687));
      double double0 = outlier0.getY();
      assertEquals(0.0, double0, 0.01);
      assertEquals(0.0, outlier0.getX(), 0.01);
      assertEquals((-2598.0036838868687), outlier0.getRadius(), 0.01);
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      Outlier outlier0 = new Outlier((-4913.979176135302), (-4913.979176135302), (-4913.979176135302));
      Point2D point2D0 = outlier0.getPoint();
      Outlier outlier1 = new Outlier((-4913.979176135302), (-4913.979176135302), (-1.0));
      assertEquals((-4912.979176135302), outlier1.getY(), 0.01);
      
      outlier1.setPoint(point2D0);
      boolean boolean0 = outlier0.equals(outlier1);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      Outlier outlier0 = new Outlier(590.7424795741315, 590.7424795741315, 0.0);
      Outlier outlier1 = new Outlier(590.7424795741315, 590.7424795741315, 0.0);
      boolean boolean0 = outlier0.equals(outlier1);
      assertTrue(boolean0);
      assertEquals(0.0, outlier1.getRadius(), 0.01);
      assertEquals(590.7424795741315, outlier1.getY(), 0.01);
      assertEquals(590.7424795741315, outlier1.getX(), 0.01);
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      Outlier outlier0 = new Outlier((-3574.70975), (-1.0), (-3574.70975));
      Outlier outlier1 = new Outlier(0.0, 2349.847396, 2349.847396);
      boolean boolean0 = outlier1.equals(outlier0);
      assertEquals((-2349.847396), outlier1.getX(), 0.01);
      assertEquals(3573.70975, outlier0.getY(), 0.01);
      assertEquals(2349.847396, outlier1.getRadius(), 0.01);
      assertFalse(boolean0);
      assertEquals((-3574.70975), outlier0.getRadius(), 0.01);
      assertEquals(0.0, outlier1.getY(), 0.01);
      assertEquals(0.0, outlier0.getX(), 0.01);
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      Outlier outlier0 = new Outlier((-3574.70975), (-3574.70975), (-3574.70975));
      boolean boolean0 = outlier0.equals(outlier0);
      assertEquals((-3574.70975), outlier0.getRadius(), 0.01);
      assertEquals(0.0, outlier0.getY(), 0.01);
      assertEquals(0.0, outlier0.getX(), 0.01);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      Outlier outlier0 = new Outlier((-3574.70975), (-1.0), (-3574.70975));
      boolean boolean0 = outlier0.equals("{0.0,3573.70975}");
      assertEquals(3573.70975, outlier0.getY(), 0.01);
      assertFalse(boolean0);
      assertEquals((-3574.70975), outlier0.getRadius(), 0.01);
      assertEquals(0.0, outlier0.getX(), 0.01);
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      Outlier outlier0 = new Outlier(0.0, 0.0, 0.0);
      Outlier outlier1 = new Outlier(0.0, 685.15715527, 0.0);
      boolean boolean0 = outlier0.overlaps(outlier1);
      assertEquals(0.0, outlier1.getX(), 0.01);
      assertFalse(boolean0);
      assertEquals(685.15715527, outlier1.getY(), 0.01);
      assertEquals(0.0, outlier1.getRadius(), 0.01);
  }

  @Test(timeout = 4000)
  public void test22()  throws Throwable  {
      Outlier outlier0 = new Outlier((-2598.0036838868687), (-2598.0036838868687), (-2598.0036838868687));
      Outlier outlier1 = new Outlier(0.0, 2977.2866134, 0.0);
      boolean boolean0 = outlier1.overlaps(outlier0);
      assertEquals((-2598.0036838868687), outlier0.getRadius(), 0.01);
      assertFalse(boolean0);
      assertEquals(0.0, outlier1.getX(), 0.01);
      assertEquals(0.0, outlier0.getY(), 0.01);
      assertEquals(0.0, outlier0.getX(), 0.01);
  }

  @Test(timeout = 4000)
  public void test23()  throws Throwable  {
      Outlier outlier0 = new Outlier((-873.084336235368), 0.0, 0.0);
      Outlier outlier1 = new Outlier(0.0, (-873.084336235368), 0.0);
      boolean boolean0 = outlier0.overlaps(outlier1);
      assertEquals(0.0, outlier1.getX(), 0.01);
      assertEquals(0.0, outlier1.getRadius(), 0.01);
      assertEquals(0.0, outlier0.getY(), 0.01);
      assertEquals((-873.084336235368), outlier1.getY(), 0.01);
      assertEquals((-873.084336235368), outlier0.getX(), 0.01);
      assertFalse(boolean0);
      assertEquals(0.0, outlier0.getRadius(), 0.01);
  }

  @Test(timeout = 4000)
  public void test24()  throws Throwable  {
      Outlier outlier0 = new Outlier(590.7424795741315, 590.7424795741315, 0.0);
      boolean boolean0 = outlier0.overlaps(outlier0);
      assertEquals(0.0, outlier0.getRadius(), 0.01);
      assertEquals(590.7424795741315, outlier0.getX(), 0.01);
      assertEquals(590.7424795741315, outlier0.getY(), 0.01);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test25()  throws Throwable  {
      Outlier outlier0 = new Outlier((-3574.70975), (-1.0), (-3574.70975));
      boolean boolean0 = outlier0.overlaps(outlier0);
      assertEquals(3573.70975, outlier0.getY(), 0.01);
      assertEquals((-3574.70975), outlier0.getRadius(), 0.01);
      assertEquals(0.0, outlier0.getX(), 0.01);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test26()  throws Throwable  {
      Outlier outlier0 = new Outlier(0.0, 0.0, 465.48);
      Outlier outlier1 = new Outlier((-196.9269271), (-196.9269271), (-196.9269271));
      assertEquals(0.0, outlier1.getY(), 0.01);
      
      Point2D.Double point2D_Double0 = new Point2D.Double((-2974.77860681539), 0.0);
      point2D_Double0.y = (-2974.77860681539);
      outlier1.setPoint(point2D_Double0);
      int int0 = outlier0.compareTo(outlier1);
      assertEquals(1, int0);
  }

  @Test(timeout = 4000)
  public void test27()  throws Throwable  {
      Outlier outlier0 = new Outlier((-2598.0036838868687), (-2598.0036838868687), (-2598.0036838868687));
      Outlier outlier1 = new Outlier(0.0, 2977.2866134, 0.0);
      int int0 = outlier0.compareTo(outlier1);
      assertEquals(0.0, outlier0.getX(), 0.01);
      assertEquals(0.0, outlier0.getY(), 0.01);
      assertEquals(0.0, outlier1.getRadius(), 0.01);
      assertEquals((-1), int0);
      assertEquals(0.0, outlier1.getX(), 0.01);
  }

  @Test(timeout = 4000)
  public void test28()  throws Throwable  {
      Outlier outlier0 = new Outlier(0.0, 2579.72441, (-2515.322927));
      int int0 = outlier0.compareTo(outlier0);
      assertEquals((-2515.322927), outlier0.getRadius(), 0.01);
      assertEquals(2515.322927, outlier0.getX(), 0.01);
      assertEquals(0, int0);
      assertEquals(5095.047337, outlier0.getY(), 0.01);
  }

  @Test(timeout = 4000)
  public void test29()  throws Throwable  {
      Outlier outlier0 = new Outlier((-3574.70975), (-3810.230835613), (-3574.70975));
      Outlier outlier1 = new Outlier(1076.35, (-3810.230835613), (-1.0));
      int int0 = outlier0.compareTo(outlier1);
      assertEquals((-3809.230835613), outlier1.getY(), 0.01);
      assertEquals(0.0, outlier0.getX(), 0.01);
      assertEquals((-1.0), outlier1.getRadius(), 0.01);
      assertEquals((-1), int0);
  }

  @Test(timeout = 4000)
  public void test30()  throws Throwable  {
      Outlier outlier0 = new Outlier(487.0, 0.8552460775994499, 0.0);
      outlier0.setPoint((Point2D) null);
      // Undeclared exception!
      try { 
        outlier0.toString();
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test31()  throws Throwable  {
      Outlier outlier0 = new Outlier((-3574.70975), (-3810.230835613), (-3574.70975));
      double double0 = outlier0.getRadius();
      assertEquals((-3574.70975), double0, 0.01);
      assertEquals((-235.5210856130002), outlier0.getY(), 0.01);
      assertEquals(0.0, outlier0.getX(), 0.01);
  }

  @Test(timeout = 4000)
  public void test32()  throws Throwable  {
      Outlier outlier0 = new Outlier((-3574.70975), (-3574.70975), (-3574.70975));
      String string0 = outlier0.toString();
      assertEquals("{0.0,0.0}", string0);
      assertEquals((-3574.70975), outlier0.getRadius(), 0.01);
  }
}
