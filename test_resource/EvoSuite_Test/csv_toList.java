/*
 * This file was automatically generated by EvoSuite
 * Wed Jun 18 08:33:26 GMT 2025
 */

package org.apache.commons.csv;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.io.StringReader;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true) 
public class CSVRecord_ESTest extends CSVRecord_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test0()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVParser cSVParser0 = CSVParser.parse("[&in#as", cSVFormat0);
      String[] stringArray0 = new String[5];
      CSVRecord cSVRecord0 = new CSVRecord(cSVParser0, stringArray0, "8,%BW.`W|Rg", 0L, 0L);
      cSVRecord0.stream();
      assertEquals(5, cSVRecord0.size());
  }

  @Test(timeout = 4000)
  public void test1()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      StringReader stringReader0 = new StringReader("LN=");
      CSVParser cSVParser0 = cSVFormat0.parse(stringReader0);
      String[] stringArray0 = new String[0];
      CSVRecord cSVRecord0 = new CSVRecord(cSVParser0, stringArray0, "o(", (-1L), (-1L));
      cSVRecord0.toList();
      assertEquals((-1L), cSVRecord0.getRecordNumber());
      assertEquals((-1L), cSVRecord0.getCharacterPosition());
  }

  @Test(timeout = 4000)
  public void test2()  throws Throwable  {
      String[] stringArray0 = new String[1];
      CSVRecord cSVRecord0 = new CSVRecord((CSVParser) null, stringArray0, "org.apache.commons.csv.Token", 0L, (-3263L));
      // Undeclared exception!
      try { 
        cSVRecord0.get("");
        fail("Expecting exception: IllegalStateException");
      
      } catch(IllegalStateException e) {
         //
         // No header mapping was specified, the record values can't be accessed by name
         //
         verifyException("org.apache.commons.csv.CSVRecord", e);
      }
  }

  @Test(timeout = 4000)
  public void test3()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      StringReader stringReader0 = new StringReader("LN=");
      CSVParser cSVParser0 = cSVFormat0.parse(stringReader0);
      String[] stringArray0 = new String[0];
      CSVRecord cSVRecord0 = new CSVRecord(cSVParser0, stringArray0, "o(", (-1L), (-1L));
      // Undeclared exception!
      try { 
        cSVRecord0.get(0);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 0
         //
         verifyException("org.apache.commons.csv.CSVRecord", e);
      }
  }

  @Test(timeout = 4000)
  public void test4()  throws Throwable  {
      StringReader stringReader0 = new StringReader("APE]47]gO5`@a&.");
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVParser cSVParser0 = new CSVParser(stringReader0, cSVFormat0, (-1240L), (-1240L));
      CSVRecord cSVRecord0 = cSVParser0.nextRecord();
      cSVRecord0.toMap();
      assertEquals((-1240L), cSVRecord0.getCharacterPosition());
      assertEquals(1, cSVRecord0.size());
      assertEquals((-1240L), cSVRecord0.getRecordNumber());
  }

  @Test(timeout = 4000)
  public void test5()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      StringReader stringReader0 = new StringReader("LN=");
      CSVParser cSVParser0 = cSVFormat0.parse(stringReader0);
      String[] stringArray0 = new String[0];
      CSVRecord cSVRecord0 = new CSVRecord(cSVParser0, stringArray0, "o(", (-1L), (-1L));
      cSVRecord0.spliterator();
      assertEquals((-1L), cSVRecord0.getCharacterPosition());
      assertEquals((-1L), cSVRecord0.getRecordNumber());
  }
}
