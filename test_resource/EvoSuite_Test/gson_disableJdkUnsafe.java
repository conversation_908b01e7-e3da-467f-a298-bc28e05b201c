/*
 * This file was automatically generated by EvoSuite
 * Wed Jun 18 08:46:39 GMT 2025
 */

package com.google.gson;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.shaded.org.mockito.Mockito.*;
import static org.evosuite.runtime.EvoAssertions.*;
import com.google.gson.ExclusionStrategy;
import com.google.gson.FieldNamingPolicy;
import com.google.gson.FieldNamingStrategy;
import com.google.gson.FormattingStyle;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.LongSerializationPolicy;
import com.google.gson.ReflectionAccessFilter;
import com.google.gson.Strictness;
import com.google.gson.ToNumberStrategy;
import com.google.gson.TypeAdapterFactory;
import com.google.gson.internal.Excluder;
import java.lang.reflect.Type;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.evosuite.runtime.ViolatedAssumptionAnswer;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true) 
public class GsonBuilder_ESTest extends GsonBuilder_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      GsonBuilder gsonBuilder0 = new GsonBuilder();
      gsonBuilder0.setDateFormat(3, 3);
      Gson gson0 = gsonBuilder0.create();
      assertTrue(gson0.htmlSafe());
      assertFalse(gson0.serializeNulls());
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      GsonBuilder gsonBuilder0 = new GsonBuilder();
      gsonBuilder0.setDateFormat(0, 1);
      Gson gson0 = gsonBuilder0.create();
      assertTrue(gson0.htmlSafe());
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      GsonBuilder gsonBuilder0 = new GsonBuilder();
      GsonBuilder gsonBuilder1 = gsonBuilder0.setVersion(0.0);
      assertSame(gsonBuilder0, gsonBuilder1);
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      GsonBuilder gsonBuilder0 = new GsonBuilder();
      FormattingStyle formattingStyle0 = Gson.DEFAULT_FORMATTING_STYLE;
      GsonBuilder gsonBuilder1 = gsonBuilder0.setFormattingStyle(formattingStyle0);
      assertSame(gsonBuilder0, gsonBuilder1);
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      GsonBuilder gsonBuilder0 = new GsonBuilder();
      ReflectionAccessFilter reflectionAccessFilter0 = mock(ReflectionAccessFilter.class, new ViolatedAssumptionAnswer());
      GsonBuilder gsonBuilder1 = gsonBuilder0.addReflectionAccessFilter(reflectionAccessFilter0);
      assertSame(gsonBuilder0, gsonBuilder1);
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      GsonBuilder gsonBuilder0 = new GsonBuilder();
      // Undeclared exception!
      try { 
        gsonBuilder0.setObjectToNumberStrategy((ToNumberStrategy) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("java.util.Objects", e);
      }
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      GsonBuilder gsonBuilder0 = new GsonBuilder();
      // Undeclared exception!
      try { 
        gsonBuilder0.setNumberToNumberStrategy((ToNumberStrategy) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("java.util.Objects", e);
      }
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      GsonBuilder gsonBuilder0 = new GsonBuilder();
      // Undeclared exception!
      try { 
        gsonBuilder0.setFieldNamingStrategy((FieldNamingStrategy) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("java.util.Objects", e);
      }
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      GsonBuilder gsonBuilder0 = new GsonBuilder();
      // Undeclared exception!
      try { 
        gsonBuilder0.setExclusionStrategies((ExclusionStrategy[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("java.util.Objects", e);
      }
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      GsonBuilder gsonBuilder0 = new GsonBuilder();
      // Undeclared exception!
      try { 
        gsonBuilder0.setDateFormat("g;}O{!2e_&;f;");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The date pattern 'g;}O{!2e_&;f;' is not valid
         //
         verifyException("com.google.gson.GsonBuilder", e);
      }
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      GsonBuilder gsonBuilder0 = new GsonBuilder();
      // Undeclared exception!
      try { 
        gsonBuilder0.setDateFormat(17, 17);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Invalid style: 17
         //
         verifyException("com.google.gson.GsonBuilder", e);
      }
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      Gson gson0 = new Gson();
      GsonBuilder gsonBuilder0 = new GsonBuilder(gson0);
      // Undeclared exception!
      try { 
        gsonBuilder0.registerTypeAdapterFactory((TypeAdapterFactory) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("java.util.Objects", e);
      }
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      GsonBuilder gsonBuilder0 = new GsonBuilder();
      // Undeclared exception!
      try { 
        gsonBuilder0.registerTypeAdapter((Type) null, (Object) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("java.util.Objects", e);
      }
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      GsonBuilder gsonBuilder0 = new GsonBuilder();
      // Undeclared exception!
      try { 
        gsonBuilder0.excludeFieldsWithModifiers((int[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("java.util.Objects", e);
      }
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      Gson gson0 = new Gson();
      GsonBuilder gsonBuilder0 = new GsonBuilder(gson0);
      // Undeclared exception!
      try { 
        gsonBuilder0.addSerializationExclusionStrategy((ExclusionStrategy) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("java.util.Objects", e);
      }
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      Gson gson0 = new Gson();
      GsonBuilder gsonBuilder0 = gson0.newBuilder();
      // Undeclared exception!
      try { 
        gsonBuilder0.addDeserializationExclusionStrategy((ExclusionStrategy) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("java.util.Objects", e);
      }
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      GsonBuilder gsonBuilder0 = null;
      try {
        gsonBuilder0 = new GsonBuilder((Gson) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("com.google.gson.GsonBuilder", e);
      }
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      GsonBuilder gsonBuilder0 = new GsonBuilder();
      FieldNamingPolicy fieldNamingPolicy0 = FieldNamingPolicy.UPPER_CAMEL_CASE_WITH_SPACES;
      GsonBuilder gsonBuilder1 = gsonBuilder0.setFieldNamingStrategy(fieldNamingPolicy0);
      assertSame(gsonBuilder1, gsonBuilder0);
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      GsonBuilder gsonBuilder0 = new GsonBuilder();
      // Undeclared exception!
      try { 
        gsonBuilder0.setFormattingStyle((FormattingStyle) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("java.util.Objects", e);
      }
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      Gson gson0 = new Gson();
      GsonBuilder gsonBuilder0 = new GsonBuilder(gson0);
      Strictness strictness0 = Strictness.STRICT;
      GsonBuilder gsonBuilder1 = gsonBuilder0.setStrictness(strictness0);
      assertSame(gsonBuilder1, gsonBuilder0);
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      GsonBuilder gsonBuilder0 = new GsonBuilder();
      Class<Type> class0 = Type.class;
      // Undeclared exception!
      try { 
        gsonBuilder0.registerTypeHierarchyAdapter(class0, class0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("com.google.gson.internal.$Gson$Preconditions", e);
      }
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      GsonBuilder gsonBuilder0 = new GsonBuilder();
      Class<Long> class0 = Long.TYPE;
      // Undeclared exception!
      try { 
        gsonBuilder0.registerTypeAdapter(class0, class0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("com.google.gson.internal.$Gson$Preconditions", e);
      }
  }

  @Test(timeout = 4000)
  public void test22()  throws Throwable  {
      GsonBuilder gsonBuilder0 = new GsonBuilder();
      // Undeclared exception!
      try { 
        gsonBuilder0.setDateFormat(57);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Invalid style: 57
         //
         verifyException("com.google.gson.GsonBuilder", e);
      }
  }

  @Test(timeout = 4000)
  public void test23()  throws Throwable  {
      GsonBuilder gsonBuilder0 = new GsonBuilder();
      // Undeclared exception!
      try { 
        gsonBuilder0.setDateFormat((-1861));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Invalid style: -1861
         //
         verifyException("com.google.gson.GsonBuilder", e);
      }
  }

  @Test(timeout = 4000)
  public void test24()  throws Throwable  {
      GsonBuilder gsonBuilder0 = new GsonBuilder();
      GsonBuilder gsonBuilder1 = gsonBuilder0.setDateFormat("*");
      Gson gson0 = gsonBuilder1.create();
      assertTrue(gson0.htmlSafe());
      assertFalse(gson0.serializeNulls());
  }

  @Test(timeout = 4000)
  public void test25()  throws Throwable  {
      GsonBuilder gsonBuilder0 = new GsonBuilder();
      GsonBuilder gsonBuilder1 = gsonBuilder0.setDateFormat((String) null);
      assertSame(gsonBuilder1, gsonBuilder0);
  }

  @Test(timeout = 4000)
  public void test26()  throws Throwable  {
      GsonBuilder gsonBuilder0 = new GsonBuilder();
      ExclusionStrategy[] exclusionStrategyArray0 = new ExclusionStrategy[6];
      GsonBuilder gsonBuilder1 = gsonBuilder0.setExclusionStrategies(exclusionStrategyArray0);
      assertSame(gsonBuilder1, gsonBuilder0);
  }

  @Test(timeout = 4000)
  public void test27()  throws Throwable  {
      GsonBuilder gsonBuilder0 = new GsonBuilder();
      // Undeclared exception!
      try { 
        gsonBuilder0.setVersion((-3016.993072757724));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Invalid version: -3016.993072757724
         //
         verifyException("com.google.gson.GsonBuilder", e);
      }
  }

  @Test(timeout = 4000)
  public void test28()  throws Throwable  {
      GsonBuilder gsonBuilder0 = new GsonBuilder();
      GsonBuilder gsonBuilder1 = gsonBuilder0.setVersion(4391.804);
      assertSame(gsonBuilder0, gsonBuilder1);
  }

  @Test(timeout = 4000)
  public void test29()  throws Throwable  {
      GsonBuilder gsonBuilder0 = new GsonBuilder();
      LongSerializationPolicy longSerializationPolicy0 = LongSerializationPolicy.DEFAULT;
      GsonBuilder gsonBuilder1 = gsonBuilder0.setLongSerializationPolicy(longSerializationPolicy0);
      assertSame(gsonBuilder0, gsonBuilder1);
  }

  @Test(timeout = 4000)
  public void test30()  throws Throwable  {
      GsonBuilder gsonBuilder0 = new GsonBuilder();
      GsonBuilder gsonBuilder1 = gsonBuilder0.disableInnerClassSerialization();
      assertSame(gsonBuilder1, gsonBuilder0);
  }

  @Test(timeout = 4000)
  public void test31()  throws Throwable  {
      GsonBuilder gsonBuilder0 = new GsonBuilder();
      GsonBuilder gsonBuilder1 = gsonBuilder0.disableHtmlEscaping();
      Gson gson0 = gsonBuilder1.create();
      assertFalse(gson0.serializeNulls());
      assertFalse(gson0.htmlSafe());
  }

  @Test(timeout = 4000)
  public void test32()  throws Throwable  {
      GsonBuilder gsonBuilder0 = new GsonBuilder();
      ExclusionStrategy exclusionStrategy0 = mock(ExclusionStrategy.class, new ViolatedAssumptionAnswer());
      GsonBuilder gsonBuilder1 = gsonBuilder0.addSerializationExclusionStrategy(exclusionStrategy0);
      assertSame(gsonBuilder0, gsonBuilder1);
  }

  @Test(timeout = 4000)
  public void test33()  throws Throwable  {
      GsonBuilder gsonBuilder0 = new GsonBuilder();
      FieldNamingPolicy fieldNamingPolicy0 = FieldNamingPolicy.LOWER_CASE_WITH_UNDERSCORES;
      GsonBuilder gsonBuilder1 = gsonBuilder0.setFieldNamingPolicy(fieldNamingPolicy0);
      assertSame(gsonBuilder1, gsonBuilder0);
  }

  @Test(timeout = 4000)
  public void test34()  throws Throwable  {
      Gson gson0 = new Gson();
      GsonBuilder gsonBuilder0 = new GsonBuilder(gson0);
      int[] intArray0 = new int[6];
      GsonBuilder gsonBuilder1 = gsonBuilder0.excludeFieldsWithModifiers(intArray0);
      assertSame(gsonBuilder0, gsonBuilder1);
  }

  @Test(timeout = 4000)
  public void test35()  throws Throwable  {
      GsonBuilder gsonBuilder0 = new GsonBuilder();
      // Undeclared exception!
      try { 
        gsonBuilder0.addReflectionAccessFilter((ReflectionAccessFilter) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("java.util.Objects", e);
      }
  }

  @Test(timeout = 4000)
  public void test36()  throws Throwable  {
      GsonBuilder gsonBuilder0 = new GsonBuilder();
      GsonBuilder gsonBuilder1 = gsonBuilder0.setDateFormat(0);
      assertSame(gsonBuilder0, gsonBuilder1);
  }

  @Test(timeout = 4000)
  public void test37()  throws Throwable  {
      Gson gson0 = new Gson();
      GsonBuilder gsonBuilder0 = new GsonBuilder(gson0);
      ExclusionStrategy exclusionStrategy0 = mock(ExclusionStrategy.class, new ViolatedAssumptionAnswer());
      GsonBuilder gsonBuilder1 = gsonBuilder0.addDeserializationExclusionStrategy(exclusionStrategy0);
      assertSame(gsonBuilder0, gsonBuilder1);
  }

  @Test(timeout = 4000)
  public void test38()  throws Throwable  {
      GsonBuilder gsonBuilder0 = new GsonBuilder();
      ToNumberStrategy toNumberStrategy0 = Gson.DEFAULT_NUMBER_TO_NUMBER_STRATEGY;
      GsonBuilder gsonBuilder1 = gsonBuilder0.setObjectToNumberStrategy(toNumberStrategy0);
      assertSame(gsonBuilder0, gsonBuilder1);
  }

  @Test(timeout = 4000)
  public void test39()  throws Throwable  {
      Gson gson0 = new Gson();
      GsonBuilder gsonBuilder0 = new GsonBuilder(gson0);
      ToNumberStrategy toNumberStrategy0 = Gson.DEFAULT_OBJECT_TO_NUMBER_STRATEGY;
      GsonBuilder gsonBuilder1 = gsonBuilder0.setNumberToNumberStrategy(toNumberStrategy0);
      assertSame(gsonBuilder0, gsonBuilder1);
  }

  @Test(timeout = 4000)
  public void test40()  throws Throwable  {
      GsonBuilder gsonBuilder0 = new GsonBuilder();
      GsonBuilder gsonBuilder1 = gsonBuilder0.generateNonExecutableJson();
      assertSame(gsonBuilder0, gsonBuilder1);
  }

  @Test(timeout = 4000)
  public void test41()  throws Throwable  {
      GsonBuilder gsonBuilder0 = new GsonBuilder();
      Excluder excluder0 = new Excluder();
      GsonBuilder gsonBuilder1 = gsonBuilder0.registerTypeAdapterFactory(excluder0);
      assertSame(gsonBuilder0, gsonBuilder1);
  }

  @Test(timeout = 4000)
  public void test42()  throws Throwable  {
      Gson gson0 = new Gson();
      GsonBuilder gsonBuilder0 = gson0.newBuilder();
      GsonBuilder gsonBuilder1 = gsonBuilder0.serializeSpecialFloatingPointValues();
      assertSame(gsonBuilder0, gsonBuilder1);
  }

  @Test(timeout = 4000)
  public void test43()  throws Throwable  {
      GsonBuilder gsonBuilder0 = new GsonBuilder();
      gsonBuilder0.serializeNulls();
      Gson gson0 = gsonBuilder0.create();
      assertTrue(gson0.htmlSafe());
      assertTrue(gson0.serializeNulls());
  }

  @Test(timeout = 4000)
  public void test44()  throws Throwable  {
      GsonBuilder gsonBuilder0 = new GsonBuilder();
      GsonBuilder gsonBuilder1 = gsonBuilder0.setLenient();
      assertSame(gsonBuilder1, gsonBuilder0);
  }

  @Test(timeout = 4000)
  public void test45()  throws Throwable  {
      GsonBuilder gsonBuilder0 = new GsonBuilder();
      GsonBuilder gsonBuilder1 = gsonBuilder0.excludeFieldsWithoutExposeAnnotation();
      assertSame(gsonBuilder0, gsonBuilder1);
  }

  @Test(timeout = 4000)
  public void test46()  throws Throwable  {
      GsonBuilder gsonBuilder0 = new GsonBuilder();
      GsonBuilder gsonBuilder1 = gsonBuilder0.setPrettyPrinting();
      assertSame(gsonBuilder0, gsonBuilder1);
  }

  @Test(timeout = 4000)
  public void test47()  throws Throwable  {
      GsonBuilder gsonBuilder0 = new GsonBuilder();
      GsonBuilder gsonBuilder1 = gsonBuilder0.enableComplexMapKeySerialization();
      assertSame(gsonBuilder1, gsonBuilder0);
  }

  @Test(timeout = 4000)
  public void test48()  throws Throwable  {
      Gson gson0 = new Gson();
      GsonBuilder gsonBuilder0 = new GsonBuilder(gson0);
      GsonBuilder gsonBuilder1 = gsonBuilder0.disableJdkUnsafe();
      assertSame(gsonBuilder0, gsonBuilder1);
  }
}
