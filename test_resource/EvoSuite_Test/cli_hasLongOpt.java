/*
 * This file was automatically generated by EvoSuite
 * Wed Jun 18 07:52:12 GMT 2025
 */

package org.apache.commons.cli;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import org.apache.commons.cli.Option;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true) 
public class Option_ESTest extends Option_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder();
      Option.Builder option_Builder1 = option_Builder0.longOpt("");
      option_Builder1.numberOfArgs((-1453));
      Option option0 = option_Builder1.build();
      boolean boolean0 = option0.requiresArg();
      assertFalse(boolean0);
      assertEquals((-1453), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      Option option0 = new Option("", "", true, "");
      option0.setArgs(0);
      boolean boolean0 = option0.acceptsArg();
      assertEquals(0, option0.getArgs());
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder("");
      Option.Builder option_Builder1 = option_Builder0.option("");
      assertSame(option_Builder0, option_Builder1);
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder();
      Option.Builder option_Builder1 = option_Builder0.required(true);
      assertSame(option_Builder1, option_Builder0);
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      Option option0 = new Option("", true, "");
      option0.setOptionalArg(true);
      option0.hasOptionalArg();
      assertFalse(option0.hasLongOpt());
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      Option option0 = new Option("", "7h`", true, "");
      option0.addValueForProcessing("");
      option0.getValuesList();
      assertFalse(option0.hasValueSeparator());
      assertEquals("", option0.getDescription());
      assertEquals("7h`", option0.getLongOpt());
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      Option option0 = new Option((String) null, (String) null, false, (String) null);
      option0.setValueSeparator('h');
      char char0 = option0.getValueSeparator();
      assertEquals('h', char0);
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      Option option0 = new Option("", true, "");
      String string0 = option0.getValue("?");
      assertNotNull(string0);
      assertFalse(option0.hasLongOpt());
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      Option option0 = new Option("", "7h`", true, "");
      option0.addValueForProcessing("");
      String string0 = option0.getValue();
      assertEquals("7h`", option0.getLongOpt());
      assertEquals("", option0.getDescription());
      assertNotNull(string0);
      assertFalse(option0.hasValueSeparator());
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      Option option0 = new Option((String) null, (String) null, false, (String) null);
      option0.getOpt();
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      Option option0 = new Option("EN", "EN", false, "EN");
      option0.getOpt();
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      Option option0 = new Option("", "7h`", true, "");
      String string0 = option0.getLongOpt();
      assertEquals("", option0.getDescription());
      assertEquals("7h`", string0);
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      Option option0 = new Option("", "", false, "");
      option0.getLongOpt();
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      Option option0 = new Option("Y", "Y");
      option0.getKey();
      assertEquals((-1), option0.getArgs());
      assertFalse(option0.hasLongOpt());
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      Option option0 = new Option("", true, "=N~d,n");
      option0.getKey();
      assertFalse(option0.hasLongOpt());
      assertEquals("=N~d,n", option0.getDescription());
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder();
      option_Builder0.longOpt("-/%rX-");
      Option option0 = option_Builder0.build();
      option0.getDescription();
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      Option option0 = new Option("Y", "Y");
      String string0 = option0.getDescription();
      assertEquals((-1), option0.getArgs());
      assertFalse(option0.hasLongOpt());
      assertNotNull(string0);
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      Option option0 = new Option("", "", true, "R");
      option0.setArgs(0);
      int int0 = option0.getArgs();
      assertFalse(option0.hasArg());
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      Option option0 = new Option("", "7h`", true, "");
      int int0 = option0.getArgs();
      assertEquals(1, int0);
      assertEquals("7h`", option0.getLongOpt());
      assertEquals("", option0.getDescription());
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder();
      option_Builder0.longOpt("");
      Option option0 = option_Builder0.build();
      option0.setArgName(" [ARG]");
      option0.getArgName();
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      Option option0 = new Option("", "");
      option0.setArgName("");
      option0.getArgName();
      assertFalse(option0.hasLongOpt());
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder();
      Option.Builder option_Builder1 = option_Builder0.longOpt("");
      option_Builder0.hasArgs();
      Option option0 = option_Builder1.build();
      option0.acceptsArg();
      assertTrue(option0.hasArg());
      assertTrue(option0.hasArgs());
  }

  @Test(timeout = 4000)
  public void test22()  throws Throwable  {
      Option option0 = new Option("Y", "org.apache.commons.cli.Option");
      // Undeclared exception!
      try { 
        option0.setType((Object) "org.apache.commons.cli.Option");
        fail("Expecting exception: ClassCastException");
      
      } catch(ClassCastException e) {
         //
         // java.lang.String cannot be cast to java.lang.Class
         //
         verifyException("org.apache.commons.cli.Option", e);
      }
  }

  @Test(timeout = 4000)
  public void test23()  throws Throwable  {
      Option option0 = new Option("", "", true, "");
      option0.addValueForProcessing("");
      try { 
        option0.getValue(2627);
        fail("Expecting exception: IndexOutOfBoundsException");
      
      } catch(IndexOutOfBoundsException e) {
         //
         // Index: 2627, Size: 1
         //
         verifyException("java.util.ArrayList", e);
      }
  }

  @Test(timeout = 4000)
  public void test24()  throws Throwable  {
      Option option0 = new Option("", "");
      // Undeclared exception!
      try { 
        option0.getId();
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
      }
  }

  @Test(timeout = 4000)
  public void test25()  throws Throwable  {
      Option option0 = new Option((String) null, true, (String) null);
      // Undeclared exception!
      try { 
        option0.getId();
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.cli.Option", e);
      }
  }

  @Test(timeout = 4000)
  public void test26()  throws Throwable  {
      // Undeclared exception!
      try { 
        Option.builder("`L%!;q`fVJVd");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The option '`L%!;q`fVJVd' contains an illegal character : '`'
         //
         verifyException("org.apache.commons.cli.OptionValidator", e);
      }
  }

  @Test(timeout = 4000)
  public void test27()  throws Throwable  {
      Option option0 = null;
      try {
        option0 = new Option("qpLfQn\"Glv?M-gW", true, "qpLfQn\"Glv?M-gW");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The option 'qpLfQn\"Glv?M-gW' contains an illegal character : '\"'
         //
         verifyException("org.apache.commons.cli.OptionValidator", e);
      }
  }

  @Test(timeout = 4000)
  public void test28()  throws Throwable  {
      Option option0 = null;
      try {
        option0 = new Option("org.apache.commons.cli.Option", "org.apache.commons.cli.Option", true, "org.apache.commons.cli.Option");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The option 'org.apache.commons.cli.Option' contains an illegal character : '.'
         //
         verifyException("org.apache.commons.cli.OptionValidator", e);
      }
  }

  @Test(timeout = 4000)
  public void test29()  throws Throwable  {
      Option option0 = null;
      try {
        option0 = new Option("-", "-");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Illegal option name '-'
         //
         verifyException("org.apache.commons.cli.OptionValidator", e);
      }
  }

  @Test(timeout = 4000)
  public void test30()  throws Throwable  {
      Option option0 = new Option("", true, "");
      boolean boolean0 = option0.hasValueSeparator();
      assertFalse(option0.hasLongOpt());
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test31()  throws Throwable  {
      Option option0 = new Option("EN", "EN", false, "EN");
      option0.setValueSeparator('W');
      boolean boolean0 = option0.hasValueSeparator();
      assertEquals('W', option0.getValueSeparator());
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test32()  throws Throwable  {
      Option option0 = new Option("", "", false, "hioZS{=@E");
      option0.setArgs(1287);
      boolean boolean0 = option0.hasArgs();
      assertEquals(1287, option0.getArgs());
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test33()  throws Throwable  {
      Option option0 = new Option("Y", "Y");
      boolean boolean0 = option0.hasArgs();
      assertFalse(option0.hasLongOpt());
      assertEquals((-1), option0.getArgs());
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test34()  throws Throwable  {
      Option option0 = new Option("Y", "Y");
      boolean boolean0 = option0.hasArg();
      assertEquals((-1), option0.getArgs());
      assertFalse(option0.hasLongOpt());
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test35()  throws Throwable  {
      Option option0 = new Option("q", true, "q");
      option0.hasArg();
      assertFalse(option0.hasLongOpt());
  }

  @Test(timeout = 4000)
  public void test36()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder();
      option_Builder0.longOpt("");
      option_Builder0.hasArgs();
      Option option0 = option_Builder0.build();
      boolean boolean0 = option0.hasArg();
      assertTrue(boolean0);
      assertEquals((-2), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test37()  throws Throwable  {
      Option option0 = new Option((String) null, false, (String) null);
      option0.getValueSeparator();
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test38()  throws Throwable  {
      Option option0 = new Option("", true, "");
      option0.hasOptionalArg();
      assertFalse(option0.hasLongOpt());
  }

  @Test(timeout = 4000)
  public void test39()  throws Throwable  {
      Option option0 = new Option("", " :: ", true, "");
      Class<Option> class0 = Option.class;
      option0.setType(class0);
      assertEquals(" :: ", option0.getLongOpt());
      assertEquals("", option0.getDescription());
  }

  @Test(timeout = 4000)
  public void test40()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder();
      option_Builder0.longOpt("");
      Option.Builder option_Builder1 = option_Builder0.optionalArg(true);
      Option option0 = option_Builder1.build();
      String string0 = option0.toString();
      assertEquals("[ option: null   [ARG] :: null :: class java.lang.String ]", string0);
      assertTrue(option0.hasOptionalArg());
  }

  @Test(timeout = 4000)
  public void test41()  throws Throwable  {
      Option option0 = new Option((String) null, (String) null);
      String string0 = option0.toString();
      assertEquals("[ option: null  :: null :: class java.lang.String ]", string0);
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test42()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder();
      Option.Builder option_Builder1 = option_Builder0.longOpt("Y");
      option_Builder0.hasArgs();
      Option option0 = option_Builder1.build();
      option0.addValueForProcessing("[ARG...]");
      boolean boolean0 = option0.requiresArg();
      assertFalse(option0.hasValueSeparator());
      assertFalse(boolean0);
      assertTrue(option0.hasArg());
      assertTrue(option0.hasArgs());
  }

  @Test(timeout = 4000)
  public void test43()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder();
      Option.Builder option_Builder1 = option_Builder0.longOpt("Y");
      option_Builder0.hasArgs();
      Option option0 = option_Builder1.build();
      boolean boolean0 = option0.requiresArg();
      assertTrue(boolean0);
      assertTrue(option0.hasArgs());
  }

  @Test(timeout = 4000)
  public void test44()  throws Throwable  {
      Option option0 = new Option("Y", "Y");
      option0.setOptionalArg(true);
      boolean boolean0 = option0.requiresArg();
      assertFalse(boolean0);
      assertEquals((-1), option0.getArgs());
      assertFalse(option0.hasLongOpt());
  }

  @Test(timeout = 4000)
  public void test45()  throws Throwable  {
      Option option0 = new Option("Y", "Y");
      boolean boolean0 = option0.requiresArg();
      assertFalse(boolean0);
      assertFalse(option0.hasLongOpt());
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test46()  throws Throwable  {
      Option option0 = new Option("", "", true, "");
      boolean boolean0 = option0.hasLongOpt();
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test47()  throws Throwable  {
      Option option0 = new Option((String) null, (String) null);
      boolean boolean0 = option0.hasLongOpt();
      assertFalse(boolean0);
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test48()  throws Throwable  {
      Option option0 = new Option("Y", "Y");
      assertFalse(option0.hasArg());
      
      option0.setArgs((-2));
      boolean boolean0 = option0.hasArgs();
      assertEquals((-2), option0.getArgs());
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test49()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder("");
      option_Builder0.argName("");
      Option option0 = option_Builder0.build();
      boolean boolean0 = option0.hasArgName();
      assertFalse(boolean0);
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test50()  throws Throwable  {
      Option option0 = new Option("Y", "Y");
      boolean boolean0 = option0.hasArgName();
      assertFalse(boolean0);
      assertEquals((-1), option0.getArgs());
      assertFalse(option0.hasLongOpt());
  }

  @Test(timeout = 4000)
  public void test51()  throws Throwable  {
      Option option0 = new Option("GCiDYY", "GCiDYY");
      String[] stringArray0 = option0.getValues();
      assertNull(stringArray0);
      assertEquals((-1), option0.getArgs());
      assertFalse(option0.hasLongOpt());
  }

  @Test(timeout = 4000)
  public void test52()  throws Throwable  {
      Option option0 = new Option("Y", "Y");
      option0.setOptionalArg(true);
      option0.setArgs((-1453));
      option0.addValueForProcessing("s-`h -\"");
      option0.getValues();
      assertEquals((-1453), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test53()  throws Throwable  {
      Option option0 = new Option("", "7h`", true, "");
      assertTrue(option0.hasArg());
      
      option0.addValueForProcessing("");
      option0.getValue("");
      assertEquals("7h`", option0.getLongOpt());
      assertEquals("", option0.getDescription());
      assertFalse(option0.hasValueSeparator());
  }

  @Test(timeout = 4000)
  public void test54()  throws Throwable  {
      Option option0 = new Option((String) null, (String) null, false, (String) null);
      option0.getValue((String) null);
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test55()  throws Throwable  {
      Option option0 = new Option("", true, "=N~d,n");
      option0.getValue((-415));
      assertEquals("=N~d,n", option0.getDescription());
      assertFalse(option0.hasLongOpt());
      assertTrue(option0.hasArg());
  }

  @Test(timeout = 4000)
  public void test56()  throws Throwable  {
      Option option0 = new Option("", "", true, "");
      option0.addValueForProcessing("");
      try { 
        option0.getValue((-2));
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test57()  throws Throwable  {
      Option option0 = new Option((String) null, (String) null);
      option0.getValue();
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test58()  throws Throwable  {
      Option option0 = new Option((String) null, (String) null);
      option0.getKey();
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test59()  throws Throwable  {
      Option option0 = new Option("Y", "Y");
      Option.Builder option_Builder0 = Option.builder("Y");
      Option option1 = option_Builder0.build();
      boolean boolean0 = option1.equals(option0);
      assertEquals((-1), option1.getArgs());
      assertEquals((-1), option0.getArgs());
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test60()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder();
      Option.Builder option_Builder1 = option_Builder0.longOpt("Y");
      Option option0 = option_Builder1.build();
      Option option1 = new Option("Y", "Y", false, "\"");
      boolean boolean0 = option1.equals(option0);
      assertEquals((-1), option1.getArgs());
      assertEquals((-1), option0.getArgs());
      assertFalse(boolean0);
      assertEquals("\"", option1.getDescription());
      assertEquals("Y", option1.getLongOpt());
  }

  @Test(timeout = 4000)
  public void test61()  throws Throwable  {
      Option option0 = new Option("Y", "Y");
      Option.Builder option_Builder0 = Option.builder();
      boolean boolean0 = option0.equals(option_Builder0);
      assertFalse(boolean0);
      assertFalse(option0.hasLongOpt());
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test62()  throws Throwable  {
      Option option0 = new Option((String) null, (String) null);
      boolean boolean0 = option0.equals(option0);
      assertTrue(boolean0);
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test63()  throws Throwable  {
      Option option0 = new Option("Y", "Y");
      Option option1 = new Option("Y", "Y", false, "\"");
      boolean boolean0 = option1.equals(option0);
      assertEquals("Y", option1.getLongOpt());
      assertEquals((-1), option1.getArgs());
      assertFalse(boolean0);
      assertEquals("\"", option1.getDescription());
  }

  @Test(timeout = 4000)
  public void test64()  throws Throwable  {
      Option option0 = new Option("", "");
      // Undeclared exception!
      try { 
        option0.addValueForProcessing("");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // NO_ARGS_ALLOWED
         //
         verifyException("org.apache.commons.cli.Option", e);
      }
  }

  @Test(timeout = 4000)
  public void test65()  throws Throwable  {
      Option option0 = new Option("", "", true, "");
      option0.addValueForProcessing("");
      // Undeclared exception!
      try { 
        option0.addValueForProcessing("");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot add value, list full.
         //
         verifyException("org.apache.commons.cli.Option", e);
      }
  }

  @Test(timeout = 4000)
  public void test66()  throws Throwable  {
      Option option0 = new Option("Y", "Y");
      option0.setOptionalArg(true);
      option0.setArgs((-1453));
      option0.addValueForProcessing("s-`h -\"");
      option0.getValue();
      assertEquals((-1453), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test67()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder();
      Option.Builder option_Builder1 = option_Builder0.optionalArg(true);
      option_Builder1.longOpt("");
      option_Builder0.valueSeparator('R');
      Option option0 = option_Builder0.build();
      // Undeclared exception!
      try { 
        option0.addValueForProcessing((String) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.cli.Option", e);
      }
  }

  @Test(timeout = 4000)
  public void test68()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder();
      Option.Builder option_Builder1 = option_Builder0.optionalArg(false);
      assertSame(option_Builder1, option_Builder0);
  }

  @Test(timeout = 4000)
  public void test69()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder();
      Option.Builder option_Builder1 = option_Builder0.hasArg(false);
      assertSame(option_Builder1, option_Builder0);
  }

  @Test(timeout = 4000)
  public void test70()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder();
      // Undeclared exception!
      try { 
        option_Builder0.build();
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Either opt or longOpt must be specified
         //
         verifyException("org.apache.commons.cli.Option$Builder", e);
      }
  }

  @Test(timeout = 4000)
  public void test71()  throws Throwable  {
      Option option0 = new Option("", " :: ", true, "");
      assertEquals(" :: ", option0.getLongOpt());
      
      option0.setLongOpt((String) null);
      assertEquals("", option0.getDescription());
      assertEquals(1, option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test72()  throws Throwable  {
      Option option0 = new Option("", true, "");
      option0.getOpt();
      assertFalse(option0.hasLongOpt());
      assertEquals(1, option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test73()  throws Throwable  {
      Option option0 = new Option((String) null, (String) null);
      option0.getType();
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test74()  throws Throwable  {
      Option option0 = new Option("", true, "=N~d,n");
      // Undeclared exception!
      try { 
        option0.addValue("");
        fail("Expecting exception: UnsupportedOperationException");
      
      } catch(UnsupportedOperationException e) {
         //
         // The addValue method is not intended for client use. Subclasses should use the addValueForProcessing method instead. 
         //
         verifyException("org.apache.commons.cli.Option", e);
      }
  }

  @Test(timeout = 4000)
  public void test75()  throws Throwable  {
      Option option0 = new Option((String) null, (String) null);
      option0.getLongOpt();
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test76()  throws Throwable  {
      Option option0 = new Option("Y", "Y");
      option0.hashCode();
      assertFalse(option0.hasLongOpt());
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test77()  throws Throwable  {
      Option option0 = new Option("Y", "Y");
      option0.isRequired();
      assertFalse(option0.hasLongOpt());
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test78()  throws Throwable  {
      Option option0 = new Option("", "7h`", true, "");
      String string0 = option0.getDescription();
      assertEquals("", string0);
      assertEquals("7h`", option0.getLongOpt());
      assertEquals(1, option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test79()  throws Throwable  {
      Option option0 = new Option("Y", "Y");
      int int0 = option0.getArgs();
      assertFalse(option0.hasLongOpt());
      assertEquals((-1), int0);
  }

  @Test(timeout = 4000)
  public void test80()  throws Throwable  {
      Option option0 = new Option("Y", "Y");
      option0.clearValues();
      assertFalse(option0.hasLongOpt());
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test81()  throws Throwable  {
      Option option0 = new Option("Y", "Y");
      option0.setDescription("Y");
      assertEquals((-1), option0.getArgs());
      assertFalse(option0.hasLongOpt());
  }

  @Test(timeout = 4000)
  public void test82()  throws Throwable  {
      Option option0 = new Option("", true, "");
      Option option1 = (Option)option0.clone();
      assertEquals(1, option1.getArgs());
      assertFalse(option1.hasLongOpt());
      assertNotSame(option1, option0);
  }

  @Test(timeout = 4000)
  public void test83()  throws Throwable  {
      Option option0 = new Option("Y", "Y");
      option0.getId();
      assertFalse(option0.hasLongOpt());
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test84()  throws Throwable  {
      Option option0 = new Option("", "7h`", true, "");
      option0.getValuesList();
      assertEquals(1, option0.getArgs());
      assertEquals("", option0.getDescription());
      assertEquals("7h`", option0.getLongOpt());
  }

  @Test(timeout = 4000)
  public void test85()  throws Throwable  {
      Option option0 = new Option("", "7h`", true, "");
      option0.setArgs(2708);
      option0.toString();
      assertEquals(2708, option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test86()  throws Throwable  {
      Option option0 = new Option((String) null, (String) null, true, "Either opt or longOpt must be specified");
      option0.setRequired(true);
      assertTrue(option0.isRequired());
  }

  @Test(timeout = 4000)
  public void test87()  throws Throwable  {
      Option option0 = new Option("", "", true, "");
      option0.getArgName();
      assertTrue(option0.hasArg());
  }

  @Test(timeout = 4000)
  public void test88()  throws Throwable  {
      Option option0 = new Option((String) null, (String) null);
      option0.setType((Object) null);
      option0.getType();
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test89()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder();
      Class<Object> class0 = Object.class;
      Option.Builder option_Builder1 = option_Builder0.type(class0);
      assertSame(option_Builder0, option_Builder1);
  }

  @Test(timeout = 4000)
  public void test90()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder("");
      Option.Builder option_Builder1 = option_Builder0.hasArg();
      assertSame(option_Builder0, option_Builder1);
  }

  @Test(timeout = 4000)
  public void test91()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder((String) null);
      Option.Builder option_Builder1 = option_Builder0.desc("");
      assertSame(option_Builder0, option_Builder1);
  }

  @Test(timeout = 4000)
  public void test92()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder();
      Option.Builder option_Builder1 = option_Builder0.required();
      option_Builder1.longOpt("");
      Option option0 = option_Builder0.build();
      boolean boolean0 = option0.isRequired();
      assertTrue(boolean0);
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test93()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder();
      option_Builder0.argName("Y");
      Option.Builder option_Builder1 = option_Builder0.longOpt("Y");
      Option option0 = option_Builder1.build();
      boolean boolean0 = option0.hasArgName();
      assertTrue(boolean0);
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test94()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder();
      Option.Builder option_Builder1 = option_Builder0.longOpt("Y");
      option_Builder0.hasArgs();
      option_Builder0.valueSeparator('R');
      Option option0 = option_Builder1.build();
      option0.addValueForProcessing("[ARG...]");
      assertTrue(option0.hasArg());
      assertEquals("[A", option0.getValue());
  }

  @Test(timeout = 4000)
  public void test95()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder();
      Option.Builder option_Builder1 = option_Builder0.valueSeparator();
      assertSame(option_Builder0, option_Builder1);
  }
}
