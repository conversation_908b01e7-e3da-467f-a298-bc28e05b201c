/*
 * This file was automatically generated by EvoSuite
 * Wed Jun 18 08:22:54 GMT 2025
 */

package org.apache.commons.csv;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.io.PipedReader;
import java.util.HashMap;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true) 
public class CSVRecord_ESTest extends CSVRecord_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      PipedReader pipedReader0 = new PipedReader(239);
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVParser cSVParser0 = new CSVParser(pipedReader0, cSVFormat0, 239, 239);
      String[] stringArray0 = new String[0];
      CSVRecord cSVRecord0 = new CSVRecord(cSVParser0, stringArray0, (String) null, (-2174L), (-2174L));
      boolean boolean0 = cSVRecord0.isSet(80);
      assertEquals((-2174L), cSVRecord0.getRecordNumber());
      assertFalse(boolean0);
      assertEquals((-2174L), cSVRecord0.getCharacterPosition());
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_CSV;
      CSVParser cSVParser0 = CSVParser.parse("f}i3Ep", cSVFormat0);
      CSVRecord cSVRecord0 = cSVParser0.nextRecord();
      cSVRecord0.values();
      assertEquals(0L, cSVRecord0.getCharacterPosition());
      assertEquals(1L, cSVRecord0.getRecordNumber());
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      PipedReader pipedReader0 = new PipedReader(239);
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVParser cSVParser0 = new CSVParser(pipedReader0, cSVFormat0, 239, 239);
      String[] stringArray0 = new String[0];
      CSVRecord cSVRecord0 = new CSVRecord(cSVParser0, stringArray0, (String) null, (-2174L), (-2174L));
      cSVRecord0.toList();
      assertEquals((-2174L), cSVRecord0.getCharacterPosition());
      assertEquals((-2174L), cSVRecord0.getRecordNumber());
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      String[] stringArray0 = new String[2];
      CSVRecord cSVRecord0 = new CSVRecord((CSVParser) null, stringArray0, stringArray0[0], (-708L), (-708L));
      cSVRecord0.size();
      assertEquals((-708L), cSVRecord0.getCharacterPosition());
      assertEquals((-708L), cSVRecord0.getRecordNumber());
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      PipedReader pipedReader0 = new PipedReader();
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVParser cSVParser0 = new CSVParser(pipedReader0, cSVFormat0, 239, 239);
      String[] stringArray0 = new String[0];
      CSVRecord cSVRecord0 = new CSVRecord(cSVParser0, stringArray0, "tuKp6", 1240L, 239);
      cSVRecord0.putIn((HashMap<String, String>) null);
      assertEquals(1240L, cSVRecord0.getRecordNumber());
      assertEquals(239L, cSVRecord0.getCharacterPosition());
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      PipedReader pipedReader0 = new PipedReader(239);
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVParser cSVParser0 = new CSVParser(pipedReader0, cSVFormat0, 239, 239);
      String[] stringArray0 = new String[0];
      CSVRecord cSVRecord0 = new CSVRecord(cSVParser0, stringArray0, "", 239, 239);
      HashMap<String, String> hashMap0 = new HashMap<String, String>();
      hashMap0.put("0RUd4'\".>k", "r/Zd/kNd*");
      cSVRecord0.putIn(hashMap0);
      assertEquals(239L, cSVRecord0.getRecordNumber());
      assertEquals(239L, cSVRecord0.getCharacterPosition());
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      String[] stringArray0 = new String[6];
      CSVRecord cSVRecord0 = new CSVRecord((CSVParser) null, stringArray0, "", 0L, 0L);
      long long0 = cSVRecord0.getRecordNumber();
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      PipedReader pipedReader0 = new PipedReader(239);
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVParser cSVParser0 = new CSVParser(pipedReader0, cSVFormat0, 239, 239);
      String[] stringArray0 = new String[0];
      CSVRecord cSVRecord0 = new CSVRecord(cSVParser0, stringArray0, (String) null, (-2174L), (-2174L));
      long long0 = cSVRecord0.getRecordNumber();
      assertEquals((-2174L), long0);
      assertEquals((-2174L), cSVRecord0.getCharacterPosition());
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      PipedReader pipedReader0 = new PipedReader(239);
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVParser cSVParser0 = new CSVParser(pipedReader0, cSVFormat0, 239, 239);
      String[] stringArray0 = new String[0];
      CSVRecord cSVRecord0 = new CSVRecord(cSVParser0, stringArray0, "", 239, 239);
      cSVRecord0.getParser();
      assertEquals(239L, cSVRecord0.getCharacterPosition());
      assertEquals(239L, cSVRecord0.getRecordNumber());
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      PipedReader pipedReader0 = new PipedReader();
      CSVParser cSVParser0 = new CSVParser(pipedReader0, cSVFormat0, 239, 0L);
      String[] stringArray0 = new String[2];
      CSVRecord cSVRecord0 = new CSVRecord(cSVParser0, stringArray0, "", 1L, 1052L);
      cSVRecord0.getParser();
      assertEquals(1L, cSVRecord0.getRecordNumber());
      assertEquals(1052L, cSVRecord0.getCharacterPosition());
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_CSV;
      CSVParser cSVParser0 = CSVParser.parse("QuoteMode=<", cSVFormat0);
      CSVRecord cSVRecord0 = cSVParser0.nextRecord();
      cSVRecord0.getComment();
      assertEquals(1L, cSVRecord0.getRecordNumber());
      assertEquals(0L, cSVRecord0.getCharacterPosition());
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      PipedReader pipedReader0 = new PipedReader(239);
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVParser cSVParser0 = new CSVParser(pipedReader0, cSVFormat0, 239, 239);
      String[] stringArray0 = new String[0];
      CSVRecord cSVRecord0 = new CSVRecord(cSVParser0, stringArray0, "No header mapping was specified, the record values can't be accessed by name", (-6L), (-6L));
      cSVRecord0.getComment();
      assertEquals((-6L), cSVRecord0.getCharacterPosition());
      assertEquals((-6L), cSVRecord0.getRecordNumber());
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_CSV;
      CSVParser cSVParser0 = CSVParser.parse("QuoteMode=<", cSVFormat0);
      CSVRecord cSVRecord0 = cSVParser0.nextRecord();
      long long0 = cSVRecord0.getCharacterPosition();
      assertEquals(1L, cSVRecord0.getRecordNumber());
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      PipedReader pipedReader0 = new PipedReader();
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVParser cSVParser0 = new CSVParser(pipedReader0, cSVFormat0, 1395L, 0L);
      String[] stringArray0 = new String[4];
      stringArray0[3] = "";
      CSVRecord cSVRecord0 = new CSVRecord(cSVParser0, stringArray0, "ln/N'", (-617L), 0L);
      String string0 = cSVRecord0.get(3);
      assertEquals((-617L), cSVRecord0.getRecordNumber());
      assertEquals(0L, cSVRecord0.getCharacterPosition());
      assertNotNull(string0);
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      CSVParser cSVParser0 = CSVParser.parse("f}iEp", cSVFormat0);
      CSVRecord cSVRecord0 = cSVParser0.nextRecord();
      // Undeclared exception!
      try { 
        cSVRecord0.get(1807);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 1807
         //
         verifyException("org.apache.commons.csv.CSVRecord", e);
      }
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      String[] stringArray0 = new String[2];
      CSVRecord cSVRecord0 = new CSVRecord((CSVParser) null, stringArray0, stringArray0[0], (-708L), (-708L));
      cSVRecord0.isMapped("[");
      assertEquals((-708L), cSVRecord0.getCharacterPosition());
      assertEquals((-708L), cSVRecord0.getRecordNumber());
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      PipedReader pipedReader0 = new PipedReader();
      CSVParser cSVParser0 = new CSVParser(pipedReader0, cSVFormat0, 239, 0L);
      String[] stringArray0 = new String[2];
      CSVRecord cSVRecord0 = new CSVRecord(cSVParser0, stringArray0, "", 1L, 1052L);
      cSVRecord0.stream();
      assertEquals(1L, cSVRecord0.getRecordNumber());
      assertEquals(1052L, cSVRecord0.getCharacterPosition());
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      PipedReader pipedReader0 = new PipedReader();
      CSVParser cSVParser0 = new CSVParser(pipedReader0, cSVFormat0, 239, 0L);
      String[] stringArray0 = new String[2];
      CSVRecord cSVRecord0 = new CSVRecord(cSVParser0, stringArray0, "", 1L, 1052L);
      cSVRecord0.toList();
      assertEquals(1052L, cSVRecord0.getCharacterPosition());
      assertEquals(1L, cSVRecord0.getRecordNumber());
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      PipedReader pipedReader0 = new PipedReader(239);
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVParser cSVParser0 = new CSVParser(pipedReader0, cSVFormat0, (-1L), 239);
      String[] stringArray0 = new String[1];
      CSVRecord cSVRecord0 = new CSVRecord(cSVParser0, stringArray0, (String) null, (-2669L), (-1L));
      boolean boolean0 = cSVRecord0.isSet(0);
      assertEquals((-1L), cSVRecord0.getCharacterPosition());
      assertEquals((-2669L), cSVRecord0.getRecordNumber());
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      PipedReader pipedReader0 = new PipedReader();
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVParser cSVParser0 = new CSVParser(pipedReader0, cSVFormat0, 239, 239);
      String[] stringArray0 = new String[0];
      CSVRecord cSVRecord0 = new CSVRecord(cSVParser0, stringArray0, "", 239, 239);
      boolean boolean0 = cSVRecord0.isSet(0);
      assertEquals(239L, cSVRecord0.getCharacterPosition());
      assertEquals(239L, cSVRecord0.getRecordNumber());
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      PipedReader pipedReader0 = new PipedReader(239);
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVParser cSVParser0 = new CSVParser(pipedReader0, cSVFormat0, 239, 239);
      String[] stringArray0 = new String[0];
      CSVRecord cSVRecord0 = new CSVRecord(cSVParser0, stringArray0, "Ov]4:|;@Gl,T]6c*X1u", 239, 239);
      boolean boolean0 = cSVRecord0.isSet((-2262));
      assertEquals(239L, cSVRecord0.getRecordNumber());
      assertFalse(boolean0);
      assertEquals(239L, cSVRecord0.getCharacterPosition());
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      PipedReader pipedReader0 = new PipedReader();
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVParser cSVParser0 = new CSVParser(pipedReader0, cSVFormat0, 1395L, 0L);
      String[] stringArray0 = new String[4];
      CSVRecord cSVRecord0 = new CSVRecord(cSVParser0, stringArray0, "ln/N'", (-617L), 0L);
      boolean boolean0 = cSVRecord0.isSet("");
      assertEquals((-617L), cSVRecord0.getRecordNumber());
      assertFalse(boolean0);
      assertEquals(0L, cSVRecord0.getCharacterPosition());
  }

  @Test(timeout = 4000)
  public void test22()  throws Throwable  {
      PipedReader pipedReader0 = new PipedReader();
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVParser cSVParser0 = new CSVParser(pipedReader0, cSVFormat0, 1395L, 0L);
      String[] stringArray0 = new String[4];
      CSVRecord cSVRecord0 = new CSVRecord(cSVParser0, stringArray0, "ln/N'", (-617L), 0L);
      boolean boolean0 = cSVRecord0.isConsistent();
      assertEquals((-617L), cSVRecord0.getRecordNumber());
      assertTrue(boolean0);
      assertEquals(0L, cSVRecord0.getCharacterPosition());
  }

  @Test(timeout = 4000)
  public void test23()  throws Throwable  {
      String[] stringArray0 = new String[7];
      CSVRecord cSVRecord0 = new CSVRecord((CSVParser) null, stringArray0, "", 1L, 1L);
      boolean boolean0 = cSVRecord0.hasComment();
      assertEquals(1L, cSVRecord0.getCharacterPosition());
      assertTrue(boolean0);
      assertEquals(1L, cSVRecord0.getRecordNumber());
  }

  @Test(timeout = 4000)
  public void test24()  throws Throwable  {
      String[] stringArray0 = new String[2];
      CSVRecord cSVRecord0 = new CSVRecord((CSVParser) null, stringArray0, stringArray0[0], 1L, 1L);
      boolean boolean0 = cSVRecord0.hasComment();
      assertEquals(1L, cSVRecord0.getCharacterPosition());
      assertFalse(boolean0);
      assertEquals(1L, cSVRecord0.getRecordNumber());
  }

  @Test(timeout = 4000)
  public void test25()  throws Throwable  {
      String[] stringArray0 = new String[2];
      CSVRecord cSVRecord0 = new CSVRecord((CSVParser) null, stringArray0, stringArray0[0], 1L, 1L);
      HashMap<String, String> hashMap0 = new HashMap<String, String>();
      cSVRecord0.putIn(hashMap0);
      assertEquals(1L, cSVRecord0.getRecordNumber());
      assertEquals(1L, cSVRecord0.getCharacterPosition());
  }

  @Test(timeout = 4000)
  public void test26()  throws Throwable  {
      PipedReader pipedReader0 = new PipedReader();
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVParser cSVParser0 = new CSVParser(pipedReader0, cSVFormat0, 1395L, 0L);
      String[] stringArray0 = new String[4];
      CSVRecord cSVRecord0 = new CSVRecord(cSVParser0, stringArray0, "ln/N'", (-617L), 0L);
      // Undeclared exception!
      try { 
        cSVRecord0.get("");
        fail("Expecting exception: IllegalStateException");
      
      } catch(IllegalStateException e) {
         //
         // No header mapping was specified, the record values can't be accessed by name
         //
         verifyException("org.apache.commons.csv.CSVRecord", e);
      }
  }

  @Test(timeout = 4000)
  public void test27()  throws Throwable  {
      PipedReader pipedReader0 = new PipedReader(239);
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVParser cSVParser0 = new CSVParser(pipedReader0, cSVFormat0, 239, 239);
      String[] stringArray0 = new String[0];
      CSVRecord cSVRecord0 = new CSVRecord(cSVParser0, stringArray0, "", 239, 239);
      cSVRecord0.size();
      assertEquals(239L, cSVRecord0.getCharacterPosition());
      assertEquals(239L, cSVRecord0.getRecordNumber());
  }

  @Test(timeout = 4000)
  public void test28()  throws Throwable  {
      PipedReader pipedReader0 = new PipedReader(239);
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVParser cSVParser0 = new CSVParser(pipedReader0, cSVFormat0, 239, 239);
      String[] stringArray0 = new String[0];
      CSVRecord cSVRecord0 = new CSVRecord(cSVParser0, stringArray0, "Ov]4:|;@Gl,T]6c*X1u", 239, 239);
      long long0 = cSVRecord0.getRecordNumber();
      assertEquals(239L, long0);
      assertEquals(239L, cSVRecord0.getCharacterPosition());
  }

  @Test(timeout = 4000)
  public void test29()  throws Throwable  {
      String[] stringArray0 = new String[2];
      CSVRecord cSVRecord0 = new CSVRecord((CSVParser) null, stringArray0, stringArray0[0], 1L, 1L);
      cSVRecord0.getParser();
      assertEquals(1L, cSVRecord0.getRecordNumber());
      assertEquals(1L, cSVRecord0.getCharacterPosition());
  }

  @Test(timeout = 4000)
  public void test30()  throws Throwable  {
      PipedReader pipedReader0 = new PipedReader();
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVParser cSVParser0 = new CSVParser(pipedReader0, cSVFormat0, 1395L, 0L);
      String[] stringArray0 = new String[4];
      CSVRecord cSVRecord0 = new CSVRecord(cSVParser0, stringArray0, "ln/N'", (-617L), 0L);
      assertEquals(4, cSVRecord0.size());
      
      cSVRecord0.get(3);
      assertEquals((-617L), cSVRecord0.getRecordNumber());
      assertEquals(0L, cSVRecord0.getCharacterPosition());
  }

  @Test(timeout = 4000)
  public void test31()  throws Throwable  {
      CSVRecord cSVRecord0 = new CSVRecord((CSVParser) null, (String[]) null, "Mapping for %s not found, expected one of %s", 0L, 1L);
      String[] stringArray0 = cSVRecord0.values();
      assertEquals(1L, cSVRecord0.getCharacterPosition());
      assertNotNull(stringArray0);
      assertEquals(0L, cSVRecord0.getRecordNumber());
  }

  @Test(timeout = 4000)
  public void test32()  throws Throwable  {
      PipedReader pipedReader0 = new PipedReader();
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVParser cSVParser0 = new CSVParser(pipedReader0, cSVFormat0, 1395L, 0L);
      String[] stringArray0 = new String[4];
      CSVRecord cSVRecord0 = new CSVRecord(cSVParser0, stringArray0, "ln/N'", (-617L), 0L);
      String string0 = cSVRecord0.toString();
      assertEquals(0L, cSVRecord0.getCharacterPosition());
      assertEquals("CSVRecord [comment='ln/N'', recordNumber=-617, values=[null, null, null, null]]", string0);
  }

  @Test(timeout = 4000)
  public void test33()  throws Throwable  {
      PipedReader pipedReader0 = new PipedReader(239);
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVParser cSVParser0 = new CSVParser(pipedReader0, cSVFormat0, 239, 239);
      String[] stringArray0 = new String[0];
      CSVRecord cSVRecord0 = new CSVRecord(cSVParser0, stringArray0, "Ov]4:|;@Gl,T]6c*X1u", 239, 239);
      cSVRecord0.toMap();
      assertEquals(239L, cSVRecord0.getCharacterPosition());
      assertEquals(239L, cSVRecord0.getRecordNumber());
  }

  @Test(timeout = 4000)
  public void test34()  throws Throwable  {
      String[] stringArray0 = new String[2];
      CSVRecord cSVRecord0 = new CSVRecord((CSVParser) null, stringArray0, stringArray0[0], 1L, 1L);
      long long0 = cSVRecord0.getCharacterPosition();
      assertEquals(2, cSVRecord0.size());
      assertEquals(1L, long0);
      assertEquals(1L, cSVRecord0.getRecordNumber());
  }

  @Test(timeout = 4000)
  public void test35()  throws Throwable  {
      PipedReader pipedReader0 = new PipedReader(239);
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVParser cSVParser0 = new CSVParser(pipedReader0, cSVFormat0, 239, 239);
      String[] stringArray0 = new String[0];
      CSVRecord cSVRecord0 = new CSVRecord(cSVParser0, stringArray0, "", 239, 239);
      cSVRecord0.getComment();
      assertEquals(239L, cSVRecord0.getRecordNumber());
      assertEquals(239L, cSVRecord0.getCharacterPosition());
  }

  @Test(timeout = 4000)
  public void test36()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      CSVParser cSVParser0 = CSVParser.parse("f}iEp", cSVFormat0);
      CSVRecord cSVRecord0 = cSVParser0.nextRecord();
      cSVRecord0.iterator();
      assertEquals(1L, cSVRecord0.getRecordNumber());
      assertEquals(1, cSVRecord0.size());
      assertEquals(0L, cSVRecord0.getCharacterPosition());
  }
}
